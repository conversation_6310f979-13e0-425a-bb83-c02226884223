{"enabled": true, "name": "Code Quality Checker", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices when tests are passing", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.py", "tests/**/*.py", "*.py"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified Python code files for potential improvements. Focus on:\n\n1. Code smells and anti-patterns\n2. Design pattern opportunities\n3. Best practices violations\n4. Readability improvements\n5. Maintainability enhancements\n6. Performance optimizations\n\nFirst, run tests to ensure they pass. If tests pass, analyze the changed code and generate specific, actionable suggestions for improving code quality while maintaining existing functionality.\n\nThen create up to 3 demonstration files in ./.tmp/YYMMDD-HHSS.** format using BDD approach:\n\n1. Write BDD natural language statements describing the improvement\n2. Create guard main tests that initially fail\n3. Implement the improved code based on BDD statements\n4. Run and verify tests pass\n5. Provide reasoning for the improvements\n\nUse minimal explanatory code without exception handling, hard-coded API values, or mocks. Focus on clean, maintainable solutions that demonstrate the improvement working correctly."}}