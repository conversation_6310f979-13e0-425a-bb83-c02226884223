{"enabled": true, "name": "Documentation Sync", "description": "Monitors all Python source files and related configuration files for changes, then updates documentation in README and docs folder to keep them synchronized with the codebase", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.py", "*.py", "pyproject.toml", "requirements.txt", "justfile", ".env", "ai_docs/**/*.md", ".claude/**/*.md", ".cursor/rules/**/*.mdc", ".roo/**/*.md"]}, "then": {"type": "askAgent", "prompt": "Source code or configuration files have been modified. Please analyze the changes and update the documentation accordingly:\n\n1. Update the main README.md file to reflect any new features, modules, or changes in functionality\n2. If there is a docs/ folder, update relevant documentation files there as well\n3. Ensure all documentation paths and references are correctly listed in the README for proper indexing\n4. Update any module-specific documentation (SPEC_AF.md, IMPLEMENTATION_DOCS.md files)\n5. Verify that the project structure documentation reflects the current state\n6. Update any API documentation or usage examples if interfaces have changed\n\nFocus on keeping documentation accurate, comprehensive, and well-organized for both users and developers."}}