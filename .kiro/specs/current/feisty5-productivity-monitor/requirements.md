# Feisty5 Productivity Monitor - Requirements

## Introduction

Feisty5 is a productivity monitoring system that helps developers maintain focus on their top task through automated distraction detection and gentle voice alerts. The system uses LLM vision analysis to determine if the user is focused on their current top task from the todolist Things3 or distracted by other activities, (priamrily other tasks!) providing stern supportive nudges to maintain productivity flow.

## Requirements

### Requirement 1: Screenshot Monitoring

**User Story:** As a developer, I want the system to automatically capture screenshots of my active window every 5 seconds, so that it can monitor my current activity without manual intervention.

#### Acceptance Criteria

1. WHEN the monitoring system is active THEN it SHALL capture a screenshot of the active window every 5 seconds
2. WHEN a screenshot is captured THEN it SHALL be saved to the appropriate data directory with timestamp naming (YYYYMMDD_HHMMSS.png)
3. WHEN the active window changes THEN the system SHALL capture the new active window content
4. IF screenshot capture fails THEN the system SHALL log the error and continue monitoring

### Requirement 2: Distraction Detection

**User Story:** As a developer, I want the system to analyze my screenshots using LLM vision analysis to determine if I'm focused on my top task or distracted, so that I can receive appropriate feedback about my focus state.

#### Acceptance Criteria

1. WHEN a screenshot is captured THEN the system SHALL analyze it using GPT-5 vision analysis (configurable model via SS_MAIN_MODEL env var)
2. WHEN analyzing a screenshot THEN the system SHALL determine if the user is focused on their current top task or distracted
3. WHEN the analysis is complete THEN the system SHALL return a structured JSON response with distracted_decision boolean and reasoning string
4. IF the screen content hasn't changed significantly THEN the system SHALL skip LLM analysis to optimize costs
5. WHEN analyzing THEN the system SHALL look for concrete evidence of the top task name visible in the screenshot

### Requirement 3: Voice Alert System

**User Story:** As a developer, I want to receive gentle voice reminders when distractions are detected, so that I can be nudged back to focus without harsh interruptions.

#### Acceptance Criteria

1. WHEN distraction is detected THEN the system SHALL provide a gentle voice alert
2. WHEN generating voice alerts THEN the system SHALL use LLM to generate supportive, encouraging language based on distraction reasoning
3. WHEN voice alerts are triggered THEN the system SHALL support both macOS 'say' and OpenAI TTS backends
4. WHEN voice alerts are triggered THEN the system SHALL allow backend switching via justfile commands (voice-toggle, voice-macos, voice-openai)
5. WHEN generating voice messages THEN the system SHALL make an additional LLM API call for message generation
6. WHEN using macOS say THEN the system SHALL use 173 WPM rate (120% of default 144 WPM)

### Requirement 4: Hash Change Detection

**User Story:** As a developer, I want the system to optimize API costs by only analyzing screenshots when screen content changes significantly, so that I don't waste money on redundant analysis.

#### Acceptance Criteria

1. WHEN a new screenshot is captured THEN the system SHALL compute a perceptual hash
2. WHEN comparing screenshots THEN the system SHALL determine if content has changed significantly
3. IF the hash indicates no significant change THEN the system SHALL skip LLM analysis
4. WHEN hash change detection is active THEN the system SHALL achieve 30-40% cost savings

### Requirement 5: Things3 Integration

**User Story:** As a developer, I want the system to fetch my current top task from Things3, so that distraction detection can be contextual to what I should be working on.

#### Acceptance Criteria

1. WHEN the system starts THEN it SHALL fetch the current top task from Things3
2. WHEN analyzing screenshots THEN the system SHALL consider the current top task context from Things3
3. WHEN the top task changes THEN the system SHALL update its analysis context
4. IF Things3 integration fails THEN the system SHALL continue with generic distraction detection

### Requirement 6: Performance Optimization

**User Story:** As a developer, I want the system to run efficiently without impacting my computer's performance, so that monitoring doesn't interfere with my actual work.

#### Acceptance Criteria

1. WHEN processing screenshots THEN the system SHALL compress images before LLM analysis (configurable via SCREENSHOT_COMPRESS env var)
2. WHEN compressing images THEN the system SHALL support multiple compression methods (binarize, bilateral filtering)

4. WHEN storing data THEN the system SHALL manage disk space efficiently with configurable cleanup options
4. WHEN making API calls THEN the system SHALL implement timeout handling (60 second timeout)
5. WHEN Peekaboo screenshot capture fails THEN the system SHALL use screencapture fallback and validate file sizes (>5KB)

### Requirement 7: Session Management

**User Story:** As a developer, I want to run monitoring sessions for specific durations and track detailed analytics, so that I can understand my focus patterns and system performance.

#### Acceptance Criteria

1. WHEN starting the system THEN it SHALL prompt for monitoring duration in minutes
2. WHEN starting the system THEN it SHALL accept command line arguments for duration (e.g., `python main.py 0.5` for 30 seconds)
3. WHEN a session starts THEN it SHALL create a timestamped JSON log file in data/logs/
4. WHEN each cycle completes THEN it SHALL log cycle data including timestamps, analysis results, and performance metrics
5. WHEN the session ends THEN it SHALL display summary statistics including total cycles, LLM calls, API calls saved, and estimated costs
6. WHEN running THEN it SHALL display progress summaries every 1 minute
7. WHEN interrupted THEN it SHALL gracefully save session data and display final statistics

### Requirement 8: Configuration Management

**User Story:** As a developer, I want to configure monitoring settings like intervals, voice backends, and model selection, so that I can customize the system to my preferences.

#### Acceptance Criteria

1. WHEN starting the system THEN it SHALL load configuration from MonitorConfig class with environment variable overrides
2. WHEN configuration changes THEN the system SHALL require restart to apply new settings (no hot-reload)
3. WHEN using different models THEN the system SHALL support model selection via SS_MAIN_MODEL environment variable
4. WHEN switching voice backends THEN the system SHALL provide toggle commands via justfile (voice-toggle, voice-macos, voice-openai)
5. WHEN configuring compression THEN the system SHALL support SCREENSHOT_COMPRESS, LLM_ANALYZE_COMPRESSED, and HASH_DETECTION_ON_COMPRESSED options
6. WHEN setting intervals THEN the system SHALL use MONITORING_INTERVAL_SECONDS for screenshot frequency

### Requirement 9: Error Handling and Resilience

**User Story:** As a developer, I want the system to handle errors gracefully and continue monitoring even when individual components fail, so that temporary issues don't interrupt my focus sessions.

#### Acceptance Criteria

1. WHEN screenshot capture fails THEN the system SHALL attempt screencapture fallback and continue monitoring
2. WHEN Peekaboo returns errors THEN the system SHALL parse stderr and handle exit code 0 failures
3. WHEN screenshot files are invalid THEN the system SHALL validate file size (>5KB) and reject failed captures
4. WHEN LLM API calls timeout THEN the system SHALL use 60-second timeout and log errors
5. WHEN Things3 integration fails THEN the system SHALL fallback to "feisty" as default task name
6. WHEN temporary files cannot be cleaned THEN the system SHALL continue operation and log cleanup failures
7. WHEN error count reaches 5 THEN the system SHALL stop monitoring automatically
8. WHEN errors occur THEN the system SHALL display clear error messages with rich formatting

### Requirement 10: Enhanced User Interface and Testing

**User Story:** As a developer, I want an enhanced CLI interface with better visual feedback and automated testing capabilities, so that I can easily monitor progress and verify distraction detection accuracy.

#### Acceptance Criteria

1. WHEN the system displays output THEN it SHALL use rich formatting with colors, panels, and proper spacing between cycles
2. WHEN cycles complete THEN the system SHALL display dimmed screenshot paths for manual verification
3. WHEN running the system THEN it SHALL provide a dedicated Claude testing mode via `just run-for-claude`
4. WHEN in Claude testing mode THEN the system SHALL run for 1 minute with enhanced output formatting
5. WHEN testing distraction detection THEN the system SHALL automatically switch focus to Finder at cycles 3 and 8
6. WHEN focus switching occurs THEN the system SHALL switch back to IDE immediately after screenshot capture
7. WHEN displaying progress THEN the system SHALL show error counts in summary reports
8. WHEN failures occur THEN the system SHALL highlight them with bold red formatting for clear visibility
9. WHEN LLM calls exceed 20 seconds THEN the system SHALL display red warning messages for performance monitoring
10. WHEN voice generation exceeds 20 seconds THEN the system SHALL display red warning messages for performance monitoring
11. WHEN voice alerts are triggered THEN the system SHALL display the exact voice message text in yellow formatting

### Requirement 11: UI Regression Testing

**User Story:** As a developer, I want automated tests that prevent regression of UI formatting and logging features, so that visual feedback and performance monitoring remain consistent across code changes.

#### Acceptance Criteria

1. WHEN running regression tests THEN the system SHALL verify performance warning display for LLM calls >20s
2. WHEN running regression tests THEN the system SHALL verify performance warning display for voice generation >20s
3. WHEN running regression tests THEN the system SHALL verify voice message text preservation and yellow formatting
4. WHEN running regression tests THEN the system SHALL verify error message red formatting and debug log hints
5. WHEN running regression tests THEN the system SHALL verify ANALYZING cycle yellow panel formatting
6. WHEN running regression tests THEN the system SHALL verify log file structure and required metadata fields
7. WHEN tests fail THEN the system SHALL provide clear feedback about which UI/logging requirement was violated