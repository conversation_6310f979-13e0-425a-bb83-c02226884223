# Will Detector Sunset Plan

**Date**: 2025-09-23
**Status**: Ready for archival

## Current State
- Code exists in `src/will_detector/`
- Has active justfile commands
- Contains real sightings data from August 2025
- Referenced by other modules for screenshot functionality

## Sunset Steps

### Phase 1: Extract Reusable Components
- [ ] Move `capture_active_window_screenshot` function to `src/utils/screenshot.py`
- [ ] Update imports in other modules to use new location
- [ ] Test that basic_checker_* modules still work

### Phase 2: Archive Data
- [ ] Backup `src/will_detector/data/sightings.json` to `archive/will_detector_sightings_backup.json`
- [ ] Backup any important screenshots to `archive/will_detector_screenshots/`

### Phase 3: Remove Code
- [ ] Delete `src/will_detector/` directory
- [ ] Remove will_detector commands from justfile:
  - `test-capture`
  - `test-classify` 
  - `test-storage`
  - `sightings`
  - `clean-screenshots`

### Phase 4: Update Documentation
- [ ] Remove will_detector references from `.kiro/steering/structure.md`
- [ ] Update justfile comments
- [ ] Archive CLAUDE.md and CLAUDE_SPEC.md (already done)

### Phase 5: Clean Root Files
- [ ] Move `CLAUDE.md` to `archive/CLAUDE_will_detector.md`
- [ ] Move `CLAUDE_SPEC.md` to `archive/CLAUDE_SPEC_will_detector.md`
- [ ] Create new `CLAUDE.md` focused on current Feisty5 system

## Dependencies to Check
- `src/basic_checker_*/` modules import screenshot functionality
- Image compression utilities reference will_detector
- Any other cross-references found during cleanup

## Rollback Plan
If needed, restore from:
- Archived specs in `.kiro/specs/history/will-detector/2025-09-23/`
- Git history before deletion
- Backed up data files