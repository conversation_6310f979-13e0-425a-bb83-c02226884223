# Spec Management

## Directory Structure
```
.kiro/specs/
├── current/           # Active specs for current features
├── history/           # Archived/deprecated specs
└── README.md         # This file
```

## Versioning Convention
- **Current specs**: Live in `.kiro/specs/current/[feature-name]/`
- **Historical specs**: Moved to `.kiro/specs/history/[feature-name]/[date-archived]/`
- **Naming**: Use kebab-case for feature names

## Archival Process
When sunsetting a feature:
1. Move spec from `current/` to `history/[feature-name]/[YYYY-MM-DD]/`
2. Update steering documents to remove references
3. Archive or remove source code
4. Update justfile to remove commands