# Project Structure

## Source Code Organization

The project follows a modular architecture with specialized components for different monitoring systems:

```
src/
├── basic_checker_hardcoded/     # Original hardcoded distraction checker
├── basic_checker_tasklist/      # Enhanced checker with Things3 integration
├── top_task_checker/           # Focus monitoring for top priority tasks
├── will_detector/              # Person detection in screenshots (DEPRECATED)
├── img_compression/            # Image processing utilities
├── notifications/              # macOS notification system
├── tasklist/                   # Things3 task management integration
└── utils/                      # Shared utilities
```

## Key Module Patterns

### Checker Modules (`basic_checker_*`)
Each checker module follows this structure:
```
basic_checker_*/
├── main.py                     # Entry point and monitoring loop
├── detector.py                 # LLM-based analysis
├── monitor.py                  # Screenshot capture orchestration
├── speaker.py                  # Voice alert system
├── hash_change_detection.py    # Performance optimization
├── timer.py                    # Interval timing utilities
├── voice_generator.py          # Dynamic voice message generation
├── data/
│   ├── screenshots/            # Captured images
│   ├── compressed/             # Processed images
│   └── logs/                   # Session monitoring data
└── voice/                      # Voice system implementation
```

### Specialized Modules

#### `will_detector/` (DEPRECATED - Being Sunset)
- **Purpose**: Detect specific person in screenshots (experimental feature)
- **Key Files**: `classifier.py`, `screenshot.py`, `storage.py`
- **Data**: `data/sightings.json`, `data/screenshots/`
- **Status**: Archived to `.kiro/specs/history/will-detector/2025-09-23/`
- **Sunset Plan**: Extract reusable screenshot functionality to `src/utils/`

#### `img_compression/`
- **Purpose**: Image processing and compression utilities
- **Methods**: Binarization, bilateral filtering, contrast enhancement
- **Key Files**: `compress_utils.py`, `binarization.py`

#### `top_task_checker/`
- **Purpose**: Monitor focus on current top priority task
- **Integration**: Things3 task fetching
- **Key Files**: `task_fetcher.py`, `focus_analyzer.py`, `hash_detector.py`

## Configuration Files

### Root Level
- `pyproject.toml` - Python package configuration
- `requirements.txt` - Dependencies
- `justfile` - Task runner commands
- `.env` - Environment variables (not committed)

### AI/Agent Configuration
- `.claude/` - Claude AI agent configurations
- `.cursor/rules/` - Cursor IDE rules
- `.kiro/steering/` - Kiro AI steering documents
- `.roo/` - Roo AI configurations

## Data Storage Patterns

### Screenshots
- **Location**: `src/*/data/screenshots/`
- **Naming**: `YYYYMMDD_HHMMSS.png`
- **Compression**: Stored in `compressed/` subdirectories
- **Cleanup**: Automated via justfile commands

### Logs
- **Session Logs**: JSON format in `data/logs/`
- **Naming**: `monitoring_session_YYYYMMDD_HHMMSS.json`
- **Content**: Cycle data, analysis results, performance metrics

### Test Data
- **Location**: `tests/*/test_data/`
- **Purpose**: Sample images for testing distraction detection
- **Categories**: `focused_*`, `distracted_*` examples

## Import Patterns

### Path Management
```python
# Standard pattern for module imports
sys.path.append(str(Path(__file__).parent.parent))
```

### Cross-Module Dependencies
- Screenshot capture: `from will_detector.screenshot import capture_active_window_screenshot` (TO BE MOVED to `src/utils/`)
- Image compression: `from img_compression.compress_utils import compress_image`
- Voice alerts: `from basic_checker_*/speaker import speak`

### Spec Management
- **Current Specs**: `.kiro/specs/current/[feature-name]/`
- **Historical Specs**: `.kiro/specs/history/[feature-name]/[date-archived]/`
- **Versioning**: Use kebab-case for feature names, ISO date for archival

## Testing Structure

```
tests/
├── coding_feisty_checker/      # Tests for distraction detection
├── img_compression/            # Image processing tests
├── top_task_checker/           # Task focus monitoring tests
└── test_data/                  # Shared test assets
```

## Development Conventions

### File Naming
- **Main modules**: `main.py` for entry points
- **Utilities**: Descriptive names (`hash_change_detection.py`)
- **Data files**: Timestamp-prefix-based naming for screenshots and logs

### Directory Structure
- **Data isolation**: Each module has its own `data/` directory
- **Shared utilities**: Common code in `src/utils/` or imported from other modules
- **Configuration**: Module-specific config in individual directories

### Documentation
- **Specs**: `SPEC_AF.md` files for feature requirements
- **Implementation notes**: `IMPLEMENTATION_DOCS.md` for technical details
- **Troubleshooting**: `claude_previous_mistakes.md` for known issues