# Product Overview

Feisty5 is a productivity monitoring system that helps developers stay focused on their top task through automated distraction detection and gentle voice alerts.

## Core Features

- **Screenshot Monitoring**: Captures active window screenshots every 5 seconds
- **Distraction Detection**: Uses LLM vision analysis (GPT5) to determine if user is focused on their top task or distracted
- **Voice Alerts**: Provides gentle voice reminders when distractions are detected
- **Hash Change Detection**: Optimizes API costs by only analyzing screenshots when screen content changes significantly
- **Things3 Integration**: Fetches current top task to define focus context and enable task-specific distraction detection

## Target Users

Developers / founders (but it's only for me) who want to maintain focus sessions and receive gentle nudges when distracted by other, less important tasks.

## Key Value Propositions

- Reduces API costs through intelligent change detection (30-40% savings)
- Non-intrusive monitoring with kind, supportive voice alerts
- Integrates with existing task management workflows (Things3)
- Configurable compression and analysis settings for performance optimization