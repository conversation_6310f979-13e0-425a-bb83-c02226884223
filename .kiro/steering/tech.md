# Technology Stack

## Build System & Package Management

- **Build System**: setuptools with pyproject.toml configuration
- **Package Manager**: pip with requirements.txt and uv.lock
- **Virtual Environment**: Must use `.venv` (not `venv`)
- **Python Version**: >=3.9

## Core Dependencies

### LLM & AI
- **OpenAI**: `openai==1.59.9` - GPT-4/5 vision analysis
- **Anthropic**: `anthropic==0.42.0` - Claude models (sonnet-4-latest)
- **Model Configuration**: 
  - Main models: `gpt-5` (OpenAI), `sonnet-4-latest` (Anthropic)
  - Distraction detection: `gpt-5-nano` (override with `SS_MAIN_MODEL` env var)
  - Never use `max_tokens` parameter (deliberately set to None)

### Image Processing
- **Pillow**: `pillow==11.3.0` - Image manipulation
- **OpenCV**: `opencv-python==*********` - Advanced image processing
- **ImageHash**: `imagehash==4.3.2` - Perceptual hashing for change detection

### External Integrations
- **Google Sheets**: `gspread==6.0.2`, `google-auth-oauthlib==1.2.0`
- **Things3**: Direct AppleScript integration for task management

### Utilities
- **CLI**: `typer==0.12.4`, `rich==14.1.0` - Command line interface
- **Config**: `python-dotenv==1.0.1` - Environment variable management

## Common Commands

### Setup
```bash
# Initial setup
just setup

# Install dependencies
pip install -r requirements.txt
```

### Running Applications
```bash
# Main feisty checker with Things3 integration
just run

# Will detector
python run.py

# Top task checker
python src/top_task_checker/main.py
```

### Testing
```bash
# Run all tests
python -m pytest

# Test specific modules
python -m src.module_name

# Integration tests
python test_integration.py
```

### Voice System
```bash
# Toggle voice backend (macOS say ↔ OpenAI TTS)
just voice-toggle

# Test voice
just voice-test

# Set specific backend
just voice-macos
just voice-openai
```

### Maintenance
```bash
# Clean screenshots (preserve logs/test data)
just clean-feisty-screenshots

# View recent sightings
just sightings
```

## Architecture Patterns

### Configuration
- **CFG Singleton**: Global config pattern in `run_config.py`
- **Manual .env Parsing**: Some modules avoid dependencies (see detector.py:12-18)
- **Environment Overrides**: Use env vars for model selection and voice backend

### Performance Optimization
- **Hash Change Detection**: Skip API calls when screen unchanged (30-40% cost savings)
- **Image Compression**: Mandatory compression with `compress=True`
- **Structured Outputs**: OpenAI API structured output enforcement for JSON responses

### Voice System
- **Dual Backend**: macOS `say` (default) + OpenAI TTS (nova voice)
- **Rate**: 173 WPM (120% of default 144 WPM)
- **Toggle**: `VOICE_BACKEND` env var or `just voice-toggle`