#!/usr/bin/env python3
"""Test the Things3 integration with basic_checker_tasklist"""

import sys
from pathlib import Path

# Add basic_checker_tasklist to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "basic_checker_tasklist"))

from detector import get_top_task

def test_integration():
    """Test that get_top_task returns either top task from Things3 or fallback"""
    
    print("Testing Things3 integration...")
    
    # Test get_top_task function
    top_task = get_top_task()
    print(f"Top task retrieved: '{top_task}'")
    
    # Validate result
    assert isinstance(top_task, str), "Top task should be a string"
    assert len(top_task.strip()) > 0, "Top task should not be empty"
    
    print("✅ Integration test passed!")
    
    # Test that prompt would be generated correctly
    prompt_preview = f"Does it look like the user is working on coding {top_task} or are they getting distracted?"
    print(f"Sample prompt: {prompt_preview}")

if __name__ == "__main__":
    test_integration()