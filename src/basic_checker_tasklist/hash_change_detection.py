import imagehash
from PIL import Image


def calculate_perceptual_hash(image_path: str) -> str:
    """Calculate perceptual hash of an image using dhash algorithm.

    Args:
        image_path: Path to image file

    Returns:
        Hex string representation of perceptual hash
    """
    with Image.open(image_path) as img:
        return str(imagehash.dhash(img))


def has_significant_change(
    current_hash: str, previous_hash: str, threshold: int = 4
) -> bool:
    """Check if images have significant change based on hash comparison.

    Args:
        current_hash: Hash of current image
        previous_hash: Hash of previous image
        threshold: Hamming distance threshold (default 4 bits = ~10% change)

    Returns:
        True if change exceeds threshold, False otherwise
    """
    if not previous_hash or not current_hash:
        return True

    hash1 = imagehash.hex_to_hash(current_hash)
    hash2 = imagehash.hex_to_hash(previous_hash)

    hamming_distance = hash1 - hash2
    return hamming_distance > threshold


def get_change_percentage(current_hash: str, previous_hash: str) -> float:
    """Get approximate percentage change between two image hashes.

    Args:
        current_hash: Hash of current image
        previous_hash: Hash of previous image

    Returns:
        Approximate percentage change (0-100)
    """
    if not previous_hash or not current_hash:
        return 100.0

    hash1 = imagehash.hex_to_hash(current_hash)
    hash2 = imagehash.hex_to_hash(previous_hash)

    hamming_distance = hash1 - hash2
    total_bits = len(hash1.hash.flatten())

    return (hamming_distance / total_bits) * 100.0
