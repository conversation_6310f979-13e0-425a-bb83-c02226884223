#!/usr/bin/env python3
"""
Voice Backend Toggle Script
Easily switch between macOS say and OpenAI TTS.
"""

import os
import sys
from pathlib import Path


def read_env_file():
    """Read current .env file contents."""
    # Look for .env in project root (4 levels up from this file)
    env_path = Path(__file__).parent.parent.parent.parent / ".env"
    if not env_path.exists():
        return {}

    env_vars = {}
    with open(env_path) as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith("#") and "=" in line:
                key, value = line.split("=", 1)
                env_vars[key] = value
    return env_vars


def write_env_file(env_vars):
    """Write updated .env file."""
    # Look for .env in project root (4 levels up from this file)
    env_path = Path(__file__).parent.parent.parent.parent / ".env"

    # Read existing file to preserve order and comments
    lines = []
    if env_path.exists():
        with open(env_path) as f:
            for line in f:
                stripped = line.strip()
                if stripped and not stripped.startswith("#") and "=" in stripped:
                    key = stripped.split("=", 1)[0]
                    if key in env_vars:
                        lines.append(f"{key}={env_vars[key]}\n")
                        del env_vars[key]  # Mark as written
                    else:
                        lines.append(line)
                else:
                    lines.append(line)

    # Add any new variables
    if env_vars:
        if lines and not lines[-1].endswith("\n\n"):
            lines.append("\n")
        lines.append("# Voice Configuration\n")
        for key, value in env_vars.items():
            lines.append(f"{key}={value}\n")

    with open(env_path, "w") as f:
        f.writelines(lines)


def toggle_voice():
    """Toggle between voice backends."""
    env_vars = read_env_file()

    current = env_vars.get("VOICE_BACKEND", "macos")
    modes = ["macos", "openai_tts"]

    try:
        current_idx = modes.index(current.lower())
    except ValueError:
        current_idx = 0

    next_idx = (current_idx + 1) % len(modes)
    next_mode = modes[next_idx]

    # Update environment
    env_vars["VOICE_BACKEND"] = next_mode

    # Add OpenAI TTS settings if switching to it
    if next_mode == "openai_tts":
        if "OPENAI_TTS_MODEL" not in env_vars:
            env_vars["OPENAI_TTS_MODEL"] = "tts-1"  # Fast model
        if "OPENAI_TTS_VOICE" not in env_vars:
            env_vars["OPENAI_TTS_VOICE"] = "nova"  # Natural voice
        if "OPENAI_TTS_SPEED" not in env_vars:
            env_vars["OPENAI_TTS_SPEED"] = "1.2"  # 120% speed
        if "OPENAI_TTS_FALLBACK" not in env_vars:
            env_vars["OPENAI_TTS_FALLBACK"] = "true"

    write_env_file(env_vars)

    print(f"🔄 Voice backend switched: {current} → {next_mode}")

    # Show current settings
    if next_mode == "openai_tts":
        print(f"   Model: {env_vars.get('OPENAI_TTS_MODEL', 'tts-1')}")
        print(f"   Voice: {env_vars.get('OPENAI_TTS_VOICE', 'nova')}")
        print(f"   Speed: {env_vars.get('OPENAI_TTS_SPEED', '1.2')}x")
        print(f"   Fallback: {env_vars.get('OPENAI_TTS_FALLBACK', 'true')}")

    return next_mode


def set_voice(backend: str):
    """Set specific voice backend."""
    valid_backends = ["macos", "openai_tts"]

    if backend.lower() not in valid_backends:
        print(f"❌ Invalid backend: {backend}")
        print(f"   Valid options: {', '.join(valid_backends)}")
        return False

    env_vars = read_env_file()
    env_vars["VOICE_BACKEND"] = backend.lower()

    # Add OpenAI TTS settings if needed
    if backend.lower() == "openai_tts":
        if "OPENAI_TTS_MODEL" not in env_vars:
            env_vars["OPENAI_TTS_MODEL"] = "tts-1"
        if "OPENAI_TTS_VOICE" not in env_vars:
            env_vars["OPENAI_TTS_VOICE"] = "nova"
        if "OPENAI_TTS_SPEED" not in env_vars:
            env_vars["OPENAI_TTS_SPEED"] = "1.2"
        if "OPENAI_TTS_FALLBACK" not in env_vars:
            env_vars["OPENAI_TTS_FALLBACK"] = "true"

    write_env_file(env_vars)
    print(f"✅ Voice backend set to: {backend.lower()}")

    return True


def test_voice():
    """Test current voice backend."""
    # Import here to avoid circular dependencies
    # Add parent directories to path for import
    import sys

    sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))
    from src.basic_checker_hardcoded.speaker import speak

    env_vars = read_env_file()
    backend = env_vars.get("VOICE_BACKEND", "macos")

    print(f"🔊 Testing {backend} voice...")

    # Set environment for this process
    os.environ["VOICE_BACKEND"] = backend
    if backend == "openai_tts":
        for key in [
            "OPENAI_TTS_MODEL",
            "OPENAI_TTS_VOICE",
            "OPENAI_TTS_SPEED",
            "OPENAI_TTS_VOLUME",
            "OPENAI_TTS_FALLBACK",
        ]:
            if key in env_vars:
                os.environ[key] = env_vars[key]

    test_text = "Testing voice backend. You're doing great! Stay focused on coding."
    speak(test_text)

    print("✅ Voice test complete!")


def show_status():
    """Show current voice configuration."""
    env_vars = read_env_file()
    backend = env_vars.get("VOICE_BACKEND", "macos")

    print("=" * 60)
    print("Voice Configuration Status")
    print("=" * 60)
    print(f"Current Backend: {backend}")

    if backend == "openai_tts":
        print("\nOpenAI TTS Settings:")
        print(f"  Model: {env_vars.get('OPENAI_TTS_MODEL', 'tts-1')}")
        print(f"  Voice: {env_vars.get('OPENAI_TTS_VOICE', 'nova')}")
        print(f"  Speed: {env_vars.get('OPENAI_TTS_SPEED', '1.2')}x")
        print(f"  Volume: {env_vars.get('OPENAI_TTS_VOLUME', '1.75')}x (afplay volume)")
        print(f"  Fallback: {env_vars.get('OPENAI_TTS_FALLBACK', 'true')}")

        # Check API key
        api_key = env_vars.get("OPENAI_API_KEY", "")
        if api_key:
            print(
                f"  API Key: {'✅ Configured' if len(api_key) > 20 else '❌ Invalid'}"
            )
        else:
            print("  API Key: ❌ Not found")

    print("\nAvailable Commands:")
    print("  python toggle_voice.py         - Toggle between backends")
    print("  python toggle_voice.py test    - Test current voice")
    print("  python toggle_voice.py macos   - Set to macOS say")
    print("  python toggle_voice.py openai  - Set to OpenAI TTS")
    print("=" * 60)


if __name__ == "__main__":
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()

        if arg == "test":
            test_voice()
        elif arg == "status":
            show_status()
        elif arg in ["macos", "mac"]:
            set_voice("macos")
        elif arg in ["openai", "openai_tts", "tts"]:
            set_voice("openai_tts")
        else:
            print(f"Unknown command: {arg}")
            show_status()
    else:
        # Default action: toggle
        new_backend = toggle_voice()

        # Offer to test
        print("\n🎤 Would you like to test the new voice? (y/n)")
        if input().lower().startswith("y"):
            test_voice()
