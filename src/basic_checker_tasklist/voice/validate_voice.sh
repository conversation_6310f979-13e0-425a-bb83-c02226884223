#!/bin/bash
# Validate voice system setup

echo "============================================================"
echo "VOICE SYSTEM VALIDATION"
echo "============================================================"
echo ""

# 1. Check .env settings
echo "1️⃣ Checking .env voice settings..."
echo "-----------------------------------"
if [ -f .env ]; then
    echo "✅ .env file exists"
    echo ""
    echo "Voice settings:"
    grep -E "VOICE|TTS" .env | while read line; do
        echo "  • $line"
    done
else
    echo "❌ .env file not found"
fi

echo ""

# 2. Test voice output
echo "2️⃣ Testing voice output..."
echo "-----------------------------------"
./src/basic_checker_hardcoded/voice/voice_speak.sh "Voice system validation test"

echo ""

# 3. Test just commands
echo "3️⃣ Testing just commands..."
echo "-----------------------------------"
echo "Testing 'just voice-test':"
just voice-test

echo ""

# 4. List voice directory contents
echo "4️⃣ Voice directory contents..."
echo "-----------------------------------"
ls -la src/basic_checker_hardcoded/voice/ | grep -v "^total"

echo ""
echo "============================================================"
echo "VALIDATION COMPLETE"
echo "============================================================"
echo ""
echo "Summary:"
echo "  • Voice backend: OpenAI TTS (set in .env)"
echo "  • Volume: 2.0x (maximum loudness)"
echo "  • Scripts location: src/basic_checker_hardcoded/voice/"
echo "  • Toggle script: src/basic_checker_hardcoded/voice/toggle_voice.py"
echo "  • Just commands: voice-test, voice-toggle, voice-status, etc."
echo ""
echo "✅ Voice system is properly configured!"
