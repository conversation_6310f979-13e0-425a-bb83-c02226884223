# Voice Backend System

This directory contains the voice backend implementation for the coding feisty checker, supporting both macOS `say` and OpenAI TTS.

## 🎯 Features

- **Dual Backend Support**: macOS say (default) and OpenAI TTS (high quality)
- **Easy Toggle**: Switch backends with a single command
- **Volume Control**: Adjustable volume for OpenAI TTS (0.0-2.0x)
- **Automatic Fallback**: Falls back to macOS if OpenAI fails
- **Environment Configuration**: All settings in `.env` file

## 📁 Files

- `openai_tts_speaker.py` - OpenAI TTS implementation
- `toggle_voice.py` - Backend toggle script
- `voice_speak.sh` - Shell wrapper for reliable execution
- `test_volume.py` - Volume testing utility
- `demo_*.sh` - Demonstration scripts
- `compare_loudness.sh` - Compare volume levels
- `VOLUME_CONTROL.md` - Volume control documentation

## ⚙️ Configuration

All settings are stored in the project root `.env` file:

```bash
# Voice Backend Settings
VOICE_BACKEND=openai_tts       # or "macos"
OPENAI_TTS_MODEL=tts-1         # or "tts-1-hd" for higher quality
OPENAI_TTS_VOICE=nova          # or alloy, echo, fable, onyx, shimmer
OPENAI_TTS_SPEED=1.2           # 0.25 to 4.0 (1.2 = 20% faster)
OPENAI_TTS_VOLUME=2.0          # 0.0 to 2.0 (2.0 = maximum loudness)
OPENAI_TTS_FALLBACK=true       # Auto-fallback to macOS on error
```

## 🎮 Usage

### Just Commands (Recommended)

```bash
just voice-test      # Test current voice
just voice-toggle    # Switch between backends
just voice-status    # Show configuration
just voice-macos     # Use macOS say
just voice-openai    # Use OpenAI TTS
```

### Direct Usage

```bash
# Test voice
./src/basic_checker_hardcoded/voice/voice_speak.sh "Hello world"

# Toggle backend
python3 src/basic_checker_hardcoded/voice/toggle_voice.py

# Test different volumes
./src/basic_checker_hardcoded/voice/demo_volume.sh
```

### Python Integration

```python
from src.basic_checker_hardcoded.speaker import speak

# Uses configured backend automatically
speak("Alert message", rate=173)
```

## 🔊 Volume Control

OpenAI TTS supports volume adjustment via `afplay -v`:
- **0.0-1.0**: Quieter than normal
- **1.0**: Normal volume
- **1.75**: Recommended (75% louder, matches macOS)
- **2.0**: Maximum (currently set)

To adjust volume, edit `.env`:
```bash
OPENAI_TTS_VOLUME=1.75  # Change to desired level
```

## 🐛 Known Issues

1. **Python Output Buffering**: Some shells don't show Python output properly
   - **Solution**: Use shell wrappers (`voice_speak.sh`)

2. **Import Paths**: Scripts need proper Python path setup
   - **Solution**: Run from project root or use just commands

## 💰 Cost

OpenAI TTS costs approximately:
- $15 per 1M characters
- ~$3-5/month for typical usage
- Uses same API key as other OpenAI services

## 🔧 Troubleshooting

### Can't hear OpenAI voice
1. Check API key in `.env`
2. Verify volume setting: `OPENAI_TTS_VOLUME=2.0`
3. Test with: `just voice-test`

### Voice not loud enough
- Set `OPENAI_TTS_VOLUME=2.0` (maximum)
- Lower system music volume
- Consider using headphones

### Toggle not working
- Ensure running from project root
- Use just commands: `just voice-toggle`
- Check `.env` file exists and is readable

## 📝 Implementation Details

The system uses a layered architecture:
1. `speaker.py` - Main interface (backward compatible)
2. `openai_tts_speaker.py` - OpenAI implementation
3. `voice_speak.sh` - Shell wrapper for reliability
4. `.env` - Centralized configuration

Voice alerts trigger when distraction is detected, using the configured backend and settings.