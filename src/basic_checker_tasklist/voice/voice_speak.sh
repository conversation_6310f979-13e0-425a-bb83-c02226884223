#!/bin/bash
# Voice backend wrapper script
# Uses environment variable VOICE_BACKEND to determine which TTS to use

# Load environment variables
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Get the text to speak
TEXT="$*"

# Default to macOS if not specified
BACKEND="${VOICE_BACKEND:-macos}"

if [ "$BACKEND" = "openai_tts" ] && [ -n "$OPENAI_API_KEY" ]; then
    # Use OpenAI TTS
    echo "🤖 Using OpenAI TTS (nova voice)..."
    
    # Create temp file for audio
    TEMP_AUDIO="/tmp/openai_tts_$$.mp3"
    
    # Call OpenAI API
    curl -s https://api.openai.com/v1/audio/speech \
        -H "Authorization: Bearer $OPENAI_API_KEY" \
        -H "Content-Type: application/json" \
        -d "{
            \"model\": \"tts-1\",
            \"input\": \"$TEXT\",
            \"voice\": \"${OPENAI_TTS_VOICE:-nova}\",
            \"speed\": ${OPENAI_TTS_SPEED:-1.2}
        }" \
        --output "$TEMP_AUDIO"
    
    # Play if file was created
    if [ -f "$TEMP_AUDIO" ]; then
        afplay "$TEMP_AUDIO"
        rm "$TEMP_AUDIO"
        echo "✅ OpenAI TTS complete"
    else
        echo "❌ OpenAI TTS failed, falling back to macOS"
        say "$TEXT"
    fi
else
    # Use macOS say
    echo "🍎 Using macOS say..."
    say -r "${VOICE_RATE:-173}" "$TEXT"
    echo "✅ macOS say complete"
fi
