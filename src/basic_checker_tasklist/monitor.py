import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from basic_checker_hardcoded.detector import analyze_distraction
from basic_checker_hardcoded.hash_change_detection import (
    calculate_perceptual_hash,
    has_significant_change,
)
from basic_checker_hardcoded.speaker import speak
from basic_checker_hardcoded.timer import IntervalTimer
from basic_checker_hardcoded.voice_generator import generate_kind_voice_message
from will_detector.screenshot import capture_active_window_screenshot


def run_monitoring_loop():
    """
    Main monitoring loop - captures screenshots every 5 seconds and checks for distraction.
    """
    data_dir = Path(__file__).parent / "data" / "screenshots"
    data_dir.mkdir(parents=True, exist_ok=True)

    print("Starting coding feisty distraction monitor...")
    print("Monitoring every 5 seconds. Press Ctrl+C to stop.")

    # Hash change detection state
    timer = IntervalTimer(5.0)
    last_hash = None
    analysis_count = 0
    skip_count = 0

    try:
        while True:
            timer.start_cycle()

            # Capture screenshot with compression
            screenshot_data = capture_active_window_screenshot(
                output_dir=data_dir, compress=True
            )

            if screenshot_data and "compressed_path" in screenshot_data:
                compressed_path = screenshot_data["compressed_path"]
                print(f"Screenshot captured and compressed: {compressed_path}")

                # Calculate hash and check for significant change
                current_hash = calculate_perceptual_hash(compressed_path)

                if last_hash is None or has_significant_change(current_hash, last_hash):
                    analysis_count += 1
                    print("🔍 Analyzing - significant change detected")

                    # Analyze for distraction
                    analysis = analyze_distraction(compressed_path)
                    print(f"Analysis: {analysis}")

                    # Voice alert if distracted
                    if analysis.get("distracted_decision", False):
                        reasoning = analysis.get("reasoning", "User appears distracted")
                        alert_message = generate_kind_voice_message(reasoning)
                        speak(alert_message, rate=173)  # 120% of 144 WPM default
                        print(f"🔊 Alert: {alert_message}")
                else:
                    skip_count += 1
                    print(
                        f"⚡ Skipping - no significant change (API calls saved: {skip_count})"
                    )

                last_hash = current_hash

            else:
                print("Screenshot capture failed")

            # Wait for precise 5-second interval
            timer.wait_for_next()

    except KeyboardInterrupt:
        print("\nMonitoring stopped.")


if __name__ == "__main__":
    run_monitoring_loop()
