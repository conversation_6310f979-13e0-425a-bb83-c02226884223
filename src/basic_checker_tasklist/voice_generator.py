import json
import os
from pathlib import Path

from openai import OpenAI
from .detector import get_top_task

MODEL = os.getenv("SS_MAIN_MODEL", "gpt-5-nano")

# Load environment variables manually to avoid dependency
env_file = Path(__file__).parent.parent.parent / ".env"
if env_file.exists():
    with open(env_file) as f:
        for line in f:
            if line.strip() and not line.startswith("#"):
                key, value = line.strip().split("=", 1)
                os.environ[key] = value


def generate_kind_voice_message(analysis_reasoning: str) -> str:
    """
    Generate a kind voice message based on existing distraction analysis.

    Args:
        analysis_reasoning: Reasoning text from distraction detector

    Returns:
        Kind voice message under 20 words
    """
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    task_name = f"coding {get_top_task()}"

    # Structured output schema
    schema = {
        "type": "object",
        "properties": {
            "activity": {"type": "string", "maxLength": 20},
            "voice_message": {"type": "string", "maxLength": 100},
        },
        "required": ["activity", "voice_message"],
        "additionalProperties": False,
    }

    response = client.chat.completions.create(
        model=MODEL,
        messages=[
            {
                "role": "user",
                "content": f"The user is distracted from {task_name}. Based on this analysis: '{analysis_reasoning}' - extract in 1-4 words what the user is doing that isn't {task_name} work (e.g. 'social media', 'reading news', 'watching videos', 'checking email'). Then create a stern, kind, encouraging voice message under 20 words that clearly tells them they're off-track and suggests getting back to {task_name} work. Be supportive but direct.",
            }
        ],
        response_format={
            "type": "json_schema",
            "json_schema": {"name": "voice_message", "schema": schema},
        },
    )

    result = json.loads(response.choices[0].message.content)
    return result["voice_message"]


if __name__ == "__main__":
    import sys
    from pathlib import Path

    # Add src to path for imports
    sys.path.append(str(Path(__file__).parent.parent))

    from will_detector.screenshot import capture_active_window_screenshot

    print("🎯 Testing voice generator...")

    # Capture a test screenshot
    data_dir = Path(__file__).parent / "data" / "screenshots"
    data_dir.mkdir(parents=True, exist_ok=True)

    screenshot_data = capture_active_window_screenshot(
        output_dir=data_dir, compress=True
    )

    if screenshot_data and "compressed_path" in screenshot_data:
        compressed_path = screenshot_data["compressed_path"]
        print(f"📸 Screenshot: {Path(compressed_path).name}")

        # Test with simulated analysis reasoning
        reasoning = "The screenshot shows a social media platform with posts and comments, indicating the user is browsing social content rather than focusing on coding tasks."

        print(f"🔍 Simulated analysis: {reasoning[:60]}...")

        # Generate voice message from analysis
        print("🤖 Generating kind voice message from analysis...")
        message = generate_kind_voice_message(reasoning)

        print(f"🔊 Generated message: '{message}'")
        print(f"📏 Message length: {len(message)} characters")

        # Test speaking it
        from basic_checker_hardcoded.speaker import speak

        print("🎵 Speaking message...")
        speak(message, rate=173)

        print("✅ Voice generator test completed!")
    else:
        print("❌ Failed to capture screenshot for testing")
