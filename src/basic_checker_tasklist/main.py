import json
import os
import subprocess
import sys
import time
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

from rich.console import Console
from rich.panel import Panel
from rich.text import Text

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Use optimized detector for 30-40% faster response (9-12s vs 13-17s)
# from basic_checker_hardcoded.detector_optimized import analyze_distraction
# Or use original for maximum accuracy:
from basic_checker_tasklist.detector import analyze_distraction
from basic_checker_tasklist.hash_change_detection import (
    calculate_perceptual_hash,
    get_change_percentage,
    has_significant_change,
)
from basic_checker_tasklist.speaker import speak
from basic_checker_tasklist.timer import IntervalTimer
from basic_checker_tasklist.voice_generator import generate_kind_voice_message
from will_detector.screenshot import capture_active_window_screenshot


@dataclass
class MonitorConfig:
    """Configuration for the coding feisty monitor."""

    # Screenshot settings
    SCREENSHOT_RESOLUTION: str = "full"  # "full", "half", "quarter"
    SCREENSHOT_COMPRESS: bool = True
    SCREENSHOT_COMPRESSION_METHOD: str = "binarize"  # "binarize", "bilateral"

    # Analysis settings
    LLM_ANALYZE_COMPRESSED: bool = (
        False  # True = use compressed image, False = use original
    )
    HASH_DETECTION_ON_COMPRESSED: bool = (
        False  # True = hash compressed, False = hash original
    )

    # Storage settings
    KEEP_ORIGINAL_AFTER_COMPRESSION: bool = (
        True  # True = keep both, False = delete original
    )
    CLEANUP_TEMP_FILES: bool = True

    # Performance settings
    MONITORING_INTERVAL_SECONDS: float = 5.0
    HASH_CHANGE_THRESHOLD: float = 0.1  # 10% change threshold

    # Voice settings
    VOICE_ALERTS_ENABLED: bool = True
    VOICE_RATE: int = 173  # 120% of default 144 WPM

    def get_screenshot_scale(self) -> float:
        """Get scale factor for screenshot resolution."""
        scale_map = {"full": 1.0, "half": 0.5, "quarter": 0.25}
        return scale_map.get(self.SCREENSHOT_RESOLUTION, 1.0)


# Global config instance
CONFIG = MonitorConfig()

# Global console instance for rich formatting
console = Console()


def switch_to_finder():
    """Switch focus to Finder for distraction testing."""
    try:
        subprocess.run(["osascript", "-e", 'tell application "Finder" to activate'], check=True)
    except subprocess.CalledProcessError:
        console.print("[dim red]⚠️ Failed to switch to Finder[/dim red]")


def switch_to_ide():
    """Switch focus back to IDE (looks for common IDEs)."""
    ides = ["Cursor", "Visual Studio Code", "PyCharm", "Xcode", "IntelliJ IDEA"]
    for ide in ides:
        try:
            subprocess.run(
                ["osascript", "-e", f'tell application "{ide}" to activate'],
                check=True,
                capture_output=True
            )
            return
        except subprocess.CalledProcessError:
            continue
    console.print("[dim red]⚠️ Failed to switch back to IDE[/dim red]")


def setup_session_logging(data_dir: Path) -> Path:
    """Setup JSON logging for this monitoring session."""
    logs_dir = data_dir / "logs"
    logs_dir.mkdir(parents=True, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"monitoring_session_{timestamp}.json"

    # Initialize log file with session metadata
    session_data = {
        "session_start": datetime.now().isoformat(),
        "session_id": timestamp,
        "cycles": [],
    }

    with open(log_file, "w") as f:
        json.dump(session_data, f, indent=2)

    console.print(f"📝 [dim]Session logging to:[/dim] [blue]{log_file}[/blue]")
    return log_file


def log_cycle_data(log_file: Path, cycle_data: dict):
    """Append cycle data to the session log file."""
    try:
        # Read existing data
        with open(log_file, "r") as f:
            session_data = json.load(f)

        # Append new cycle data
        session_data["cycles"].append(cycle_data)

        # Write back to file
        with open(log_file, "w") as f:
            json.dump(session_data, f, indent=2)
    except Exception as e:
        console.print(f"[red]⚠️ Logging error: {e}[/red]")


def get_duration_from_user() -> float:
    """Get monitoring duration from user input."""
    while True:
        try:
            duration_str = input("How many minutes to run the feisty coding monitor? ")
            duration = float(duration_str)
            if duration > 0:
                return duration
            else:
                print("Please enter a positive number.")
        except ValueError:
            print("Please enter a valid number.")


def format_summary_stats(stats: dict) -> str:
    """Format summary statistics for display."""
    total_calls = stats["llm_calls"] + stats["api_calls_saved"]
    reduction_pct = (
        (stats["api_calls_saved"] / total_calls * 100) if total_calls > 0 else 0
    )

    return f"""
📊 MONITORING SUMMARY:
   • Total cycles: {stats["total_cycles"]}
   • LLM calls: {stats["llm_calls"]}
   • API calls saved: {stats["api_calls_saved"]}
   • API call reduction: {reduction_pct:.0f}%
   • Duration: {stats["elapsed_minutes"]:.1f} minutes
   • Estimated cost saved: ${stats["api_calls_saved"] * 0.01:.2f}
"""


def run_feisty_monitor(duration_minutes: float) -> dict:
    """
    Run the feisty coding monitor with hash change detection and voice alerts.

    REQUIREMENTS MET:
    ✅ Screenshot capture every 5s of active window using Peekaboo/screencapture fallback
    ✅ GPT-5 vision analysis for distraction detection with structured JSON output
    ✅ Voice alerts (macOS say) when distraction detected
    ✅ Hash change detection to optimize API costs (only analyze when screen changes)
    ✅ Things3 integration for dynamic task context
    ✅ Image compression pipeline with graceful fallback
    ✅ Session logging to JSON files with detailed metrics

    QUIRKS & FIXES APPLIED:
    - Peekaboo error handling: Returns exit code 0 on failure, requires stderr parsing
    - File size validation: Rejects screenshots <5KB as likely failed captures
    - Import path migration: Fixed basic_checker_hardcoded → basic_checker_tasklist
    - Compression fallback: Uses original screenshot when compression unavailable
    - Robust fallback: screencapture used when Peekaboo fails for any reason

    Args:
        duration_minutes: How long to run monitoring

    Returns:
        dict with statistics including total_cycles, llm_calls, api_calls_saved, elapsed_minutes
    """
    timer = IntervalTimer(CONFIG.MONITORING_INTERVAL_SECONDS)
    data_dir = Path(__file__).parent / "data" / "screenshots"
    data_dir.mkdir(parents=True, exist_ok=True)

    # Setup session logging
    log_file = setup_session_logging(data_dir)

    # Statistics tracking
    stats = {
        "total_cycles": 0,
        "llm_calls": 0,
        "api_calls_saved": 0,
        "elapsed_minutes": 0,
        "last_summary_time": time.time(),
        "error_count": 0,
    }

    last_hash = None
    last_cycle_time = time.time()
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)

    console.print(Panel.fit(
        f"🚀 [bold green]Starting feisty coding monitor[/bold green] for [bold cyan]{duration_minutes}[/bold cyan] minutes\n"
        f"📊 [dim]Summary reports every 1 minute[/dim]\n"
        f"📝 [dim]Detailed logs saved to JSON file[/dim]\n"
        f"[bold yellow]Press Ctrl+C to stop early[/bold yellow]",
        title="Feisty5 Monitor"
    ))

    try:
        while time.time() < end_time and stats["error_count"] < 5:
            timer.start_cycle()
            stats["total_cycles"] += 1

            # Focus switching for testing distraction detection
            if stats["total_cycles"] in [3, 8]:
                console.print(f"[dim yellow]🔄 Switching to Finder for cycle {stats['total_cycles']} (testing distraction detection)[/dim yellow]")
                switch_to_finder()
                time.sleep(1)  # Brief pause to ensure focus switch

            # Calculate time since last cycle
            current_time = time.time()
            time_since_last = current_time - last_cycle_time
            last_cycle_time = current_time

            # OPTIMIZED: Capture to memory first for hash calculation
            import tempfile

            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
                temp_path = tmp_file.name

            # Capture screenshot to temporary location
            screenshot_data = capture_active_window_screenshot(
                output_dir=Path(temp_path).parent, compress=CONFIG.SCREENSHOT_COMPRESS
            )

            # Switch back to IDE after taking screenshot in test cycles
            if stats["total_cycles"] in [3, 8]:
                console.print(f"[dim yellow]🔄 Switching back to IDE after screenshot[/dim yellow]")
                switch_to_ide()

            if screenshot_data and "path" in screenshot_data:
                temp_screenshot_path = screenshot_data["path"]

                if temp_screenshot_path and Path(temp_screenshot_path).exists():
                    # Choose file for hash detection based on config
                    hash_file = temp_screenshot_path
                    if (
                        CONFIG.HASH_DETECTION_ON_COMPRESSED
                        and "compressed_path" in screenshot_data
                    ):
                        hash_file = screenshot_data["compressed_path"]

                    # Hash change detection
                    current_hash = calculate_perceptual_hash(hash_file)
                else:
                    stats["error_count"] += 1
                    console.print(f"[bold red]Cycle {stats['total_cycles']}: ❌ Invalid screenshot path[/bold red]: [dim]{temp_screenshot_path}[/dim]")
                    if stats["error_count"] >= 5:
                        console.print("[bold red]⛔ Too many errors (5), stopping monitor[/bold red]")
                        break
                    continue

                # Calculate change percentage for better reporting
                if last_hash is not None:
                    change_pct = get_change_percentage(current_hash, last_hash)
                else:
                    change_pct = 100.0  # First screenshot

                # Convert threshold percentage to hamming distance (rough approximation)
                threshold_hamming = int(
                    CONFIG.HASH_CHANGE_THRESHOLD * 64
                )  # 64 bits in dhash
                if last_hash is None or has_significant_change(
                    current_hash, last_hash, threshold_hamming
                ):
                    stats["llm_calls"] += 1
                    console.print()  # Add spacing between cycles

                    # Make ANALYZING cycles pop with a panel for high visibility
                    console.print(Panel.fit(
                        f"🔍 [bold yellow]ANALYZING[/bold yellow] - [bold cyan]{change_pct:.1f}% change detected[/bold cyan]\n"
                        f"📸 [dim]{temp_screenshot_path}[/dim]",
                        title=f"Cycle {stats['total_cycles']} - LLM Analysis",
                        border_style="yellow"
                    ))

                    # Save files based on config
                    timestamp = screenshot_data.get("timestamp", "unknown")
                    permanent_path = data_dir / f"{timestamp}.png"

                    # Choose file for LLM analysis based on config
                    analysis_file = temp_screenshot_path
                    if (
                        CONFIG.LLM_ANALYZE_COMPRESSED
                        and "compressed_path" in screenshot_data
                    ):
                        analysis_file = screenshot_data["compressed_path"]
                        # Save compressed file to permanent location
                        import shutil

                        shutil.move(analysis_file, permanent_path)
                    else:
                        # Save original file to permanent location
                        import shutil

                        shutil.move(temp_screenshot_path, permanent_path)
                        analysis_file = permanent_path

                    # Run LLM analysis on chosen file with debug data
                    analysis_start = time.time()
                    analysis = analyze_distraction(
                        str(analysis_file), include_debug_data=True
                    )
                    analysis_time = time.time() - analysis_start

                    # Warning for slow LLM calls
                    if analysis_time > 20:
                        console.print(f"   [bold red]⚠️ SLOW LLM CALL: {analysis_time:.1f}s (>20s)[/bold red]")

                    # Show LLM analysis summary (full details in logs)
                    decision = (
                        "DISTRACTED"
                        if analysis.get("distracted_decision")
                        else "FOCUSED"
                    )
                    reasoning = analysis.get("reasoning", "No reasoning provided")
                    decision_color = "red" if analysis.get("distracted_decision") else "green"
                    console.print(f"   🤖 [dim]LLM Analysis[/dim] [dim]({analysis_time:.1f}s)[/dim]: [{decision_color}]{decision}[/{decision_color}] - [dim]{reasoning[:100]}...[/dim]")

                    # Voice alert if distracted
                    voice_time = 0
                    if (
                        analysis.get("distracted_decision", False)
                        and CONFIG.VOICE_ALERTS_ENABLED
                    ):
                        reasoning = analysis.get("reasoning", "User appears distracted")
                        voice_start = time.time()
                        alert_message = generate_kind_voice_message(reasoning)
                        voice_time = time.time() - voice_start

                        # Warning for slow voice generation
                        if voice_time > 20:
                            console.print(f"   [bold red]⚠️ SLOW VOICE GENERATION: {voice_time:.1f}s (>20s)[/bold red]")

                        speak(alert_message, rate=CONFIG.VOICE_RATE)
                        console.print(f"   🔊 [bold yellow]VOICE ALERT[/bold yellow] [dim]({voice_time:.1f}s)[/dim]: [yellow]{alert_message}[/yellow]")
                        stats["llm_calls"] += 1  # Voice generation also uses LLM
                    else:
                        console.print("   ✅ [green]Focused on task[/green]")

                    # Log cycle data with full debug information
                    cycle_data = {
                        "cycle_number": stats["total_cycles"],
                        "timestamp": datetime.now().isoformat(),
                        "action": "analyze",
                        "change_percentage": change_pct,
                        "time_since_last_cycle": time_since_last,
                        "screenshot_path": str(permanent_path),
                        "analysis_time": analysis_time,
                        "voice_time": voice_time,
                        "llm_analysis": analysis,
                        "stats": {
                            "llm_calls": stats["llm_calls"],
                            "api_calls_saved": stats["api_calls_saved"],
                        },
                    }
                    log_cycle_data(log_file, cycle_data)

                    # Cleanup based on config
                    if CONFIG.CLEANUP_TEMP_FILES:
                        try:
                            Path(temp_screenshot_path).unlink()
                        except OSError:
                            pass

                        # Clean up compressed file if we're not keeping originals
                        if (
                            not CONFIG.KEEP_ORIGINAL_AFTER_COMPRESSION
                            and "compressed_path" in screenshot_data
                        ):
                            try:
                                Path(screenshot_data["compressed_path"]).unlink()
                            except OSError:
                                pass

                else:
                    stats["api_calls_saved"] += 1
                    console.print()  # Add spacing between cycles
                    console.print(f"[bold blue]Cycle {stats['total_cycles']}[/bold blue]: ⚡ [dim]SKIPPED[/dim] ([cyan]{change_pct:.1f}% change[/cyan]) [dim]- file not saved ({time_since_last:.1f}s)[/dim]")
                    console.print(f"[dim]📸 {temp_screenshot_path} (discarded)[/dim]")
                    # Clean up temporary file since no significant change
                    try:
                        Path(temp_screenshot_path).unlink()
                    except OSError:
                        pass

                    # Log skipped cycle data
                    cycle_data = {
                        "cycle_number": stats["total_cycles"],
                        "timestamp": datetime.now().isoformat(),
                        "action": "skip",
                        "change_percentage": change_pct,
                        "time_since_last_cycle": time_since_last,
                        "reason": "below_threshold",
                        "stats": {
                            "llm_calls": stats["llm_calls"],
                            "api_calls_saved": stats["api_calls_saved"],
                        },
                    }
                    log_cycle_data(log_file, cycle_data)

                last_hash = current_hash
            else:
                stats["error_count"] += 1
                console.print()  # Add spacing between cycles
                console.print(f"[bold red]Cycle {stats['total_cycles']}: ❌ Screenshot failed[/bold red] [dim]({time_since_last:.1f}s)[/dim]")
                if stats["error_count"] >= 5:
                    console.print("[bold red]⛔ Too many errors (5), stopping monitor[/bold red]")
                    break

                # Log failed screenshot
                cycle_data = {
                    "cycle_number": stats["total_cycles"],
                    "timestamp": datetime.now().isoformat(),
                    "action": "error",
                    "time_since_last_cycle": time_since_last,
                    "error": "screenshot_capture_failed",
                    "stats": {
                        "llm_calls": stats["llm_calls"],
                        "api_calls_saved": stats["api_calls_saved"],
                    },
                }
                log_cycle_data(log_file, cycle_data)

            # Print summary every minute
            current_time = time.time()
            if current_time - stats["last_summary_time"] >= 60:
                stats["elapsed_minutes"] = (current_time - start_time) / 60
                console.print(Panel.fit(
                    f"📊 [bold cyan]1-MINUTE SUMMARY[/bold cyan]\n"
                    f"   LLM calls so far: [yellow]{stats['llm_calls']}[/yellow]\n"
                    f"   API calls saved: [green]{stats['api_calls_saved']}[/green]\n"
                    f"   Errors: [red]{stats['error_count']}[/red]\n"
                    f"   Estimated cost: [cyan]${stats['llm_calls'] * 0.01:.2f}[/cyan]",
                    border_style="blue"
                ))
                stats["last_summary_time"] = current_time

            timer.wait_for_next()

    except KeyboardInterrupt:
        console.print("\n[bold red]⛔ Monitoring stopped by user[/bold red]")

    # Final statistics
    stats["elapsed_minutes"] = (time.time() - start_time) / 60

    # Add session summary to log file
    try:
        with open(log_file, "r") as f:
            session_data = json.load(f)

        session_data["session_end"] = datetime.now().isoformat()
        session_data["final_stats"] = stats

        with open(log_file, "w") as f:
            json.dump(session_data, f, indent=2)

        console.print(f"📝 [dim]Session log completed:[/dim] [blue]{log_file}[/blue]")
    except Exception as e:
        console.print(f"[red]⚠️ Failed to write session summary: {e}[/red]")

    return stats


def run_for_claude():
    """Run monitor for Claude testing with 1 minute duration and enhanced output."""
    console.print(Panel.fit(
        "🤖 [bold green]Claude Testing Mode[/bold green]\n"
        "Duration: [cyan]1 minute[/cyan]\n"
        "Features: [dim]Focus switching, Error handling, Enhanced output[/dim]",
        title="Feisty5 Monitor - Claude Mode"
    ))

    stats = run_feisty_monitor(1.0)

    # Enhanced final summary
    console.print(Panel.fit(
        format_summary_stats(stats),
        title="Final Results",
        border_style="green"
    ))
    console.print("✅ [bold green]Claude testing completed![/bold green]")


if __name__ == "__main__":
    # Check for claude mode
    if len(sys.argv) > 1 and sys.argv[1] == "claude":
        run_for_claude()
        sys.exit(0)

    console.print("🎯 [bold blue]Feisty Coding Monitor[/bold blue]")
    console.print("[dim]Features: Hash change detection + Voice alerts + Cost tracking[/dim]")
    console.print()

    # Check for command line argument for duration
    if len(sys.argv) > 1:
        try:
            duration = float(sys.argv[1])
            console.print(f"[green]Using duration from command line: {duration} minutes[/green]")
        except ValueError:
            console.print("[red]Invalid duration argument, falling back to interactive input[/red]")
            duration = get_duration_from_user()
    else:
        duration = get_duration_from_user()

    stats = run_feisty_monitor(duration)

    console.print(Panel.fit(
        format_summary_stats(stats),
        title="Final Results",
        border_style="green"
    ))
    console.print("✅ [bold green]Monitoring completed![/bold green]")
