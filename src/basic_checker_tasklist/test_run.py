import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from basic_checker_tasklist.detector import analyze_distraction
from basic_checker_tasklist.speaker import speak
from will_detector.screenshot import capture_active_window_screenshot


def test_single_run():
    """Test a single iteration of the monitoring system."""
    data_dir = Path(__file__).parent / "data" / "screenshots"
    data_dir.mkdir(parents=True, exist_ok=True)

    print("Testing coding feisty distraction detector...")

    # Capture screenshot with compression
    screenshot_data = capture_active_window_screenshot(
        output_dir=data_dir, compress=True
    )

    if screenshot_data and "path" in screenshot_data:
        # Use compressed path if available, otherwise fall back to original
        analysis_path = screenshot_data.get("compressed_path") or screenshot_data["path"]
        print(f"✓ Screenshot captured: {screenshot_data['path']}")
        if screenshot_data.get("compressed_path"):
            print(f"✓ Compressed version: {screenshot_data['compressed_path']}")
        else:
            print("⚠️ Compression failed, using original screenshot")

        # Analyze for distraction
        print("Analyzing screenshot for distraction...")
        analysis = analyze_distraction(analysis_path)

        print(f"✓ Analysis result: {analysis}")

        # Test voice alert
        if analysis.get("distracted_decision", False):
            alert_message = "Focus on feisty coding! Get back to work."[:100]
            speak(alert_message, rate=173)
            print(f"🔊 Voice alert triggered: {alert_message}")
        else:
            print("✓ No distraction detected - user appears focused on coding")

        return True
    else:
        print("✗ Screenshot capture failed")
        return False


if __name__ == "__main__":
    success = test_single_run()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
