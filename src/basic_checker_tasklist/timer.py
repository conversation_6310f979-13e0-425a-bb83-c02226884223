import time

class IntervalTimer:
    """Maintains precise intervals regardless of processing time."""
    
    def __init__(self, interval_seconds: float = 5.0):
        self.interval_seconds = interval_seconds
        self.cycle_start_time = None
    
    def start_cycle(self) -> None:
        """Mark the start of a processing cycle."""
        self.cycle_start_time = time.time()
    
    def wait_for_next(self) -> None:
        """Sleep for remaining time to reach target interval."""
        if self.cycle_start_time is None:
            return
        
        elapsed = time.time() - self.cycle_start_time
        remaining = self.interval_seconds - elapsed
        
        if remaining > 0:
            time.sleep(remaining)
    
    def get_actual_interval(self) -> float:
        """Return actual time elapsed since cycle start."""
        if self.cycle_start_time is None:
            return 0.0
        return time.time() - self.cycle_start_time