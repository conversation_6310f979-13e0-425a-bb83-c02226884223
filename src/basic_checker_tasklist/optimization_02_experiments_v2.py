#!/usr/bin/env python3
"""
LLM Optimization Experiments V2 for Coding Feisty Checker
Tests optimization strategies within gpt-5-nano's constraints.
"""

import base64
import io
import json
import os
import time
from dataclasses import dataclass
from pathlib import Path
from typing import List

from openai import OpenAI
from PIL import Image

# Load environment variables
env_file = Path(__file__).parent.parent.parent / ".env"
if env_file.exists():
    with open(env_file) as f:
        for line in f:
            if line.strip() and not line.startswith("#"):
                if "=" in line:
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value


@dataclass
class TestResult:
    """Result from a single test."""

    config_name: str
    test_file: str
    expected: bool
    actual: bool
    correct: bool
    reasoning: str
    latency: float
    prompt_tokens: int
    completion_tokens: int
    image_kb: float
    error: str = ""


class OptimizationExperiments:
    """Run LLM optimization experiments."""

    def __init__(self):
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.test_dir = (
            Path(__file__).parent.parent.parent
            / "tests/basic_checker_hardcoded/test_data"
        )
        self.results_dir = Path(__file__).parent / "experiment_results"
        self.results_dir.mkdir(exist_ok=True)

        # Test cases
        self.test_cases = {
            "focused_ide_1.png": False,
            "focused_ide_2.png": False,
            "focused_terminal.png": False,
            "focused_task_complete.png": False,
            "distracted_whatsapp_1.png": True,
            "distracted_whatsapp_2.png": True,
        }

    def resize_image(self, path: Path, max_size: int) -> bytes:
        """Resize image keeping aspect ratio."""
        with Image.open(path) as img:
            # Calculate new size
            ratio = max_size / max(img.size)
            if ratio < 1:
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.Resampling.LANCZOS)

            buffer = io.BytesIO()
            img.save(buffer, format="PNG", optimize=True)
            return buffer.getvalue()

    def binarize_image(self, path: Path) -> bytes:
        """Convert to black and white."""
        with Image.open(path) as img:
            img = img.convert("L")
            img = img.point(lambda p: 255 if p > 128 else 0, "1")
            buffer = io.BytesIO()
            img.save(buffer, format="PNG", optimize=True)
            return buffer.getvalue()

    def test_config(
        self, name: str, prompt: str, image_data: bytes, expected: bool, test_file: str
    ) -> TestResult:
        """Test a single configuration."""
        encoded = base64.b64encode(image_data).decode()

        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{encoded}"},
                    },
                ],
            }
        ]

        schema = {
            "type": "object",
            "properties": {
                "distracted_decision": {"type": "boolean"},
                "reasoning": {"type": "string"},
            },
            "required": ["distracted_decision", "reasoning"],
            "additionalProperties": False,
        }

        try:
            start = time.time()
            response = self.client.chat.completions.create(
                model="gpt-5-nano",
                messages=messages,
                response_format={
                    "type": "json_schema",
                    "json_schema": {"name": "analysis", "schema": schema},
                },
            )
            latency = time.time() - start

            result = json.loads(response.choices[0].message.content)

            return TestResult(
                config_name=name,
                test_file=test_file,
                expected=expected,
                actual=result["distracted_decision"],
                correct=result["distracted_decision"] == expected,
                reasoning=result["reasoning"],
                latency=latency,
                prompt_tokens=response.usage.prompt_tokens if response.usage else 0,
                completion_tokens=response.usage.completion_tokens
                if response.usage
                else 0,
                image_kb=len(image_data) / 1024,
                error="",
            )
        except Exception as e:
            return TestResult(
                config_name=name,
                test_file=test_file,
                expected=expected,
                actual=False,
                correct=False,
                reasoning="",
                latency=0,
                prompt_tokens=0,
                completion_tokens=0,
                image_kb=len(image_data) / 1024,
                error=str(e)[:100],
            )

    def run_experiments(self):
        """Run all experiment variations."""

        # Define prompts to test
        prompts = {
            "original": "Does it look like the user is working on coding feisty or are they getting distracted? Analyze the screenshot and determine if they appear focused on development work related to coding/programming or if they seem distracted by other activities like social media, entertainment, browsing unrelated content, etc. The hardest part of your task is that the primary thing the user get's distracted by is 'other valid tasks'. You'll need concrete evidence that they're coding or doing something directly relevant to feisty. You should e.g., ask the image whether the word feisty can be seen IN the image.",
            "concise": "Is user coding on feisty project? Look for 'feisty' in IDE/terminal. Reply briefly: focused or distracted.",
            "minimal": "Feisty coding visible? Yes=focused, No=distracted. One sentence reason.",
            "structured": "Check: 1) Is 'feisty' visible? 2) Coding tools shown? 3) Social media/entertainment? Reply: focused/distracted + brief reason.",
            "direct": "Screenshot shows feisty coding work or distraction? Answer with brief reasoning.",
        }

        all_results = []

        print("🧪 Running Optimization Experiments")
        print("=" * 60)

        # Test each configuration
        configs = [
            # Baseline
            ("1_baseline", prompts["original"], "original", None),
            # Prompt variations
            ("2_concise", prompts["concise"], "original", None),
            ("3_minimal", prompts["minimal"], "original", None),
            ("4_structured", prompts["structured"], "original", None),
            ("5_direct", prompts["direct"], "original", None),
            # Resolution tests with minimal prompt
            ("6_res_1024", prompts["minimal"], "resize", 1024),
            ("7_res_800", prompts["minimal"], "resize", 800),
            ("8_res_640", prompts["minimal"], "resize", 640),
            ("9_res_512", prompts["minimal"], "resize", 512),
            # Binarization tests
            ("10_binary", prompts["minimal"], "binary", None),
            ("11_binary_800", prompts["minimal"], "binary_resize", 800),
            # Combined optimizations
            ("12_optimal_v1", prompts["concise"], "resize", 800),
            ("13_optimal_v2", prompts["minimal"], "resize", 640),
            ("14_optimal_v3", prompts["direct"], "resize", 512),
            ("15_aggressive", prompts["minimal"], "binary_resize", 640),
        ]

        for config_name, prompt, processing, param in configs:
            print(f"\n📊 Testing: {config_name}")
            print(f"   Processing: {processing}, Param: {param}")

            config_results = []

            for test_file, expected in self.test_cases.items():
                print(f"   • {test_file}...", end=" ")

                # Load and process image
                img_path = self.test_dir / test_file

                if processing == "original":
                    img_data = img_path.read_bytes()
                elif processing == "resize":
                    img_data = self.resize_image(img_path, param)
                elif processing == "binary":
                    img_data = self.binarize_image(img_path)
                elif processing == "binary_resize":
                    img_data = self.resize_image(img_path, param)
                    # Convert resized to binary
                    temp_img = Image.open(io.BytesIO(img_data))
                    temp_img = temp_img.convert("L")
                    temp_img = temp_img.point(lambda p: 255 if p > 128 else 0, "1")
                    buffer = io.BytesIO()
                    temp_img.save(buffer, format="PNG", optimize=True)
                    img_data = buffer.getvalue()
                else:
                    img_data = img_path.read_bytes()

                # Run test
                result = self.test_config(
                    config_name, prompt, img_data, expected, test_file
                )
                config_results.append(result)
                all_results.append(result)

                # Print quick result
                if result.error:
                    print("❌ ERROR")
                else:
                    status = "✅" if result.correct else "❌"
                    print(
                        f"{status} {result.latency:.1f}s, {result.completion_tokens} tokens, {result.image_kb:.0f}KB"
                    )

            # Config summary
            if config_results and not all(r.error for r in config_results):
                valid = [r for r in config_results if not r.error]
                accuracy = sum(1 for r in valid if r.correct) / len(valid) * 100
                avg_latency = sum(r.latency for r in valid) / len(valid)
                avg_tokens = sum(r.completion_tokens for r in valid) / len(valid)
                avg_size = sum(r.image_kb for r in valid) / len(valid)

                print(
                    f"   📈 Summary: {accuracy:.0f}% accurate, {avg_latency:.1f}s, {avg_tokens:.0f} tokens, {avg_size:.0f}KB"
                )

        # Save and display results
        self.save_results(all_results)
        self.print_summary(all_results)

    def save_results(self, results: List[TestResult]):
        """Save results to JSON."""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = self.results_dir / f"results_{timestamp}.json"

        data = {
            "timestamp": timestamp,
            "results": [
                {
                    "config": r.config_name,
                    "file": r.test_file,
                    "expected": r.expected,
                    "actual": r.actual,
                    "correct": r.correct,
                    "reasoning": r.reasoning[:200],
                    "latency": r.latency,
                    "prompt_tokens": r.prompt_tokens,
                    "completion_tokens": r.completion_tokens,
                    "image_kb": r.image_kb,
                    "error": r.error,
                }
                for r in results
            ],
        }

        with open(output_file, "w") as f:
            json.dump(data, f, indent=2)

        print(f"\n💾 Results saved to: {output_file}")

    def print_summary(self, results: List[TestResult]):
        """Print final summary."""
        print("\n" + "=" * 60)
        print("🏆 FINAL SUMMARY")
        print("=" * 60)

        # Group by config
        from collections import defaultdict

        configs = defaultdict(list)
        for r in results:
            if not r.error:
                configs[r.config_name].append(r)

        # Calculate metrics
        metrics = []
        for name, config_results in configs.items():
            if config_results:
                accuracy = sum(1 for r in config_results if r.correct) / len(
                    config_results
                )
                avg_latency = sum(r.latency for r in config_results) / len(
                    config_results
                )
                avg_tokens = sum(r.completion_tokens for r in config_results) / len(
                    config_results
                )
                avg_size = sum(r.image_kb for r in config_results) / len(config_results)

                # Score: prioritize accuracy, then speed
                score = (accuracy * 100) - (avg_latency * 2)

                metrics.append(
                    {
                        "name": name,
                        "accuracy": accuracy,
                        "latency": avg_latency,
                        "tokens": avg_tokens,
                        "size_kb": avg_size,
                        "score": score,
                    }
                )

        # Sort by score
        metrics.sort(key=lambda x: x["score"], reverse=True)

        # Print top configs
        print("\nTop Configurations (by accuracy - latency):")
        print(
            f"{'Config':<20} {'Accuracy':<10} {'Latency':<10} {'Tokens':<10} {'Size':<10}"
        )
        print("-" * 60)

        for m in metrics[:5]:
            print(
                f"{m['name']:<20} {m['accuracy']:.0%}  {m['latency']:.1f}s  {m['tokens']:.0f}  {m['size_kb']:.0f}KB"
            )

        # Compare to baseline
        baseline = next((m for m in metrics if "baseline" in m["name"]), None)
        if baseline and metrics:
            best = metrics[0]
            if best["name"] != baseline["name"]:
                print("\n📊 Best vs Baseline:")
                print(
                    f"  Baseline: {baseline['latency']:.1f}s, {baseline['tokens']:.0f} tokens"
                )
                print(
                    f"  Best ({best['name']}): {best['latency']:.1f}s, {best['tokens']:.0f} tokens"
                )
                print(
                    f"  Improvement: {(baseline['latency'] - best['latency']) / baseline['latency'] * 100:.0f}% faster"
                )
                print(
                    f"  Token reduction: {(baseline['tokens'] - best['tokens']) / baseline['tokens'] * 100:.0f}%"
                )


if __name__ == "__main__":
    print("🚀 LLM Optimization Experiments V2")
    print("Testing various optimizations for Coding Feisty Checker")
    print()

    exp = OptimizationExperiments()
    exp.run_experiments()

    print("\n✅ Experiments complete!")
