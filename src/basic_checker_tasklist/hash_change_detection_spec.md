# Timer and Hash Change Detection Optimization Plan

## Problem Statement
- Current monitoring loop has variable intervals due to processing time
- LLM analysis runs on every screenshot, wasting API calls on identical screens
- Need consistent 5-second intervals and change detection to skip unchanged screenshots

## Solution Architecture

### 1. timer.py - Dynamic Interval Management
**Purpose**: Maintain precise 5-second intervals regardless of processing time

**Implementation**:
```python
class IntervalTimer:
    def __init__(self, interval_seconds: float = 5.0)
    def start_cycle(self) -> None  # Mark start of processing cycle
    def wait_for_next(self) -> None  # Sleep remaining time to hit interval
    def get_actual_interval(self) -> float  # Return actual time elapsed
```

**Logic**:
- Track cycle start time with `time.time()`
- Calculate remaining sleep time: `interval - (current_time - start_time)`
- Only sleep if positive remainder, otherwise log overrun

### 2. hash_change_threshold.py - Screenshot Change Detection
**Purpose**: Skip LLM analysis if screenshot unchanged by >10%

**Method**: Perceptual hashing (imagehash library)
- Most robust for UI changes in 2025
- Handles minor animations/lighting changes
- Fast computation (~1ms per image)

**Implementation**:
```python
def calculate_perceptual_hash(image_path: str) -> str
def has_significant_change(current_hash: str, previous_hash: str, threshold: int = 4) -> bool
def get_change_percentage(current_hash: str, previous_hash: str) -> float
```

**Threshold Logic**:
- Hamming distance >4 bits = significant change
- ~10% change threshold in practice
- Store last_hash in monitor.py state

## Integration Plan

### Modified monitor.py Flow:
1. `timer.start_cycle()`
2. Capture screenshot + compression
3. Calculate perceptual hash
4. Compare with previous hash
5. If significant change: run LLM analysis
6. If no change: skip LLM, log "no change detected"
7. `timer.wait_for_next()`

## Dependencies
- `imagehash` library for perceptual hashing
- No additional dependencies for timer (uses built-in time module)

## Performance Benefits
- Consistent 5-second intervals
- ~80% reduction in LLM API calls (typical screen change frequency)
- Sub-millisecond hash comparison vs multi-second LLM analysis