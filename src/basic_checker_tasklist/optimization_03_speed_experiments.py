#!/usr/bin/env python3
"""
Fast Parallel LLM Speed Experiments for Coding Feisty Checker
Focuses on configurations likely to achieve <10s latency.
"""

import base64
import io
import json
import os
import time
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor, TimeoutError, as_completed
from dataclasses import dataclass
from pathlib import Path
from typing import List, Optional

from openai import OpenAI
from PIL import Image

# Load environment variables
env_file = Path(__file__).parent.parent.parent / ".env"
if env_file.exists():
    with open(env_file) as f:
        for line in f:
            if line.strip() and not line.startswith("#"):
                if "=" in line:
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value


@dataclass
class Config:
    """Experiment configuration."""

    name: str
    model: str
    prompt: str
    max_resolution: Optional[int] = None  # Max dimension (width or height)
    use_binarization: bool = False
    timeout_seconds: float = 10.0


@dataclass
class Result:
    """Test result."""

    config: str
    test_file: str
    success: bool
    correct: bool = False
    latency: float = 0.0
    tokens: int = 0
    image_kb: float = 0.0
    reasoning: str = ""
    error: str = ""
    timed_out: bool = False


class SpeedExperiments:
    """Fast parallel experiments focused on speed."""

    def __init__(self, max_workers: int = 6):
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.test_dir = (
            Path(__file__).parent.parent.parent
            / "tests/basic_checker_hardcoded/test_data"
        )
        self.max_workers = max_workers

        # Test files with expected results
        self.tests = {
            "focused_ide_1.png": False,
            "focused_terminal.png": False,
            "distracted_whatsapp_1.png": True,
            "distracted_whatsapp_2.png": True,
        }

        # Define optimized prompts (learned from previous experiments)
        self.prompts = {
            "ultra_short": "Coding feisty? Yes/No + reason.",
            "minimal": "Is 'feisty' visible in IDE/terminal? Brief answer.",
            "direct": "Feisty project work visible? One sentence.",
            "binary": "User coding feisty=focused, else=distracted. Decide.",
        }

    def process_image(
        self, path: Path, max_res: Optional[int], binarize: bool
    ) -> bytes:
        """Process image with optimizations."""
        with Image.open(path) as img:
            # Resize if specified
            if max_res and max(img.size) > max_res:
                ratio = max_res / max(img.size)
                new_size = tuple(int(d * ratio) for d in img.size)
                img = img.resize(new_size, Image.Resampling.LANCZOS)

            # Binarize if specified
            if binarize:
                img = img.convert("L")
                img = img.point(lambda p: 255 if p > 128 else 0, "1")

            # Save to bytes
            buffer = io.BytesIO()
            img.save(buffer, format="PNG", optimize=True)
            return buffer.getvalue()

    def run_single_test(self, config: Config, test_file: str, expected: bool) -> Result:
        """Run a single test with timeout."""
        result = Result(config=config.name, test_file=test_file, success=False)

        try:
            # Process image
            img_path = self.test_dir / test_file
            img_data = self.process_image(
                img_path, config.max_resolution, config.use_binarization
            )
            result.image_kb = len(img_data) / 1024

            # Prepare API call
            encoded = base64.b64encode(img_data).decode()
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": config.prompt},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/png;base64,{encoded}"},
                        },
                    ],
                }
            ]

            schema = {
                "type": "object",
                "properties": {
                    "distracted_decision": {"type": "boolean"},
                    "reasoning": {"type": "string"},
                },
                "required": ["distracted_decision", "reasoning"],
                "additionalProperties": False,
            }

            # Make API call with timing
            start = time.time()
            response = self.client.chat.completions.create(
                model=config.model,
                messages=messages,
                response_format={
                    "type": "json_schema",
                    "json_schema": {"name": "analysis", "schema": schema},
                },
                # Note: timeout parameter not supported here, handled at thread level
            )
            result.latency = time.time() - start

            # Parse response
            data = json.loads(response.choices[0].message.content)
            result.correct = data["distracted_decision"] == expected
            result.reasoning = data["reasoning"][:100]  # Truncate for display
            result.tokens = response.usage.total_tokens if response.usage else 0
            result.success = True

        except TimeoutError:
            result.timed_out = True
            result.error = "Timeout"
        except Exception as e:
            result.error = str(e)[:50]

        return result

    def run_config_tests(self, config: Config) -> List[Result]:
        """Run all tests for a configuration in parallel."""
        results = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tests for this config
            futures = {
                executor.submit(
                    self.run_single_test, config, test_file, expected
                ): test_file
                for test_file, expected in self.tests.items()
            }

            # Collect results with timeout handling
            try:
                for future in as_completed(futures, timeout=config.timeout_seconds * 2):
                    try:
                        result = future.result(timeout=1)
                        results.append(result)
                    except Exception as e:
                        test_file = futures[future]
                        results.append(
                            Result(
                                config=config.name,
                                test_file=test_file,
                                success=False,
                                error=str(e)[:50],
                            )
                        )
            except TimeoutError:
                # Handle remaining futures that timed out
                for future in futures:
                    if not future.done():
                        test_file = futures[future]
                        results.append(
                            Result(
                                config=config.name,
                                test_file=test_file,
                                success=False,
                                timed_out=True,
                                error="Batch timeout",
                            )
                        )
                        future.cancel()

        return results

    def get_speed_configs(self) -> List[Config]:
        """Get configurations optimized for speed (<10s target)."""
        configs = []

        # Test different prompts with original resolution
        for prompt_name, prompt_text in self.prompts.items():
            configs.append(
                Config(
                    name=f"prompt_{prompt_name}", model="gpt-5-nano", prompt=prompt_text
                )
            )

        # Test resolution reduction with best prompt
        best_prompt = self.prompts["ultra_short"]
        for resolution in [800, 640, 512]:
            configs.append(
                Config(
                    name=f"res_{resolution}",
                    model="gpt-5-nano",
                    prompt=best_prompt,
                    max_resolution=resolution,
                )
            )

        # Test binarization
        configs.append(
            Config(
                name="binary_full",
                model="gpt-5-nano",
                prompt=best_prompt,
                use_binarization=True,
            )
        )

        configs.append(
            Config(
                name="binary_640",
                model="gpt-5-nano",
                prompt=best_prompt,
                max_resolution=640,
                use_binarization=True,
            )
        )

        # Aggressive optimization combos
        configs.append(
            Config(
                name="aggressive_v1",
                model="gpt-5-nano",
                prompt=self.prompts["minimal"],
                max_resolution=640,
            )
        )

        configs.append(
            Config(
                name="aggressive_v2",
                model="gpt-5-nano",
                prompt=self.prompts["binary"],
                max_resolution=512,
                use_binarization=True,
            )
        )

        # Placeholder for alternative models (easy to add)
        if os.getenv("ENABLE_ALT_MODELS", "false").lower() == "true":
            # Example for adding Gemini/Qwen when available
            configs.append(
                Config(
                    name="gemini_test",
                    model="gemini-flash",  # Replace with actual model name
                    prompt=best_prompt,
                    max_resolution=640,
                )
            )

        return configs

    def run_all_experiments(self):
        """Run all experiments in parallel batches."""
        configs = self.get_speed_configs()
        all_results = []

        print("⚡ Fast Parallel Speed Experiments")
        print(f"   Testing {len(configs)} configs on {len(self.tests)} images")
        print(f"   Max {self.max_workers} parallel workers, 10s timeout per test")
        print("=" * 60)

        # Run each configuration
        for config in configs:
            print(f"\n📊 {config.name}: ", end="", flush=True)

            start = time.time()
            results = self.run_config_tests(config)
            batch_time = time.time() - start

            # Quick summary
            successful = [r for r in results if r.success]
            if successful:
                accuracy = sum(1 for r in successful if r.correct) / len(successful)
                avg_latency = sum(r.latency for r in successful) / len(successful)
                avg_kb = sum(r.image_kb for r in successful) / len(successful)

                status = "✅" if avg_latency < 10 else "⚠️"
                print(
                    f"{status} {avg_latency:.1f}s, {accuracy:.0%} acc, {avg_kb:.0f}KB ({batch_time:.1f}s total)"
                )

                # Show per-file results if interesting
                if avg_latency < 5:  # Very fast!
                    for r in successful:
                        print(f"     • {r.test_file}: {r.latency:.1f}s")
            else:
                print(f"❌ All failed ({batch_time:.1f}s)")

            all_results.extend(results)

        # Final analysis
        self.print_analysis(all_results)
        self.save_results(all_results)

    def print_analysis(self, results: List[Result]):
        """Analyze and print results."""
        print("\n" + "=" * 60)
        print("🏆 SPEED OPTIMIZATION RESULTS")
        print("=" * 60)

        # Group by config
        from collections import defaultdict

        by_config = defaultdict(list)
        for r in results:
            if r.success and not r.timed_out:
                by_config[r.config].append(r)

        # Calculate metrics
        metrics = []
        for config_name, config_results in by_config.items():
            if config_results:
                accuracy = sum(1 for r in config_results if r.correct) / len(
                    config_results
                )
                avg_latency = sum(r.latency for r in config_results) / len(
                    config_results
                )
                max_latency = max(r.latency for r in config_results)
                avg_tokens = sum(r.tokens for r in config_results if r.tokens) / len(
                    config_results
                )

                metrics.append(
                    {
                        "name": config_name,
                        "accuracy": accuracy,
                        "avg_latency": avg_latency,
                        "max_latency": max_latency,
                        "tokens": avg_tokens,
                        "under_10s": max_latency < 10,
                        "under_5s": max_latency < 5,
                    }
                )

        # Sort by speed
        metrics.sort(key=lambda x: x["avg_latency"])

        # Show configs under 10s
        fast_configs = [m for m in metrics if m["under_10s"]]
        if fast_configs:
            print("\n✅ Configurations under 10 seconds:")
            print(f"{'Config':<20} {'Avg':<8} {'Max':<8} {'Accuracy':<10} {'Tokens'}")
            print("-" * 56)
            for m in fast_configs[:10]:
                marker = "🚀" if m["under_5s"] else "✅"
                print(
                    f"{marker} {m['name']:<18} {m['avg_latency']:.1f}s  {m['max_latency']:.1f}s  {m['accuracy']:.0%}  {m['tokens']:.0f}"
                )

        # Best overall (accuracy + speed balance)
        if metrics:
            # Score: accuracy weight 60%, speed weight 40%
            for m in metrics:
                m["score"] = (m["accuracy"] * 60) + (
                    (10 - min(m["avg_latency"], 10)) * 4
                )

            metrics.sort(key=lambda x: x["score"], reverse=True)
            best = metrics[0]

            print(f"\n🎯 RECOMMENDED CONFIG: {best['name']}")
            print(f"   • Average latency: {best['avg_latency']:.1f}s")
            print(f"   • Max latency: {best['max_latency']:.1f}s")
            print(f"   • Accuracy: {best['accuracy']:.0%}")
            print(f"   • Tokens: {best['tokens']:.0f}")

            # Implementation suggestion
            print("\n📝 To implement in detector.py:")
            if "prompt_" in best["name"]:
                prompt_key = best["name"].replace("prompt_", "")
                print(f'   prompt = "{self.prompts.get(prompt_key, "")}"')
            if "res_" in best["name"]:
                res = best["name"].replace("res_", "")
                print(f"   # Resize images to max {res}px before encoding")
            if "binary" in best["name"]:
                print("   # Apply binarization before encoding")

    def save_results(self, results: List[Result]):
        """Save results to JSON."""
        output_file = (
            Path(__file__).parent
            / "experiment_results"
            / f"speed_results_{time.strftime('%Y%m%d_%H%M%S')}.json"
        )
        output_file.parent.mkdir(exist_ok=True)

        data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "results": [
                {
                    "config": r.config,
                    "test_file": r.test_file,
                    "success": r.success,
                    "correct": r.correct,
                    "latency": r.latency,
                    "tokens": r.tokens,
                    "image_kb": r.image_kb,
                    "reasoning": r.reasoning,
                    "error": r.error,
                    "timed_out": r.timed_out,
                }
                for r in results
            ],
        }

        with open(output_file, "w") as f:
            json.dump(data, f, indent=2)

        print(f"\n💾 Results saved to: {output_file}")


if __name__ == "__main__":
    print("🚀 LLM Speed Optimization Experiments")
    print("   Focus: Configurations likely to achieve <10s latency")
    print()

    # Run with parallel execution
    exp = SpeedExperiments(max_workers=6)
    exp.run_all_experiments()

    print("\n✅ Speed experiments complete!")
