#!/usr/bin/env python3
"""
LLM Optimization Experiments for Coding Feisty Checker
Tests various optimization strategies to reduce API latency while maintaining accuracy.
"""

import base64
import io
import json
import os
import time
from dataclasses import dataclass
from pathlib import Path
from typing import List, <PERSON><PERSON>

from openai import OpenAI
from PIL import Image

# Load environment variables
env_file = Path(__file__).parent.parent.parent / ".env"
if env_file.exists():
    with open(env_file) as f:
        for line in f:
            if line.strip() and not line.startswith("#"):
                if "=" in line:
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value


@dataclass
class ExperimentConfig:
    """Configuration for each experiment variation."""

    name: str
    model: str
    prompt_template: str
    max_tokens: int | None
    image_resolution: Tuple[int, int] | None  # None = original
    use_compression: bool
    compression_method: str = "binarize"


@dataclass
class ExperimentResult:
    """Results from a single experiment run."""

    config_name: str
    screenshot_path: str
    expected_result: bool
    actual_result: bool
    reasoning: str
    latency_seconds: float
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    image_size_bytes: int
    success: bool
    error: str = ""


class LLMOptimizationExperiments:
    """Run optimization experiments for LLM distraction detection."""

    def __init__(self):
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.test_data_dir = (
            Path(__file__).parent.parent.parent
            / "tests/basic_checker_hardcoded/test_data"
        )
        self.results_dir = Path(__file__).parent / "experiment_results"
        self.results_dir.mkdir(exist_ok=True)

        # Test cases with expected results
        self.test_cases = [
            ("focused_ide_1.png", False),  # Not distracted
            ("focused_ide_2.png", False),
            ("focused_terminal.png", False),
            ("focused_task_complete.png", False),
            ("distracted_whatsapp_1.png", True),  # Distracted
            ("distracted_whatsapp_2.png", True),
        ]

    # ----- Image Processing Helpers -----

    def _resize_image(self, image_path: Path, target_size: Tuple[int, int]) -> bytes:
        """Resize image to target dimensions."""
        with Image.open(image_path) as img:
            # Keep aspect ratio
            img.thumbnail(target_size, Image.Resampling.LANCZOS)
            buffer = io.BytesIO()
            img.save(buffer, format="PNG")
            return buffer.getvalue()

    def _compress_image(self, image_path: Path, method: str) -> bytes:
        """Apply compression to image."""
        if method == "binarize":
            # Simple binarization
            with Image.open(image_path) as img:
                img = img.convert("L")  # Grayscale
                # Apply threshold
                threshold = 128
                img = img.point(lambda p: 255 if p > threshold else 0, "1")
                buffer = io.BytesIO()
                img.save(buffer, format="PNG")
                return buffer.getvalue()
        else:
            # Return original
            return image_path.read_bytes()

    def _prepare_image(self, image_path: Path, config: ExperimentConfig) -> str:
        """Prepare image based on experiment config."""
        image_data = image_path.read_bytes()

        # Apply resizing if specified
        if config.image_resolution:
            image_data = self._resize_image(image_path, config.image_resolution)

        # Apply compression if specified
        if config.use_compression:
            image_data = self._compress_image(image_path, config.compression_method)

        # Encode to base64
        return base64.b64encode(image_data).decode()

    # ----- Experiment Configurations -----

    def get_experiment_configs(self) -> List[ExperimentConfig]:
        """Define all experiment variations to test."""

        # Prompt variations
        PROMPT_ORIGINAL = "Does it look like the user is working on coding feisty or are they getting distracted? Analyze the screenshot and determine if they appear focused on development work related to coding/programming or if they seem distracted by other activities like social media, entertainment, browsing unrelated content, etc. The hardest part of your task is that the primary thing the user get's distracted by is 'other valid tasks'. You'll need concrete evidence that they're coding or doing something directly relevant to feisty. You should e.g., ask the image whether the word feisty can be seen IN the image."

        PROMPT_SHORT = "Is the user coding on the feisty project or distracted? Look for evidence of coding work with 'feisty' visible, or signs of distraction like social media/entertainment."

        PROMPT_ULTRA_SHORT = (
            "Coding feisty or distracted? Check for IDE/terminal with 'feisty' visible."
        )

        PROMPT_STRUCTURED = "Task: Detect if user is working on 'feisty' coding project.\nLook for: IDE, terminal, code files with 'feisty' in view.\nDistracted if: social media, entertainment, unrelated browsing.\nDecide: focused or distracted?"

        configs = []

        # 1. Baseline - Current implementation
        configs.append(
            ExperimentConfig(
                name="1_baseline_current",
                model="gpt-5-nano",
                prompt_template=PROMPT_ORIGINAL,
                max_tokens=None,
                image_resolution=None,
                use_compression=False,
            )
        )

        # 2. Token limit experiments
        configs.append(
            ExperimentConfig(
                name="2_tokens_100",
                model="gpt-5-nano",
                prompt_template=PROMPT_ORIGINAL,
                max_tokens=100,
                image_resolution=None,
                use_compression=False,
            )
        )

        configs.append(
            ExperimentConfig(
                name="3_tokens_50",
                model="gpt-5-nano",
                prompt_template=PROMPT_ORIGINAL,
                max_tokens=50,
                image_resolution=None,
                use_compression=False,
            )
        )

        configs.append(
            ExperimentConfig(
                name="4_tokens_25",
                model="gpt-5-nano",
                prompt_template=PROMPT_ORIGINAL,
                max_tokens=25,
                image_resolution=None,
                use_compression=False,
            )
        )

        # 3. Prompt optimization experiments
        configs.append(
            ExperimentConfig(
                name="5_prompt_short",
                model="gpt-5-nano",
                prompt_template=PROMPT_SHORT,
                max_tokens=50,
                image_resolution=None,
                use_compression=False,
            )
        )

        configs.append(
            ExperimentConfig(
                name="6_prompt_ultra_short",
                model="gpt-5-nano",
                prompt_template=PROMPT_ULTRA_SHORT,
                max_tokens=25,
                image_resolution=None,
                use_compression=False,
            )
        )

        configs.append(
            ExperimentConfig(
                name="7_prompt_structured",
                model="gpt-5-nano",
                prompt_template=PROMPT_STRUCTURED,
                max_tokens=50,
                image_resolution=None,
                use_compression=False,
            )
        )

        # 4. Image resolution experiments
        configs.append(
            ExperimentConfig(
                name="8_resolution_1024",
                model="gpt-5-nano",
                prompt_template=PROMPT_SHORT,
                max_tokens=50,
                image_resolution=(1024, 768),
                use_compression=False,
            )
        )

        configs.append(
            ExperimentConfig(
                name="9_resolution_800",
                model="gpt-5-nano",
                prompt_template=PROMPT_SHORT,
                max_tokens=50,
                image_resolution=(800, 600),
                use_compression=False,
            )
        )

        configs.append(
            ExperimentConfig(
                name="10_resolution_640",
                model="gpt-5-nano",
                prompt_template=PROMPT_SHORT,
                max_tokens=50,
                image_resolution=(640, 480),
                use_compression=False,
            )
        )

        configs.append(
            ExperimentConfig(
                name="11_resolution_512",
                model="gpt-5-nano",
                prompt_template=PROMPT_SHORT,
                max_tokens=50,
                image_resolution=(512, 384),
                use_compression=False,
            )
        )

        # 5. Compression experiments
        configs.append(
            ExperimentConfig(
                name="12_compressed_binarize",
                model="gpt-5-nano",
                prompt_template=PROMPT_SHORT,
                max_tokens=50,
                image_resolution=None,
                use_compression=True,
                compression_method="binarize",
            )
        )

        # 6. Combined optimizations
        configs.append(
            ExperimentConfig(
                name="13_optimized_v1",
                model="gpt-5-nano",
                prompt_template=PROMPT_SHORT,
                max_tokens=50,
                image_resolution=(800, 600),
                use_compression=False,
            )
        )

        configs.append(
            ExperimentConfig(
                name="14_optimized_v2",
                model="gpt-5-nano",
                prompt_template=PROMPT_ULTRA_SHORT,
                max_tokens=25,
                image_resolution=(640, 480),
                use_compression=False,
            )
        )

        configs.append(
            ExperimentConfig(
                name="15_optimized_v3_aggressive",
                model="gpt-5-nano",
                prompt_template=PROMPT_ULTRA_SHORT,
                max_tokens=20,
                image_resolution=(512, 384),
                use_compression=True,
            )
        )

        # 7. Different models (if available)
        if os.getenv("ENABLE_GPT4O_TESTS", "false").lower() == "true":
            configs.append(
                ExperimentConfig(
                    name="16_model_gpt4o_mini",
                    model="gpt-4o-mini",
                    prompt_template=PROMPT_SHORT,
                    max_tokens=50,
                    image_resolution=(800, 600),
                    use_compression=False,
                )
            )

        return configs

    # ----- Experiment Execution -----

    def run_single_test(
        self, config: ExperimentConfig, image_path: Path, expected: bool
    ) -> ExperimentResult:
        """Run a single test with given configuration."""
        result = ExperimentResult(
            config_name=config.name,
            screenshot_path=str(image_path),
            expected_result=expected,
            actual_result=False,
            reasoning="",
            latency_seconds=0,
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            image_size_bytes=0,
            success=False,
        )

        try:
            # Prepare image
            encoded_image = self._prepare_image(image_path, config)
            result.image_size_bytes = len(encoded_image)

            # Build API request
            schema = {
                "type": "object",
                "properties": {
                    "distracted_decision": {"type": "boolean"},
                    "reasoning": {"type": "string"},
                },
                "required": ["distracted_decision", "reasoning"],
                "additionalProperties": False,
            }

            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": config.prompt_template},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{encoded_image}"
                            },
                        },
                    ],
                }
            ]

            # Time the API call
            start_time = time.time()

            kwargs = {
                "model": config.model,
                "messages": messages,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "distraction_analysis", "schema": schema},
                },
            }

            # Note: max_tokens not supported with structured outputs in gpt-5-nano
            # We'll simulate token limits via prompt engineering instead

            response = self.client.chat.completions.create(**kwargs)

            result.latency_seconds = time.time() - start_time

            # Parse response
            analysis = json.loads(response.choices[0].message.content)
            result.actual_result = analysis["distracted_decision"]
            result.reasoning = analysis["reasoning"]

            # Token usage
            if response.usage:
                result.prompt_tokens = response.usage.prompt_tokens
                result.completion_tokens = response.usage.completion_tokens
                result.total_tokens = response.usage.total_tokens

            result.success = True

        except Exception as e:
            result.error = str(e)
            result.success = False

        return result

    def run_all_experiments(self):
        """Run all experiment configurations on all test cases."""
        configs = self.get_experiment_configs()
        all_results = []

        print(
            f"🧪 Running {len(configs)} experiment configurations on {len(self.test_cases)} test cases"
        )
        print(f"   Total tests: {len(configs) * len(self.test_cases)}")
        print("=" * 60)

        for config in configs:
            print(f"\n📊 Testing: {config.name}")
            print(f"   Model: {config.model}, Max tokens: {config.max_tokens}")
            print(
                f"   Resolution: {config.image_resolution}, Compressed: {config.use_compression}"
            )

            config_results = []

            for image_file, expected_distracted in self.test_cases:
                image_path = self.test_data_dir / image_file

                print(f"   • Testing {image_file}...", end=" ")

                result = self.run_single_test(config, image_path, expected_distracted)
                config_results.append(result)
                all_results.append(result)

                # Quick summary
                if result.success:
                    accuracy = (
                        "✅" if result.actual_result == result.expected_result else "❌"
                    )
                    print(
                        f"{accuracy} {result.latency_seconds:.1f}s, {result.total_tokens} tokens"
                    )
                else:
                    print(f"❌ ERROR: {result.error[:50]}")

            # Config summary
            successful = [r for r in config_results if r.success]
            if successful:
                avg_latency = sum(r.latency_seconds for r in successful) / len(
                    successful
                )
                avg_tokens = sum(r.total_tokens for r in successful) / len(successful)
                accuracy = sum(
                    1 for r in successful if r.actual_result == r.expected_result
                ) / len(successful)

                print(
                    f"   📈 Summary: {accuracy:.0%} accuracy, {avg_latency:.1f}s avg, {avg_tokens:.0f} tokens avg"
                )

        # Save results
        self._save_results(all_results)
        self._print_final_summary(all_results)

        return all_results

    def _save_results(self, results: List[ExperimentResult]):
        """Save results to JSON file."""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = self.results_dir / f"experiment_results_{timestamp}.json"

        data = {
            "timestamp": timestamp,
            "results": [
                {
                    "config_name": r.config_name,
                    "screenshot": Path(r.screenshot_path).name,
                    "expected": r.expected_result,
                    "actual": r.actual_result,
                    "correct": r.actual_result == r.expected_result,
                    "reasoning": r.reasoning,
                    "latency_seconds": r.latency_seconds,
                    "prompt_tokens": r.prompt_tokens,
                    "completion_tokens": r.completion_tokens,
                    "total_tokens": r.total_tokens,
                    "image_size_bytes": r.image_size_bytes,
                    "success": r.success,
                    "error": r.error,
                }
                for r in results
            ],
        }

        with open(output_file, "w") as f:
            json.dump(data, f, indent=2)

        print(f"\n💾 Results saved to: {output_file}")

    def _print_final_summary(self, results: List[ExperimentResult]):
        """Print final summary comparing all configurations."""
        print("\n" + "=" * 60)
        print("🏆 FINAL SUMMARY - TOP CONFIGURATIONS")
        print("=" * 60)

        # Group by config
        from collections import defaultdict

        config_groups = defaultdict(list)
        for r in results:
            if r.success:
                config_groups[r.config_name].append(r)

        # Calculate metrics for each config
        config_metrics = []
        for config_name, config_results in config_groups.items():
            accuracy = sum(
                1 for r in config_results if r.actual_result == r.expected_result
            ) / len(config_results)
            avg_latency = sum(r.latency_seconds for r in config_results) / len(
                config_results
            )
            avg_tokens = sum(r.total_tokens for r in config_results) / len(
                config_results
            )

            # Score: weighted combination (accuracy is most important)
            score = (accuracy * 100) - (avg_latency * 2) - (avg_tokens / 100)

            config_metrics.append(
                {
                    "name": config_name,
                    "accuracy": accuracy,
                    "latency": avg_latency,
                    "tokens": avg_tokens,
                    "score": score,
                }
            )

        # Sort by score
        config_metrics.sort(key=lambda x: x["score"], reverse=True)

        # Print top 5
        print("\nTop 5 configurations by score (accuracy - latency - tokens):")
        print(
            f"{'Config':<30} {'Accuracy':<10} {'Latency':<10} {'Tokens':<10} {'Score':<10}"
        )
        print("-" * 70)

        for i, m in enumerate(config_metrics[:5], 1):
            print(
                f"{i}. {m['name']:<28} {m['accuracy']:.0%}  {m['latency']:.1f}s  {m['tokens']:.0f}  {m['score']:.1f}"
            )

        # Best for different priorities
        print("\n🎯 Best for specific needs:")
        fastest = min(config_metrics, key=lambda x: x["latency"])
        print(f"  Fastest: {fastest['name']} ({fastest['latency']:.1f}s)")

        most_accurate = max(config_metrics, key=lambda x: x["accuracy"])
        print(
            f"  Most Accurate: {most_accurate['name']} ({most_accurate['accuracy']:.0%})"
        )

        least_tokens = min(config_metrics, key=lambda x: x["tokens"])
        print(
            f"  Least Tokens: {least_tokens['name']} ({least_tokens['tokens']:.0f} tokens)"
        )

        # Calculate improvements vs baseline
        baseline = next((m for m in config_metrics if "baseline" in m["name"]), None)
        if baseline:
            print(f"\n📊 Improvements vs baseline ({baseline['name']}):")
            print(
                f"  Baseline: {baseline['accuracy']:.0%} accuracy, {baseline['latency']:.1f}s, {baseline['tokens']:.0f} tokens"
            )

            best = config_metrics[0]
            latency_improvement = (
                (baseline["latency"] - best["latency"]) / baseline["latency"] * 100
            )
            token_reduction = (
                (baseline["tokens"] - best["tokens"]) / baseline["tokens"] * 100
            )

            print(f"  Best overall: {best['name']}")
            print(
                f"    • Latency: {best['latency']:.1f}s ({latency_improvement:.0f}% faster)"
            )
            print(f"    • Tokens: {best['tokens']:.0f} ({token_reduction:.0f}% fewer)")
            print(
                f"    • Accuracy: {best['accuracy']:.0%} vs {baseline['accuracy']:.0%}"
            )


if __name__ == "__main__":
    print("🚀 LLM Optimization Experiments for Coding Feisty Checker")
    print("=" * 60)

    experiments = LLMOptimizationExperiments()
    results = experiments.run_all_experiments()

    print("\n✅ Experiments completed!")
