#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
test_cfg_notifications.py
Demonstrates using the notification module with a CFG object for configuration.
"""

from dataclasses import dataclass

from mac_notifications import MacNotifier, NotificationConfig, send_banner


@dataclass
class CFG:
    """Configuration singleton for notifications."""

    # Notification settings
    NOTIFICATION_TYPE: str = "banner"  # "banner" or "alert"
    DEFAULT_TITLE: str = "Will Detector"
    DEFAULT_SOUND: str = "Glass"
    DEFAULT_TIMEOUT: int = 5

    # Alert settings
    ALERT_ACTION_BUTTON: str = "View"
    ALERT_CLOSE_BUTTON: str = "Close"

    # Banner settings
    BANNER_TIMEOUT: int = 3

    # Priority settings
    HIGH_PRIORITY_SOUND: str = "Ping"
    CRITICAL_SOUND: str = "Sosumi"


def test_cfg_notifications():
    """Test notifications using CFG object."""

    cfg = CFG()

    print("Testing CFG-based Notifications...")
    print("=" * 40)

    # Test 1: Banner with CFG defaults
    print("\n1. Testing banner with CFG defaults...")
    config = NotificationConfig(
        notification_type=cfg.NOTIFICATION_TYPE,
        title=cfg.DEFAULT_TITLE,
        message="New message detected from William Hayes",
        subtitle="Slack",
        sound_name=cfg.DEFAULT_SOUND,
        timeout=cfg.BANNER_TIMEOUT,
    )

    notifier = MacNotifier(config)
    result1 = notifier.send()
    print(f"Result: {result1}")

    # Test 2: Alert with CFG settings
    print("\n2. Testing alert with CFG settings...")
    config = NotificationConfig(
        notification_type="alert",
        title=f"{cfg.DEFAULT_TITLE} Alert",
        message="Critical message from William Hayes detected!",
        subtitle="High Priority",
        action_button=cfg.ALERT_ACTION_BUTTON,
        close_button=cfg.ALERT_CLOSE_BUTTON,
    )

    notifier = MacNotifier(config)
    result2 = notifier.send()
    print(f"Result: {result2}")

    # Test 3: High priority notification
    print("\n3. Testing high priority notification...")
    config = NotificationConfig(
        notification_type="banner",
        title=cfg.DEFAULT_TITLE,
        message="URGENT: William Hayes message requires immediate attention!",
        subtitle="High Priority",
        sound_name=cfg.HIGH_PRIORITY_SOUND,
        timeout=cfg.BANNER_TIMEOUT,
    )

    notifier = MacNotifier(config)
    result3 = notifier.send()
    print(f"Result: {result3}")

    # Test 4: Using convenience functions with CFG
    print("\n4. Testing convenience functions with CFG...")
    result4 = send_banner(
        title=cfg.DEFAULT_TITLE,
        message="Convenience function test",
        subtitle="CFG Test",
        sound=cfg.DEFAULT_SOUND,
        timeout=cfg.BANNER_TIMEOUT,
    )
    print(f"Result: {result4}")

    print("\nCFG notification tests completed!")


if __name__ == "__main__":
    test_cfg_notifications()
