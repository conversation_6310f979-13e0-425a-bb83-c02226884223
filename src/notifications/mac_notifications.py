#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
mac_notifications.py
macOS notification system using osascript for native notifications.
Supports alerts, banners, and custom notification content.
"""

import json
import subprocess
from dataclasses import dataclass
from typing import Any, Dict, Optional


@dataclass
class NotificationConfig:
    """Configuration for macOS notifications."""

    # Notification type
    notification_type: str = (
        "banner"  # "banner" (auto-dismissing) or "alert" (modal/sticky)
    )

    # Content
    title: str = "Will Detector"
    message: str = "Notification"
    subtitle: Optional[str] = None

    # Sound
    sound_name: Optional[str] = None  # "default", "Glass", "Ping", etc.

    # Icon (optional)
    icon_path: Optional[str] = None

    # Actions (optional)
    action_button: Optional[str] = None
    close_button: str = "Close"

    # Timeout (for banners)
    timeout: int = 5  # seconds

    # Priority
    priority: str = "normal"  # "low", "normal", "high", "critical"


class MacNotifier:
    """macOS notification system using AppleScript."""

    def __init__(self, config: NotificationConfig):
        self.config = config

    def _build_apple_script(self) -> str:
        """Build AppleScript for native banner notification (non-modal, auto-dismissing)."""

        # Build notification parameters
        params = []
        params.append(f'with title "{self.config.title}"')

        # Add subtitle if provided
        if self.config.subtitle:
            params.append(f'subtitle "{self.config.subtitle}"')

        # Add sound if specified
        if self.config.sound_name:
            params.append(f'sound name "{self.config.sound_name}"')

        # Note: timeout parameter not supported in single-line AppleScript format
        # Banners will use system default timeout (auto-dismissing)

        # Build native notification (no "System Events" wrapper to avoid "Script Editor" branding)
        script = f'display notification "{self.config.message}" {" ".join(params)}'

        return script

    def _build_alert_script(self) -> str:
        """Build AppleScript for modal alert dialog (sticky, requires user interaction)."""

        # Build message with title and subtitle
        message = self.config.message
        if self.config.subtitle:
            message = f"{self.config.subtitle}\n\n{message}"

        # Build dialog parameters
        params = []
        params.append(f'with title "{self.config.title}"')

        # Add buttons
        buttons = []
        if self.config.action_button:
            buttons.append(self.config.action_button)
        buttons.append(self.config.close_button)

        buttons_str = ", ".join(f'"{btn}"' for btn in buttons)
        params.append(f"buttons {{{buttons_str}}}")

        # Add default button
        params.append(f'default button "{self.config.close_button}"')

        # Add icon if specified
        if self.config.icon_path:
            params.append(f'with icon file "{self.config.icon_path}"')

        # Build multi-line AppleScript for dialog
        script = f'''tell application "System Events"
    activate
    display dialog "{message}" {" ".join(params)}
end tell'''

        return script

    def send(self) -> Dict[str, Any]:
        """Send notification based on config."""

        try:
            if self.config.notification_type == "alert":
                script = self._build_alert_script()
            else:  # banner
                script = self._build_apple_script()

            # Execute AppleScript
            result = subprocess.run(
                ["osascript", "-e", script], capture_output=True, text=True, timeout=30
            )

            if result.returncode == 0:
                return {
                    "success": True,
                    "type": self.config.notification_type,
                    "title": self.config.title,
                    "message": self.config.message,
                    "response": result.stdout.strip() if result.stdout else None,
                }
            else:
                return {
                    "success": False,
                    "error": result.stderr.strip(),
                    "returncode": result.returncode,
                }

        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Notification timed out"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def send_banner(
        self,
        title: str,
        message: str,
        subtitle: Optional[str] = None,
        sound: Optional[str] = None,
        timeout: int = 5,
    ) -> Dict[str, Any]:
        """Quick banner notification."""
        config = NotificationConfig(
            notification_type="banner",
            title=title,
            message=message,
            subtitle=subtitle,
            sound_name=sound,
            timeout=timeout,
        )
        return MacNotifier(config).send()

    def send_alert(
        self,
        title: str,
        message: str,
        subtitle: Optional[str] = None,
        action_button: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Quick alert dialog."""
        config = NotificationConfig(
            notification_type="alert",
            title=title,
            message=message,
            subtitle=subtitle,
            action_button=action_button,
        )
        return MacNotifier(config).send()


# Convenience functions
def send_banner(
    title: str,
    message: str,
    subtitle: Optional[str] = None,
    sound: Optional[str] = None,
    timeout: int = 5,
) -> Dict[str, Any]:
    """Send a banner notification."""
    return MacNotifier(NotificationConfig()).send_banner(
        title, message, subtitle, sound, timeout
    )


def send_alert(
    title: str,
    message: str,
    subtitle: Optional[str] = None,
    action_button: Optional[str] = None,
) -> Dict[str, Any]:
    """Send an alert dialog."""
    return MacNotifier(NotificationConfig()).send_alert(
        title, message, subtitle, action_button
    )


if __name__ == "__main__":
    # Test the notification system

    print("Testing macOS Notifications...")
    print("=" * 40)

    # Test 1: Banner notification
    print("\n1. Testing banner notification...")
    result1 = send_banner(
        title="Will Detector",
        message="New message detected from William Hayes",
        subtitle="Slack",
        sound="Glass",
        timeout=3,
    )
    print(f"Result: {json.dumps(result1, indent=2)}")

    # Test 2: Alert dialog
    print("\n2. Testing alert dialog...")
    result2 = send_alert(
        title="Will Detector Alert",
        message="Critical message from William Hayes detected!",
        subtitle="High Priority",
        action_button="View",
    )
    print(f"Result: {json.dumps(result2, indent=2)}")

    # Test 3: Custom config
    print("\n3. Testing custom config...")
    config = NotificationConfig(
        notification_type="banner",
        title="Custom Test",
        message="This is a custom notification",
        subtitle="Testing",
        sound_name="Ping",
        timeout=2,
    )
    notifier = MacNotifier(config)
    result3 = notifier.send()
    print(f"Result: {json.dumps(result3, indent=2)}")

    print("\nNotification tests completed!")
