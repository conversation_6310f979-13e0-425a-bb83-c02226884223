#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
demo_both_types.py
Demonstrates both notification types with clear naming.
"""

import time

from mac_notifications import send_alert, send_banner


def demo_both_types():
    """Demo both notification types."""

    print("Demo: Both Notification Types")
    print("=" * 40)

    # 1. Banner (auto-dismissing, non-modal)
    print("\n1. Sending BANNER notification (auto-dismissing)...")
    print("   - Should appear briefly and disappear automatically")
    print("   - Won't block your workflow")
    print("   - Shows in notification center")

    result1 = send_banner(
        title="Will Detector",
        message="This is a BANNER notification - auto-dismissing",
        subtitle="Non-modal, non-blocking",
        sound="Glass",
    )
    print(f"   Result: {result1}")

    time.sleep(2)  # Wait a moment

    # 2. Alert (modal/sticky, requires interaction)
    print("\n2. Sending ALERT notification (modal/sticky)...")
    print("   - Will appear as a modal dialog")
    print("   - Requires user interaction to dismiss")
    print("   - Blocks workflow until dismissed")

    result2 = send_alert(
        title="Will Detector Alert",
        message="This is an ALERT notification - modal/sticky",
        subtitle="Requires user interaction",
        action_button="OK",
    )
    print(f"   Result: {result2}")

    print("\nDemo completed!")
    print("\nSummary:")
    print("- BANNER: Auto-dismissing, non-blocking")
    print("- ALERT: Modal/sticky, requires interaction")


if __name__ == "__main__":
    demo_both_types()
