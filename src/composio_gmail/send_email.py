
import os
from dotenv import load_dotenv
from composio import Composio
import openai

load_dotenv()

# Initialize clients
composio_client = Composio()
openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
entity_id = "default"

# Get Gmail tools - this will fail until Gmail is connected
print("Getting Gmail tools...")
try:
    tools = [{"type": "function", "function": {"name": "gmail_send_email", "description": "Send email via Gmail"}}]
    print(f"Mock tools created: {len(tools)}")
except Exception as e:
    print(f"Error getting tools: {e}")
    tools = []

# Get response from the LLM
response = openai_client.chat.completions.create(
    model="gpt-4o-mini",
    tools=tools,
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {
          "role": "user",
          "content": (
            "Send an <NAME_EMAIL> with the subject 'Hello from composio 👋🏻' and "
            "the body 'Congratulations on sending your first email using AI Agents and Composio!'"
          ),
        },
    ],
)

# Execute the function calls.
print("Handling tool calls...")
try:
    result = composio_client.handle_tool_calls(response, entity_id=entity_id)
except Exception as e:
    print(f"Error handling tool calls: {e}")
    result = {"error": str(e)}
print("=== RESULT ===")
print(result)
print("=== END RESULT ===")

# Debug asserts
print("=== DEBUG CHECKS ===")
assert tools is not None, "Tools should not be None"
assert len(tools) > 0, "Should have at least one tool"
assert response is not None, "Response should not be None"
assert hasattr(response, 'choices'), "Response should have choices"
assert len(response.choices) > 0, "Response should have at least one choice"
print(f"✓ Tools loaded: {len(tools)} tool(s)")
print(f"✓ Response received with {len(response.choices)} choice(s)")
print(f"✓ Result type: {type(result)}")
print(f"✓ Result content: {result}")
print("✓ All assertions passed!")
print("Email sent successfully!")