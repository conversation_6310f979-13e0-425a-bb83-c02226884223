import cv2
import numpy as np
import os


def enhance_contrast(input_path: str, output_path: str) -> str:
    """
    Enhance image contrast using CLAHE (Contrast Limited Adaptive Histogram Equalization)
    
    Args:
        input_path: Path to input image
        output_path: Path for output image
        
    Returns:
        Path to the created output image
    """
    # Read image
    img = cv2.imread(input_path)
    
    # Convert to LAB color space for better contrast enhancement
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    
    # Split into L, A, B channels
    l, a, b = cv2.split(lab)
    
    # Apply CLAHE to the L channel
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
    l_enhanced = clahe.apply(l)
    
    # Merge channels back
    lab_enhanced = cv2.merge([l_enhanced, a, b])
    
    # Convert back to BGR
    enhanced = cv2.cvtColor(lab_enhanced, cv2.COLOR_LAB2BGR)
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save the enhanced image
    cv2.imwrite(output_path, enhanced)
    
    return output_path