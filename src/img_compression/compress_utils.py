import os
from pathlib import Path
from .binarization import binarize_image
from .bilateral_filtering import bilateral_filter_image


def compress_screenshot_fast(input_path: str, output_dir: str = None, method: str = "binarize") -> dict:
    """
    Fast screenshot compression optimized for OCR and minimal latency.
    
    Args:
        input_path: Path to input screenshot
        output_dir: Output directory (defaults to same directory as input with _compressed suffix)
        method: Compression method ("binarize" or "bilateral")
        
    Returns:
        dict with compression results including paths, sizes, and performance metrics
    """
    input_path = Path(input_path)
    
    if output_dir is None:
        output_dir = input_path.parent / "compressed"
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate output filename
    stem = input_path.stem
    suffix = "_binarized" if method == "binarize" else "_bilateral"
    output_path = output_dir / f"{stem}{suffix}.png"
    
    # Get original size
    original_size = input_path.stat().st_size
    
    # Apply compression based on method
    import time
    start_time = time.time()
    
    if method == "binarize":
        result_path = binarize_image(str(input_path), str(output_path))
    elif method == "bilateral":
        result_path = bilateral_filter_image(str(input_path), str(output_path))
    else:
        raise ValueError(f"Unknown compression method: {method}")
    
    compression_time = time.time() - start_time
    
    # Get compressed size
    compressed_size = Path(result_path).stat().st_size
    
    # Calculate metrics
    compression_ratio = (original_size - compressed_size) / original_size
    size_reduction_mb = (original_size - compressed_size) / (1024 * 1024)
    
    return {
        "input_path": str(input_path),
        "output_path": result_path,
        "method": method,
        "original_size_bytes": original_size,
        "compressed_size_bytes": compressed_size,
        "compression_ratio": compression_ratio,
        "size_reduction_mb": size_reduction_mb,
        "compression_time_sec": compression_time,
        "success": True
    }


def compress_for_will_detector(screenshot_path: str) -> dict:
    """
    Optimized compression specifically for Will detector use case.
    Uses binarization for fastest processing and best OCR results.
    """
    return compress_screenshot_fast(
        input_path=screenshot_path,
        method="binarize"  # Fastest method with best OCR score
    )