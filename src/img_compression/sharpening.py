import cv2
import numpy as np
import os


def sharpen_image(input_path: str, output_path: str) -> str:
    """
    Sharpen image to improve text clarity using unsharp masking
    
    Args:
        input_path: Path to input image
        output_path: Path for output image
        
    Returns:
        Path to the created output image
    """
    # Read image
    img = cv2.imread(input_path)
    
    # Create Gaussian blur
    blurred = cv2.GaussianBlur(img, (0, 0), 2.0)
    
    # Unsharp masking: original + amount * (original - blurred)
    amount = 1.5  # Sharpening strength
    sharpened = cv2.addWeighted(img, 1 + amount, blurred, -amount, 0)
    
    # Alternative: Using a sharpening kernel
    # kernel = np.array([[-1,-1,-1],
    #                    [-1, 9,-1],
    #                    [-1,-1,-1]])
    # sharpened = cv2.filter2D(img, -1, kernel)
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save the sharpened image
    cv2.imwrite(output_path, sharpened)
    
    return output_path