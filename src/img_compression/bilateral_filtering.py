import cv2
import numpy as np
import os


def bilateral_filter_image(input_path: str, output_path: str) -> str:
    """
    Apply bilateral filter to reduce noise while preserving edges
    
    Args:
        input_path: Path to input image
        output_path: Path for output image
        
    Returns:
        Path to the created output image
    """
    # Read image
    img = cv2.imread(input_path)
    
    # Apply bilateral filter
    # d=5: diameter of pixel neighborhood (smaller = faster)
    # sigmaColor=80: filter sigma in color space (larger = farther colors mix)
    # sigmaSpace=80: filter sigma in coordinate space (larger = farther pixels influence)
    filtered = cv2.bilateralFilter(img, d=5, sigmaColor=80, sigmaSpace=80)
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save the filtered image
    cv2.imwrite(output_path, filtered)
    
    return output_path