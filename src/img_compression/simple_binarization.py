from PIL import Image
import numpy as np
import os


def binarize_image_pil(input_path: str, output_path: str) -> str:
    """
    Simple binarization using PIL as fallback
    """
    # Open image and convert to grayscale
    img = Image.open(input_path).convert('L')
    
    # Convert to numpy array
    img_array = np.array(img)
    
    # Apply threshold (using Otsu-like approach)
    threshold = np.mean(img_array)
    binary_array = np.where(img_array > threshold, 255, 0).astype(np.uint8)
    
    # Convert back to PIL image
    binary_img = Image.fromarray(binary_array)
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save the binary image
    binary_img.save(output_path)
    
    return output_path