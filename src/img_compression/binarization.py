import cv2
import numpy as np
from PIL import Image
import os


def binarize_image(input_path: str, output_path: str) -> str:
    """
    Convert image to binary (black and white) for better OCR performance
    
    Args:
        input_path: Path to input image
        output_path: Path for output image
        
    Returns:
        Path to the created output image
    """
    # Read image
    img = cv2.imread(input_path)
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Apply Otsu's thresholding for automatic optimal threshold
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Save the binary image
    cv2.imwrite(output_path, binary)
    
    return output_path