import json
import sys
import time
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Use optimized detector for 30-40% faster response (9-12s vs 13-17s)
# from basic_checker_hardcoded.detector_optimized import analyze_distraction
# Or use original for maximum accuracy:
from basic_checker_hardcoded.detector import analyze_distraction
from basic_checker_hardcoded.hash_change_detection import (
    calculate_perceptual_hash,
    get_change_percentage,
    has_significant_change,
)
from basic_checker_hardcoded.speaker import speak
from basic_checker_hardcoded.timer import IntervalTimer
from basic_checker_hardcoded.voice_generator import generate_kind_voice_message
from will_detector.screenshot import capture_active_window_screenshot


@dataclass
class MonitorConfig:
    """Configuration for the coding feisty monitor."""

    # Screenshot settings
    SCREENSHOT_RESOLUTION: str = "full"  # "full", "half", "quarter"
    SCREENSHOT_COMPRESS: bool = True
    SCREENSHOT_COMPRESSION_METHOD: str = "binarize"  # "binarize", "bilateral"

    # Analysis settings
    LLM_ANALYZE_COMPRESSED: bool = (
        False  # True = use compressed image, False = use original
    )
    HASH_DETECTION_ON_COMPRESSED: bool = (
        False  # True = hash compressed, False = hash original
    )

    # Storage settings
    KEEP_ORIGINAL_AFTER_COMPRESSION: bool = (
        True  # True = keep both, False = delete original
    )
    CLEANUP_TEMP_FILES: bool = True

    # Performance settings
    MONITORING_INTERVAL_SECONDS: float = 5.0
    HASH_CHANGE_THRESHOLD: float = 0.1  # 10% change threshold

    # Voice settings
    VOICE_ALERTS_ENABLED: bool = True
    VOICE_RATE: int = 173  # 120% of default 144 WPM

    def get_screenshot_scale(self) -> float:
        """Get scale factor for screenshot resolution."""
        scale_map = {"full": 1.0, "half": 0.5, "quarter": 0.25}
        return scale_map.get(self.SCREENSHOT_RESOLUTION, 1.0)


# Global config instance
CONFIG = MonitorConfig()


def setup_session_logging(data_dir: Path) -> Path:
    """Setup JSON logging for this monitoring session."""
    logs_dir = data_dir / "logs"
    logs_dir.mkdir(parents=True, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"monitoring_session_{timestamp}.json"

    # Initialize log file with session metadata
    session_data = {
        "session_start": datetime.now().isoformat(),
        "session_id": timestamp,
        "cycles": [],
    }

    with open(log_file, "w") as f:
        json.dump(session_data, f, indent=2)

    print(f"📝 Session logging to: {log_file}")
    return log_file


def log_cycle_data(log_file: Path, cycle_data: dict):
    """Append cycle data to the session log file."""
    try:
        # Read existing data
        with open(log_file, "r") as f:
            session_data = json.load(f)

        # Append new cycle data
        session_data["cycles"].append(cycle_data)

        # Write back to file
        with open(log_file, "w") as f:
            json.dump(session_data, f, indent=2)
    except Exception as e:
        print(f"⚠️ Logging error: {e}")


def get_duration_from_user() -> float:
    """Get monitoring duration from user input."""
    while True:
        try:
            duration_str = input("How many minutes to run the feisty coding monitor? ")
            duration = float(duration_str)
            if duration > 0:
                return duration
            else:
                print("Please enter a positive number.")
        except ValueError:
            print("Please enter a valid number.")


def format_summary_stats(stats: dict) -> str:
    """Format summary statistics for display."""
    total_calls = stats["llm_calls"] + stats["api_calls_saved"]
    reduction_pct = (
        (stats["api_calls_saved"] / total_calls * 100) if total_calls > 0 else 0
    )

    return f"""
📊 MONITORING SUMMARY:
   • Total cycles: {stats["total_cycles"]}
   • LLM calls: {stats["llm_calls"]}
   • API calls saved: {stats["api_calls_saved"]}
   • API call reduction: {reduction_pct:.0f}%
   • Duration: {stats["elapsed_minutes"]:.1f} minutes
   • Estimated cost saved: ${stats["api_calls_saved"] * 0.01:.2f}
"""


def run_feisty_monitor(duration_minutes: float) -> dict:
    """
    Run the feisty coding monitor with hash change detection and voice alerts.

    Args:
        duration_minutes: How long to run monitoring

    Returns:
        dict with statistics
    """
    timer = IntervalTimer(CONFIG.MONITORING_INTERVAL_SECONDS)
    data_dir = Path(__file__).parent / "data" / "screenshots"
    data_dir.mkdir(parents=True, exist_ok=True)

    # Setup session logging
    log_file = setup_session_logging(data_dir)

    # Statistics tracking
    stats = {
        "total_cycles": 0,
        "llm_calls": 0,
        "api_calls_saved": 0,
        "elapsed_minutes": 0,
        "last_summary_time": time.time(),
    }

    last_hash = None
    last_cycle_time = time.time()
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)

    print(f"🚀 Starting feisty coding monitor for {duration_minutes} minutes...")
    print("📊 Summary reports every 1 minute")
    print("📝 Detailed logs saved to JSON file")
    print("Press Ctrl+C to stop early")
    print("=" * 50)

    try:
        while time.time() < end_time:
            timer.start_cycle()
            stats["total_cycles"] += 1

            # Calculate time since last cycle
            current_time = time.time()
            time_since_last = current_time - last_cycle_time
            last_cycle_time = current_time

            # OPTIMIZED: Capture to memory first for hash calculation
            import tempfile

            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
                temp_path = tmp_file.name

            # Capture screenshot to temporary location
            screenshot_data = capture_active_window_screenshot(
                output_dir=Path(temp_path).parent, compress=CONFIG.SCREENSHOT_COMPRESS
            )

            if screenshot_data and "path" in screenshot_data:
                temp_screenshot_path = screenshot_data["path"]

                if temp_screenshot_path and Path(temp_screenshot_path).exists():
                    # Choose file for hash detection based on config
                    hash_file = temp_screenshot_path
                    if (
                        CONFIG.HASH_DETECTION_ON_COMPRESSED
                        and "compressed_path" in screenshot_data
                    ):
                        hash_file = screenshot_data["compressed_path"]

                    # Hash change detection
                    current_hash = calculate_perceptual_hash(hash_file)
                else:
                    print(
                        f"Cycle {stats['total_cycles']}: ❌ Invalid screenshot path: {temp_screenshot_path}"
                    )
                    continue

                # Calculate change percentage for better reporting
                if last_hash is not None:
                    change_pct = get_change_percentage(current_hash, last_hash)
                else:
                    change_pct = 100.0  # First screenshot

                # Convert threshold percentage to hamming distance (rough approximation)
                threshold_hamming = int(
                    CONFIG.HASH_CHANGE_THRESHOLD * 64
                )  # 64 bits in dhash
                if last_hash is None or has_significant_change(
                    current_hash, last_hash, threshold_hamming
                ):
                    stats["llm_calls"] += 1
                    print(
                        f"Cycle {stats['total_cycles']}: 🔍 ANALYZING ({change_pct:.1f}% change) ({time_since_last:.1f}s)"
                    )

                    # Save files based on config
                    timestamp = screenshot_data.get("timestamp", "unknown")
                    permanent_path = data_dir / f"{timestamp}.png"

                    # Choose file for LLM analysis based on config
                    analysis_file = temp_screenshot_path
                    if (
                        CONFIG.LLM_ANALYZE_COMPRESSED
                        and "compressed_path" in screenshot_data
                    ):
                        analysis_file = screenshot_data["compressed_path"]
                        # Save compressed file to permanent location
                        import shutil

                        shutil.move(analysis_file, permanent_path)
                    else:
                        # Save original file to permanent location
                        import shutil

                        shutil.move(temp_screenshot_path, permanent_path)
                        analysis_file = permanent_path

                    # Run LLM analysis on chosen file with debug data
                    analysis_start = time.time()
                    analysis = analyze_distraction(
                        str(analysis_file), include_debug_data=True
                    )
                    analysis_time = time.time() - analysis_start

                    # Show LLM analysis summary (full details in logs)
                    decision = (
                        "DISTRACTED"
                        if analysis.get("distracted_decision")
                        else "FOCUSED"
                    )
                    reasoning = analysis.get("reasoning", "No reasoning provided")
                    print(
                        f"   🤖 LLM Analysis ({analysis_time:.1f}s): {decision} - {reasoning[:100]}..."
                    )

                    # Voice alert if distracted
                    voice_time = 0
                    if (
                        analysis.get("distracted_decision", False)
                        and CONFIG.VOICE_ALERTS_ENABLED
                    ):
                        reasoning = analysis.get("reasoning", "User appears distracted")
                        voice_start = time.time()
                        alert_message = generate_kind_voice_message(reasoning)
                        voice_time = time.time() - voice_start
                        speak(alert_message, rate=CONFIG.VOICE_RATE)
                        print(f"   🔊 VOICE ALERT ({voice_time:.1f}s): {alert_message}")
                        stats["llm_calls"] += 1  # Voice generation also uses LLM
                    else:
                        print("   ✅ Focused on coding")

                    # Log cycle data with full debug information
                    cycle_data = {
                        "cycle_number": stats["total_cycles"],
                        "timestamp": datetime.now().isoformat(),
                        "action": "analyze",
                        "change_percentage": change_pct,
                        "time_since_last_cycle": time_since_last,
                        "screenshot_path": str(permanent_path),
                        "analysis_time": analysis_time,
                        "voice_time": voice_time,
                        "llm_analysis": analysis,
                        "stats": {
                            "llm_calls": stats["llm_calls"],
                            "api_calls_saved": stats["api_calls_saved"],
                        },
                    }
                    log_cycle_data(log_file, cycle_data)

                    # Cleanup based on config
                    if CONFIG.CLEANUP_TEMP_FILES:
                        try:
                            Path(temp_screenshot_path).unlink()
                        except OSError:
                            pass

                        # Clean up compressed file if we're not keeping originals
                        if (
                            not CONFIG.KEEP_ORIGINAL_AFTER_COMPRESSION
                            and "compressed_path" in screenshot_data
                        ):
                            try:
                                Path(screenshot_data["compressed_path"]).unlink()
                            except OSError:
                                pass

                else:
                    stats["api_calls_saved"] += 1
                    print(
                        f"Cycle {stats['total_cycles']}: ⚡ SKIPPED ({change_pct:.1f}% change) - file not saved ({time_since_last:.1f}s)"
                    )
                    # Clean up temporary file since no significant change
                    try:
                        Path(temp_screenshot_path).unlink()
                    except OSError:
                        pass

                    # Log skipped cycle data
                    cycle_data = {
                        "cycle_number": stats["total_cycles"],
                        "timestamp": datetime.now().isoformat(),
                        "action": "skip",
                        "change_percentage": change_pct,
                        "time_since_last_cycle": time_since_last,
                        "reason": "below_threshold",
                        "stats": {
                            "llm_calls": stats["llm_calls"],
                            "api_calls_saved": stats["api_calls_saved"],
                        },
                    }
                    log_cycle_data(log_file, cycle_data)

                last_hash = current_hash
            else:
                print(
                    f"Cycle {stats['total_cycles']}: ❌ Screenshot failed ({time_since_last:.1f}s)"
                )

                # Log failed screenshot
                cycle_data = {
                    "cycle_number": stats["total_cycles"],
                    "timestamp": datetime.now().isoformat(),
                    "action": "error",
                    "time_since_last_cycle": time_since_last,
                    "error": "screenshot_capture_failed",
                    "stats": {
                        "llm_calls": stats["llm_calls"],
                        "api_calls_saved": stats["api_calls_saved"],
                    },
                }
                log_cycle_data(log_file, cycle_data)

            # Print summary every minute
            current_time = time.time()
            if current_time - stats["last_summary_time"] >= 60:
                stats["elapsed_minutes"] = (current_time - start_time) / 60
                print("\n" + "📊 1-MINUTE SUMMARY:")
                print(f"   LLM calls so far: {stats['llm_calls']}")
                print(f"   API calls saved: {stats['api_calls_saved']}")
                print(f"   Estimated cost: ${stats['llm_calls'] * 0.01:.2f}")
                print("=" * 50)
                stats["last_summary_time"] = current_time

            timer.wait_for_next()

    except KeyboardInterrupt:
        print("\n⛔ Monitoring stopped by user")

    # Final statistics
    stats["elapsed_minutes"] = (time.time() - start_time) / 60

    # Add session summary to log file
    try:
        with open(log_file, "r") as f:
            session_data = json.load(f)

        session_data["session_end"] = datetime.now().isoformat()
        session_data["final_stats"] = stats

        with open(log_file, "w") as f:
            json.dump(session_data, f, indent=2)

        print(f"📝 Session log completed: {log_file}")
    except Exception as e:
        print(f"⚠️ Failed to write session summary: {e}")

    return stats


if __name__ == "__main__":
    print("🎯 Feisty Coding Monitor")
    print("Features: Hash change detection + Voice alerts + Cost tracking")
    print()

    duration = get_duration_from_user()
    stats = run_feisty_monitor(duration)

    print(format_summary_stats(stats))
    print("✅ Monitoring completed!")
