# Coding Feisty Checker - File Organization

## Core Files
- `detector.py` - Original implementation (13-17s latency)
- `detector_optimized.py` - **Production optimized version (9-12s latency)**
- `detector_optimized.md` - Full documentation for optimized detector

## Optimization Journey (Chronological)
Files are named with `optimization_XX_` prefix for chronological sorting:

1. **`optimization_01_experiments.py`**
   - First attempt at optimization
   - Tested max_tokens parameter (failed - not supported)
   - Tested various prompt lengths and image resolutions
   - Sequential execution, very slow

2. **`optimization_02_experiments_v2.py`**
   - Simplified approach without max_tokens
   - More systematic testing of configurations
   - Still sequential, but more reliable

3. **`optimization_03_speed_experiments.py`**
   - **Parallel execution** with ThreadPoolExecutor
   - Timeout handling (10s limit)
   - Focus on configurations likely to be fast
   - This led to the final recommendations

## Documentation
- `optimization_summary.md` - Analysis of all experiments and recommendations
- `detector_optimized.md` - User guide for the optimized detector
- `OPTIMIZATION_FILES.md` - This file

## Other Related Files
- `experiment_results/` - Directory containing JSON results from experiments
- `hash_change_detection.py` - Hash-based optimization to skip redundant API calls
- `voice_generator.py` - Voice alert generation (also uses LLM)

## Quick Start
To use the optimized detector:
```python
# In your code, replace:
from basic_checker_hardcoded.detector import analyze_distraction

# With:
from basic_checker_hardcoded.detector_optimized import analyze_distraction
```

Set optimization mode via environment variable:
```bash
export FEISTY_OPTIMIZATION_MODE=balanced  # Options: ultra_fast, fast, balanced, accurate, original
```

## Running Experiments
To reproduce or extend the optimization experiments:
```bash
# Run the latest parallel speed experiments
python optimization_03_speed_experiments.py

# Test the optimized detector
python detector_optimized.py path/to/test/image.png
```




