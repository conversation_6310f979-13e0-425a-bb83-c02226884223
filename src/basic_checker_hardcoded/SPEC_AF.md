# Coding Feisty Checker - Product Requirements Document

## Original Human Specification

OK - NEXT FEATURE!!! Let's make a new src/basic_checker_hardcoded dir. Add a SPEC_AF.md and put PRD basics in it for the following: this feature should take a screenshot every 5 seconds, run compression on it and save it to /data/screenshots dir using the previously built scripts in src/ (ensure setup correctly for import, minimum LOC please). The feature should then run it through the LLM as we did for Will-Detector but it's trying to answer the question "does it look like the user is working on coding feisty or are they getting distracted?". Model should reason about it in thinking tokens but then output a JSON (structured outputs enforced by OAI API setting) with distracted_decision (True|False) and reasoning KVs. If distracted_decision then run a mac voice prompt to speak to user and get them back on track, concise max 100 chars, speed 120%. default voice.

## Speaker Module Implementation

Save this as `speaker.py` and import it from other scripts.

```python
#!/usr/bin/env python3
import subprocess
from typing import Optional

def speak(text: str, voice: Optional[str] = None, rate: Optional[int] = None) -> None:
    if not text:
        return
    cmd = ["/usr/bin/say"]
    if voice:
        cmd.extend(["-v", voice])
    if rate:
        cmd.extend(["-r", str(rate)])
    cmd.append(text)
    subprocess.run(cmd, check=False)

### Example Usage
from speaker import speak
speak("Hello from another module")
speak("Using different voice and rate", voice="Victoria", rate=200)
```

## SPEC HUMAN UPDATE 1
Updated the prompt: "text": "Does it look like the user is working on coding feisty or are they getting distracted? Analyze the screenshot and determine if they appear focused on development work related to coding/programming or if they seem distracted by other activities like social media, entertainment, browsing unrelated content, etc. The hardest part of your task is that the primary thing the user get's distracted by is 'other valid tasks'. You'll need concrete evidence that they're coding or doing something directly relevant to feisty. You should e.g., ask the image whether the word feisty can be seen IN the image.
model = gpt-5-mini
max_tokens = 400

challenge: is the prompt AT the image or two different calls?


## Overview
Automated distraction detection system that monitors user activity to determine if they are working on coding feisty or getting distracted.

## Core Functionality

### Screenshot Monitoring
- Capture screenshot every 5 seconds
- Apply compression using existing `src/img_compression` modules
- Save to `src/basic_checker_hardcoded/data/screenshots/` with timestamp format `YYYYMMDD_HHMMSS.png`

### Distraction Detection
- Process each compressed screenshot through LLM (OpenAI API)
- Question: "Does it look like the user is working on coding feisty or are they getting distracted?"
- Model reasoning in thinking tokens
- Structured JSON output enforced by OpenAI API settings:
  ```json
  {
    "distracted_decision": true/false,
    "reasoning": "explanation of decision"
  }
  ```

### Voice Alerts
- When `distracted_decision: true`, trigger voice prompt
- Max 100 characters, concise message to redirect user
- Speed: 120% (rate parameter)
- Voice backends:
  - macOS say (default, always available)
  - OpenAI TTS (nova voice, higher quality, requires API key)
- Toggle: `just voice-toggle` or `VOICE_BACKEND` env var
- Example messages: "Focus on feisty coding!", "Back to work on feisty!"

## Technical Requirements

### Dependencies
- Reuse `src/will_detector/screenshot.py` for capture
- Reuse `src/img_compression/compress_utils.py` for compression  
- OpenAI API for LLM analysis with structured outputs
- macOS `say` command for voice prompts

### Module Structure
```
src/basic_checker_hardcoded/
├── __init__.py
├── SPEC_AF.md
├── speaker.py          # Voice prompt utility
├── monitor.py          # Main monitoring loop
├── detector.py         # LLM distraction detection
└── data/
    └── screenshots/    # Timestamped compressed screenshots
```

### Configuration
- Screenshot interval: 5 seconds
- Voice rate: 120% (144 WPM default * 1.2)
- Max alert message: 100 characters
- Compression method: binarization (fastest from previous analysis)

## Success Criteria
- Screenshots captured and compressed every 5 seconds
- LLM successfully analyzes screenshots for distraction
- Voice alerts trigger immediately when distraction detected
- Minimal system impact and latency
- Proper import structure for reusing existing modules