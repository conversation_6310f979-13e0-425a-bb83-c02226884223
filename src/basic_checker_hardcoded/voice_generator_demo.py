import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from basic_checker_hardcoded.detector import analyze_distraction
from basic_checker_hardcoded.speaker import speak
from basic_checker_hardcoded.voice_generator import generate_kind_voice_message
from will_detector.screenshot import capture_active_window_screenshot


def demo_voice_generator():
    """Demo the voice generator using real screenshot analysis."""
    print("🎯 Voice Generator Demo - Real Screenshot Analysis")
    print("=" * 50)

    # Create persistent demo directory
    data_dir = Path(__file__).parent / "data" / "dont-delete"
    data_dir.mkdir(parents=True, exist_ok=True)

    print("📸 Capturing current screenshot...")

    # Capture screenshot with compression
    screenshot_data = capture_active_window_screenshot(
        output_dir=data_dir, compress=True
    )

    if screenshot_data and "compressed_path" in screenshot_data:
        compressed_path = screenshot_data["compressed_path"]
        original_path = screenshot_data["path"]

        print(f"✅ Screenshot saved: {Path(original_path).name}")
        print(f"✅ Compressed saved: {Path(compressed_path).name}")

        # Step 1: Run distraction analysis
        print("\n🔍 Step 1: Analyzing screenshot for distraction...")
        analysis = analyze_distraction(compressed_path)

        distracted = analysis.get("distracted_decision", False)
        reasoning = analysis.get("reasoning", "No reasoning provided")

        print(f"   Distracted: {distracted}")
        print(f"   Reasoning: {reasoning}")

        # Step 2: Generate voice message from analysis
        if distracted:
            print("\n🤖 Step 2: Generating personalized voice message...")
            voice_message = generate_kind_voice_message(reasoning)

            print(f"   Generated: '{voice_message}'")
            print(f"   Length: {len(voice_message)} characters")

            # Step 3: Speak the message
            print("\n🎵 Step 3: Speaking the message...")
            speak(voice_message, rate=173)

            print("✅ Voice alert delivered!")
        else:
            print("\n✅ No distraction detected - staying focused on coding!")
            print("   No voice alert needed.")

        print(f"\n📁 Files saved to: {data_dir}")
        print("   (These files won't be auto-deleted)")

    else:
        print("❌ Failed to capture screenshot")
        return False

    print("\n" + "=" * 50)
    print("🎉 Voice generator demo completed!")
    return True


if __name__ == "__main__":
    demo_voice_generator()
