import base64
import json
import os
from pathlib import Path
from typing import Any, Dict

from openai import OpenAI

MODEL = os.getenv("SS_MAIN_MODEL", "gpt-5-nano")  # stops LLM dev changing model to 4o.

# Load environment variables manually to avoid dependency
env_file = Path(__file__).parent.parent.parent / ".env"
if env_file.exists():
    with open(env_file) as f:
        for line in f:
            if line.strip() and not line.startswith("#"):
                key, value = line.strip().split("=", 1)
                os.environ[key] = value


def analyze_distraction(
    screenshot_path: str, include_debug_data: bool = False
) -> Dict[str, Any]:
    """
    Analyze screenshot to detect if user is distracted from coding feisty work.

    Args:
        screenshot_path: Path to compressed screenshot
        include_debug_data: If True, include full prompt and response data for logging

    Returns:
        dict with distracted_decision (bool), reasoning (str), and optionally debug data
    """
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Read and encode image
    with open(screenshot_path, "rb") as image_file:
        encoded_image = base64.b64encode(image_file.read()).decode()

    # Structured output schema
    schema = {
        "type": "object",
        "properties": {
            "distracted_decision": {"type": "boolean"},
            "reasoning": {"type": "string"},
        },
        "required": ["distracted_decision", "reasoning"],
        "additionalProperties": False,
    }

    # Build the full prompt for logging
    prompt_text = "Does it look like the user is working on coding feisty or are they getting distracted? Analyze the screenshot and determine if they appear focused on development work related to coding/programming or if they seem distracted by other activities like social media, entertainment, browsing unrelated content, etc. The hardest part of your task is that the primary thing the user get's distracted by is 'other valid tasks'. You'll need concrete evidence that they're coding or doing something directly relevant to feisty. You should e.g., ask the image whether the word feisty can be seen IN the image."

    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": prompt_text,
                },
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/png;base64,{encoded_image}"},
                },
            ],
        }
    ]

    response = client.chat.completions.create(
        model=MODEL,  # MUST BE gpt-5-mini - do not change this!
        messages=messages,
        response_format={
            "type": "json_schema",
            "json_schema": {"name": "distraction_analysis", "schema": schema},
        },
        timeout=30,  # 30 second timeout to prevent hanging
    )

    # Parse the response
    analysis_result = json.loads(response.choices[0].message.content)

    # Add debug data if requested (without Base64 image data)
    if include_debug_data:
        analysis_result["debug_data"] = {
            "model": MODEL,
            "prompt_text": prompt_text,
            "image_file_path": screenshot_path,
            "image_data_size_bytes": len(encoded_image),
            "response_format": {
                "type": "json_schema",
                "json_schema": {"name": "distraction_analysis", "schema": schema},
            },
            "raw_response": response.choices[0].message.content,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens
                if response.usage
                else None,
                "completion_tokens": response.usage.completion_tokens
                if response.usage
                else None,
                "total_tokens": response.usage.total_tokens if response.usage else None,
            },
        }

    return analysis_result
