here’s the OpenAI TTS (“audio.speech”) spec + working macOS quickstarts, trimmed to the metal and double-checked against official sources.

spec (OAI TTS)
	•	Endpoint: POST /v1/audio/speech (client SDK: client.audio.speech.create(...); streaming: .with_streaming_response.create(...)).  ￼
	•	Models: gpt-4o-mini-tts (current), tts-1 (legacy still shown in official examples). gpt-4o-mini-tts input limit ~2k tokens.  ￼
	•	Voices: built-ins exposed by the SDK include: alloy, ash, ballad, coral, echo, sage, shimmer, verse, marin, cedar.  ￼
	•	Output formats: mp3 | opus | aac | flac | wav | pcm. For lowest overhead, use WAV or raw PCM 24kHz, 16-bit, little-endian.  ￼ ￼
	•	Notable params:
	•	input (string to speak), voice, model (above),
	•	response_format (see list), speed (float),
	•	stream_format (SSE / audio; handled by SDK under .with_streaming_response).  ￼ ￼

⸻

quickstart A — stream PCM to your speakers (Python, macOS)

Low-latency playback as bytes arrive.

python3 -m venv venv && source venv/bin/activate
pip install openai sounddevice numpy
export OPENAI_API_KEY="YOUR_KEY"

# speak_stream_pcm.py
import os, numpy as np, sounddevice as sd
from openai import OpenAI

SAMPLE_RATE = 24000  # PCM is 24kHz per docs
VOICE = os.getenv("OPENAI_TTS_VOICE", "alloy")
MODEL = os.getenv("OPENAI_TTS_MODEL", "gpt-4o-mini-tts")

client = OpenAI(api_key=os.environ["OPENAI_API_KEY"])

def say(text: str):
    # Stream raw 16-bit PCM and play immediately
    with client.audio.speech.with_streaming_response.create(
        model=MODEL,
        voice=VOICE,
        input=text,
        response_format="pcm",
    ) as stream, sd.OutputStream(samplerate=SAMPLE_RATE, channels=1, dtype="int16") as out:
        for chunk in stream.iter_bytes(chunk_size=4096):
            if chunk:
                out.write(np.frombuffer(chunk, dtype=np.int16))

if __name__ == "__main__":
    import sys
    say(" ".join(sys.argv[1:]) or "Streaming PCM from OpenAI, nice and snappy.")

Run:

python speak_stream_pcm.py "Your generated text goes here."

Why this works: the OpenAI Python SDK exposes .with_streaming_response whose context manager yields byte iterators like .iter_bytes(...); we request response_format="pcm" and push the buffer straight to CoreAudio via sounddevice.  ￼

⸻

quickstart B — dead-simple file + play (Python, macOS)

Safest reference path (matches the official example), then play with afplay.

python3 -m venv venv && source venv/bin/activate
pip install openai
export OPENAI_API_KEY="YOUR_KEY"

# speak_wav.py
from pathlib import Path
import subprocess
from openai import OpenAI

out = Path("speech.wav")
client = OpenAI()

with client.audio.speech.with_streaming_response.create(
    model="gpt-4o-mini-tts",   # or "tts-1"
    voice="alloy",
    input="Hello from OpenAI TTS.",
    response_format="wav",
) as resp:
    resp.stream_to_file(out)

subprocess.run(["afplay", str(out)], check=True)

This mirrors the official openai-python example (their sample uses tts-1 and stream_to_file), just switching response_format to WAV for easy playback on macOS.  ￼

⸻

quickstart C — raw curl to MP3 (for sanity checks)

curl https://api.openai.com/v1/audio/speech \
  -H "Authorization: Bearer $OPENAI_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o-mini-tts",
    "voice": "alloy",
    "input": "This audio was generated by the OpenAI TTS endpoint.",
    "response_format": "mp3"
  }' --output speech.mp3

afplay speech.mp3

(Formats + model names per official docs.)  ￼

⸻

footnotes you actually care about
	•	Latency tip: prefer WAV/PCM over MP3/OPUS to dodge decode cost. PCM is 24kHz 16-bit LE.  ￼
	•	Voices/models evolve: when in doubt, check the Models page (gpt-4o-mini-tts) and TTS guide for the latest recommendations.  ￼

Want me to wrap this in a tiny menu-bar daemon with a queue and barge-in? I’ll drop either Quickstart in and wire a global hotkey next.