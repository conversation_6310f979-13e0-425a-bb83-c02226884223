import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from basic_checker_hardcoded.detector import analyze_distraction
from basic_checker_hardcoded.hash_change_detection import (
    calculate_perceptual_hash,
    has_significant_change,
)
from basic_checker_hardcoded.speaker import speak
from basic_checker_hardcoded.timer import IntervalTimer
from will_detector.screenshot import capture_active_window_screenshot


def demo_optimized_monitoring(cycles=10):
    """Demo the optimized monitoring system with timer + hash change detection."""
    print("🚀 Starting optimized coding feisty monitor demo...")
    print(f"Running {cycles} cycles with 3-second intervals")
    print("=" * 60)

    timer = IntervalTimer(3.0)  # 3 seconds for demo
    data_dir = Path(__file__).parent / "data" / "screenshots"
    data_dir.mkdir(parents=True, exist_ok=True)

    last_hash = None
    analysis_count = 0
    skip_count = 0

    try:
        for cycle in range(1, cycles + 1):
            print(f"\n🔄 Cycle {cycle}/{cycles}")
            timer.start_cycle()

            # Capture screenshot with compression
            screenshot_data = capture_active_window_screenshot(
                output_dir=data_dir, compress=True
            )

            if screenshot_data and "compressed_path" in screenshot_data:
                compressed_path = screenshot_data["compressed_path"]
                print(f"📸 Screenshot: {Path(compressed_path).name}")

                # Calculate perceptual hash (fast)
                current_hash = calculate_perceptual_hash(compressed_path)
                print(f"🔢 Hash: {current_hash[:16]}...")

                # Check for significant change
                if last_hash is None or has_significant_change(current_hash, last_hash):
                    analysis_count += 1
                    print("🔍 ANALYZING - Significant change detected")

                    # Run LLM analysis (slow)
                    analysis = analyze_distraction(compressed_path)
                    print(
                        f"🤖 Analysis: distracted={analysis.get('distracted_decision')}"
                    )
                    print(f"💭 Reasoning: {analysis.get('reasoning', '')}...")

                    # Voice alert if distracted
                    if analysis.get("distracted_decision", False):
                        alert_message = "Focus on feisty coding! Get back to work."[
                            :100
                        ]
                        speak(alert_message, rate=173)
                        print(f"🔊 VOICE ALERT: {alert_message}")

                else:
                    skip_count += 1
                    print("⚡ SKIPPING - No significant change (saving API call)")

                last_hash = current_hash
            else:
                print("❌ Screenshot capture failed")

            # Wait for precise interval
            processing_time = timer.get_actual_interval()
            print(f"⏱️  Processing: {processing_time:.3f}s")
            timer.wait_for_next()

    except KeyboardInterrupt:
        print("\n\n⛔ Demo stopped by user")

    # Summary
    print("\n" + "=" * 60)
    print("📊 OPTIMIZATION SUMMARY:")
    print(f"   • Total cycles: {cycle}")
    print(f"   • LLM analyses: {analysis_count}")
    print(f"   • Skipped (no change): {skip_count}")
    if analysis_count + skip_count > 0:
        reduction = (skip_count / (analysis_count + skip_count)) * 100
        print(f"   • API call reduction: {reduction:.1f}%")
    print("   • Interval precision: 3.000s target")

    return analysis_count, skip_count


if __name__ == "__main__":
    print("Demo: Optimized monitoring with timer + hash change detection")
    print(
        "This will capture screenshots every 3 seconds and show optimization in action"
    )
    print("Press Ctrl+C to stop early\n")

    demo_optimized_monitoring(cycles=10)
