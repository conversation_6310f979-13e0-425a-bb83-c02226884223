import sys
import tempfile
import time
from pathlib import Path

from PIL import Image, ImageDraw

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from basic_checker_hardcoded.hash_change_threshold import (
    calculate_perceptual_hash,
    get_change_percentage,
    has_significant_change,
)

from basic_checker_hardcoded.timer import IntervalTimer


def validation_test_timer_precision():
    """Validate timer maintains precise 2-second intervals."""
    print("🔍 Testing timer precision with 2-second intervals...")

    timer = IntervalTimer(2.0)
    intervals = []

    for i in range(3):
        start = time.time()
        timer.start_cycle()

        # Simulate variable processing time
        processing_time = 0.1 + (i * 0.3)  # 0.1, 0.4, 0.7 seconds
        time.sleep(processing_time)

        timer.wait_for_next()
        elapsed = time.time() - start
        intervals.append(elapsed)

        print(
            f"  Cycle {i + 1}: Processing={processing_time:.1f}s, Total={elapsed:.3f}s"
        )

    # Check all intervals are close to 2.0 seconds
    for i, interval in enumerate(intervals):
        assert 1.95 <= interval <= 2.05, f"Interval {i + 1} not precise: {interval}s"

    print("✅ Timer precision validation passed")
    return True


def validation_test_hash_change_detection():
    """Validate hash change detection with realistic scenario."""
    print("🔍 Testing hash change detection with realistic screenshots...")

    with tempfile.TemporaryDirectory() as temp_dir:
        # Create base "IDE" screenshot
        base_image = Image.new("RGB", (800, 600), (40, 44, 52))  # Dark IDE theme
        draw = ImageDraw.Draw(base_image)

        # Add IDE elements
        draw.rectangle([0, 0, 800, 30], fill=(60, 64, 72))  # Title bar
        draw.rectangle([0, 30, 200, 600], fill=(50, 54, 62))  # Sidebar
        draw.text((10, 5), "VS Code - feisty.py", fill=(255, 255, 255))
        draw.text((10, 50), "src/", fill=(255, 255, 255))
        draw.text((20, 70), "main.py", fill=(255, 255, 255))

        base_path = Path(temp_dir) / "base.png"
        base_image.save(base_path)
        base_hash = calculate_perceptual_hash(str(base_path))

        # Test 1: No change (identical screenshot)
        identical_path = Path(temp_dir) / "identical.png"
        base_image.save(identical_path)
        identical_hash = calculate_perceptual_hash(str(identical_path))

        no_change = not has_significant_change(base_hash, identical_hash)
        change_pct = get_change_percentage(base_hash, identical_hash)
        print(
            f"  Identical images: change={change_pct:.1f}%, significant={not no_change}"
        )
        assert no_change, "Identical images should not show significant change"

        # Test 2: Minor change (cursor movement, small text change)
        minor_change = base_image.copy()
        draw_minor = ImageDraw.Draw(minor_change)
        draw_minor.text(
            (220, 100), "print('hello')", fill=(255, 255, 255)
        )  # Small code addition

        minor_path = Path(temp_dir) / "minor.png"
        minor_change.save(minor_path)
        minor_hash = calculate_perceptual_hash(str(minor_path))

        minor_significant = has_significant_change(base_hash, minor_hash)
        minor_pct = get_change_percentage(base_hash, minor_hash)
        print(
            f"  Minor change: change={minor_pct:.1f}%, significant={minor_significant}"
        )

        # Test 3: Major change (different application)
        major_change = Image.new(
            "RGB", (800, 600), (255, 255, 255)
        )  # White background (browser)
        draw_major = ImageDraw.Draw(major_change)
        draw_major.rectangle([0, 0, 800, 80], fill=(220, 220, 220))  # Browser toolbar
        draw_major.text((10, 30), "https://facebook.com", fill=(0, 0, 0))
        draw_major.text((100, 200), "Social Media Content", fill=(0, 0, 0))

        major_path = Path(temp_dir) / "major.png"
        major_change.save(major_path)
        major_hash = calculate_perceptual_hash(str(major_path))

        major_significant = has_significant_change(base_hash, major_hash)
        major_pct = get_change_percentage(base_hash, major_hash)
        print(
            f"  Major change: change={major_pct:.1f}%, significant={major_significant}"
        )
        assert major_significant, (
            "Different applications should show significant change"
        )

        print("✅ Hash change detection validation passed")
        return True


def validation_test_integrated_monitoring_simulation():
    """Simulate realistic monitoring scenario with timer + hash detection."""
    print("🔍 Testing integrated monitoring simulation...")

    timer = IntervalTimer(1.0)  # 1 second for fast testing
    last_hash = None
    analysis_count = 0
    skip_count = 0

    # Simulate 5 monitoring cycles
    for cycle in range(5):
        timer.start_cycle()

        # Simulate screenshot capture (create test image)
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp:
            # Create image that changes every 2 cycles
            color_intensity = 255 if (cycle // 2) % 2 else 100
            test_image = Image.new(
                "RGB", (100, 100), (color_intensity, color_intensity, color_intensity)
            )
            test_image.save(tmp.name)

            # Calculate hash
            current_hash = calculate_perceptual_hash(tmp.name)

            # Check if we should analyze
            if last_hash is None or has_significant_change(current_hash, last_hash):
                analysis_count += 1
                print(f"  Cycle {cycle + 1}: ANALYZING (change detected)")
            else:
                skip_count += 1
                print(f"  Cycle {cycle + 1}: SKIPPED (no significant change)")

            last_hash = current_hash

        timer.wait_for_next()

    print(f"  Results: {analysis_count} analyses, {skip_count} skipped")
    print(
        f"  API call reduction: {(skip_count / (analysis_count + skip_count) * 100):.0f}%"
    )

    # Should have some skips (identical images) and some analyses
    assert skip_count > 0, "Should skip some identical screenshots"
    assert analysis_count > 0, "Should analyze some changed screenshots"

    print("✅ Integrated monitoring simulation passed")
    return True


def run_all_validations():
    """Run all validation tests."""
    print("🚀 Running comprehensive validation tests...")
    print("=" * 50)

    results = []
    results.append(validation_test_timer_precision())
    results.append(validation_test_hash_change_detection())
    results.append(validation_test_integrated_monitoring_simulation())

    print("=" * 50)
    if all(results):
        print("🎉 ALL VALIDATION TESTS PASSED!")
        print("✅ Timer maintains precise intervals")
        print("✅ Hash detection identifies significant changes")
        print("✅ Integration works for monitoring scenarios")
        return True
    else:
        print("❌ Some validation tests failed")
        return False


if __name__ == "__main__":
    success = run_all_validations()
    exit(0 if success else 1)
