#!/usr/bin/env python3
"""
OpenAI TTS Volume Testing Script

Tests different volume levels for OpenAI TTS playback.
macOS afplay volume range: 0.0 to 2.0 (1.0 = normal, 2.0 = double volume)
"""
import os
import subprocess
import tempfile
import time
from openai import OpenAI

def test_openai_volume(text: str, volume: float = 1.0):
    """
    Play OpenAI TTS at specified volume.
    
    Args:
        text: Text to speak
        volume: Volume level (0.0 to 2.0, where 1.0 is normal)
    """
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ No OpenAI API key found")
        return
    
    client = OpenAI(api_key=api_key)
    
    # Generate TTS
    print(f"🎤 Generating speech at volume {volume}x...")
    response = client.audio.speech.create(
        model="tts-1",
        voice="nova",
        input=text,
        speed=1.2,
        response_format="mp3"
    )
    
    # Save to temp file
    with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
        tmp_path = tmp_file.name
        for chunk in response.iter_bytes():
            tmp_file.write(chunk)
    
    # Play with specified volume
    print(f"🔊 Playing at {volume}x volume...")
    subprocess.run(["afplay", "-v", str(volume), tmp_path], check=True)
    
    # Cleanup
    os.unlink(tmp_path)
    print(f"✅ Done (volume: {volume}x)\n")

def test_macos_volume(text: str, rate: int = 173):
    """Test macOS say for comparison."""
    print(f"🍎 macOS say (rate: {rate})...")
    subprocess.run(["/usr/bin/say", "-r", str(rate), text])
    print("✅ Done\n")

def main():
    test_text = "Testing voice volume. Can you hear me clearly over your music?"
    
    print("=" * 50)
    print("VOICE VOLUME COMPARISON TEST")
    print("=" * 50)
    
    # Test macOS for reference
    print("\n1️⃣  REFERENCE: macOS Say (default volume)")
    print("-" * 40)
    test_macos_volume(test_text)
    time.sleep(1)
    
    # Test OpenAI at different volumes
    volumes = [
        (1.0, "Normal volume"),
        (1.5, "50% louder"),
        (2.0, "Double volume (MAX)"),
        (1.75, "75% louder (recommended)"),
    ]
    
    for i, (vol, desc) in enumerate(volumes, 2):
        print(f"\n{i}️⃣  OpenAI TTS - {desc}")
        print("-" * 40)
        test_openai_volume(test_text, vol)
        time.sleep(1)
    
    print("\n" + "=" * 50)
    print("RECOMMENDED SETTINGS:")
    print("- Volume: 1.75x (75% louder)")
    print("- This should match macOS say loudness")
    print("- Set OPENAI_TTS_VOLUME=1.75 in .env")
    print("=" * 50)

if __name__ == "__main__":
    main()

