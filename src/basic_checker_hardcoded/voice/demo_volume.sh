#!/bin/zsh
# Demonstrate OpenAI TTS Volume Control

# Load .env variables
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
fi

echo "========================================"
echo "OpenAI TTS VOLUME DEMONSTRATION"
echo "========================================"
echo ""
echo "The OpenAI TTS now supports volume control!"
echo "Default volume is set to 1.75x (75% louder)"
echo "to match macOS say loudness."
echo ""
echo "Environment variable: OPENAI_TTS_VOLUME"
echo "Range: 0.0 to 2.0 (1.0 = normal)"
echo ""
echo "Current settings:"
echo "  Backend: ${VOICE_BACKEND:-macos}"
echo "  Volume: ${OPENAI_TTS_VOLUME:-1.75}x"
echo "  Voice: ${OPENAI_TTS_VOICE:-nova}"
echo ""
echo "========================================"
echo ""

# Test at different volumes
echo "🔊 Testing at 1.0x (normal volume)..."
OPENAI_TTS_VOLUME=1.0 VOICE_BACKEND=openai_tts ./voice_speak.sh "Normal volume test."
sleep 1

echo ""
echo "🔊 Testing at 1.75x (recommended - matches macOS)..."
OPENAI_TTS_VOLUME=1.75 VOICE_BACKEND=openai_tts ./voice_speak.sh "Recommended volume - seventy-five percent louder."
sleep 1

echo ""
echo "🔊 Testing at 2.0x (maximum volume)..."  
OPENAI_TTS_VOLUME=2.0 VOICE_BACKEND=openai_tts ./voice_speak.sh "Maximum volume - double the normal level."

echo ""
echo "========================================"
echo "✅ Volume control is working!"
echo ""
echo "To set your preferred volume:"
echo "  Add to .env: OPENAI_TTS_VOLUME=1.75"
echo "  (Already added with recommended 1.75x)"
echo "========================================"

