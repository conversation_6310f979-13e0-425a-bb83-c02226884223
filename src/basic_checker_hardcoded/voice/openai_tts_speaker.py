#!/usr/bin/env python3
"""
OpenAI TTS (Text-to-Speech) Speaker Implementation
Uses OpenAI's standard TTS API for high-quality voice synthesis.
Provides toggle mechanism to switch between macOS say and OpenAI TTS.
"""

import os
import subprocess
import tempfile
from typing import Optional

from openai import OpenAI


class OpenAITTSSpeaker:
    """
    High-quality text-to-speech using OpenAI's TTS API.

    Features:
    - Models: tts-1 (low latency) or tts-1-hd (high quality)
    - Voices: alloy, echo, fable, onyx, nova, shimmer
    - Streaming support for low latency
    - Automatic fallback to macOS say
    """

    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.client = OpenAI(api_key=self.api_key) if self.api_key else None
        self.model = os.getenv(
            "OPENAI_TTS_MODEL", "tts-1"
        )  # tts-1 for speed, tts-1-hd for quality
        self.voice = os.getenv(
            "OPENAI_TTS_VOICE", "nova"
        )  # nova is natural and friendly
        self.speed = float(os.getenv("OPENAI_TTS_SPEED", "1.2"))  # 1.2 = 120% speed

    def is_available(self) -> bool:
        """Check if OpenAI TTS is available."""
        return self.client is not None

    def speak_openai(self, text: str, voice: Optional[str] = None) -> bool:
        """
        Generate speech using OpenAI TTS API.

        Args:
            text: Text to speak
            voice: Optional voice override

        Returns:
            True if successful, False otherwise
        """
        if not self.client:
            return False

        try:
            # Use provided voice or configured default
            selected_voice = voice or self.voice

            # Create speech with streaming for lower latency
            response = self.client.audio.speech.create(
                model=self.model,
                voice=selected_voice,
                input=text,
                speed=self.speed,
                response_format="mp3",  # MP3 is well-supported on macOS
            )

            # Save to temporary file and play
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as tmp_file:
                tmp_path = tmp_file.name

                # Stream response to file
                for chunk in response.iter_bytes():
                    tmp_file.write(chunk)

            # Play audio using macOS afplay (blocking to hear it)
            subprocess.run(["afplay", tmp_path], check=True)

            # Clean up temp file after a delay (let it play first)
            # In production, you might want to handle this better
            import threading

            threading.Timer(10.0, lambda: os.unlink(tmp_path)).start()

            return True

        except Exception as e:
            print(f"OpenAI TTS error: {e}")
            return False

    def speak_macos(
        self, text: str, voice: Optional[str] = None, rate: int = 173
    ) -> None:
        """
        Fallback to macOS say command.

        Args:
            text: Text to speak
            voice: Optional macOS voice name
            rate: Speech rate in WPM
        """
        if not text:
            return

        cmd = ["/usr/bin/say"]
        if voice:
            cmd.extend(["-v", voice])
        if rate:
            cmd.extend(["-r", str(rate)])
        cmd.append(text)

        subprocess.run(cmd, check=False)


def speak(text: str, voice: Optional[str] = None, rate: Optional[int] = None) -> None:
    """
    Enhanced speak function with OpenAI TTS support and fallback.

    This is a drop-in replacement for the existing speaker.py speak() function.
    It checks environment variables to determine which backend to use.

    Environment variables:
    - VOICE_BACKEND: "macos" (default), "openai_tts"
    - OPENAI_TTS_MODEL: "tts-1" (fast) or "tts-1-hd" (quality)
    - OPENAI_TTS_VOICE: "nova", "alloy", "echo", "fable", "onyx", "shimmer"
    - OPENAI_TTS_SPEED: "1.0" to "4.0" (default 1.2 for 120%)
    - OPENAI_TTS_FALLBACK: "true" to fallback to macOS on error

    Args:
        text: Text to speak
        voice: Optional voice override
        rate: Optional rate for macOS say (ignored for OpenAI)
    """
    if not text:
        return

    # Get backend preference
    backend = os.getenv("VOICE_BACKEND", "macos").lower()

    if backend == "openai_tts":
        # Try OpenAI TTS
        tts = OpenAITTSSpeaker()

        if tts.is_available():
            success = tts.speak_openai(text, voice)

            if success:
                return
            elif os.getenv("OPENAI_TTS_FALLBACK", "true").lower() == "true":
                print("OpenAI TTS failed, falling back to macOS say...")
                backend = "macos"
            else:
                return  # No fallback
        else:
            print("OpenAI TTS not available (no API key?), using macOS say...")
            backend = "macos"

    # Default to macOS say
    if backend == "macos":
        # Original implementation
        cmd = ["/usr/bin/say"]
        if voice:
            cmd.extend(["-v", voice])
        if rate:
            cmd.extend(["-r", str(rate)])
        cmd.append(text)
        subprocess.run(cmd, check=False)


# Quick test function
if __name__ == "__main__":
    import sys

    # Test text
    test_text = "Hey! You're distracted from coding. Let's get back to work."

    if len(sys.argv) > 1:
        test_text = " ".join(sys.argv[1:])

    print("=" * 60)
    print("OpenAI TTS Speaker Test")
    print("=" * 60)
    print(f"Backend: {os.getenv('VOICE_BACKEND', 'macos')}")
    print(f"Model: {os.getenv('OPENAI_TTS_MODEL', 'tts-1')}")
    print(f"Voice: {os.getenv('OPENAI_TTS_VOICE', 'nova')}")
    print(f"Speed: {os.getenv('OPENAI_TTS_SPEED', '1.2')}x")
    print("-" * 60)
    print(f"Text: {test_text}")
    print("-" * 60)

    speak(test_text)
    print("✅ Done!")
