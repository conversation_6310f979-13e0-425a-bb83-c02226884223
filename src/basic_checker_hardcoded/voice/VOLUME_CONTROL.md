# OpenAI TTS Volume Control

## Problem Solved
You couldn't hear the OpenAI TTS voice over your music, while the macOS voice was loud enough.

## Solution
Added volume control using macOS `afplay -v` flag.

## Configuration

### Environment Variable
```bash
OPENAI_TTS_VOLUME=1.75  # Recommended (75% louder)
```

### Volume Range
- **0.0 to 2.0** (afplay volume scale)
- **1.0** = Normal volume (too quiet)
- **1.75** = Recommended (matches macOS say loudness) ✅
- **2.0** = Maximum volume

## Usage

### Test Different Volumes
```bash
# Run the volume test script
./src/basic_checker_hardcoded/voice/demo_volume.sh

# Or test individual volumes
OPENAI_TTS_VOLUME=1.5 ./voice_speak.sh "Testing at 50% louder"
OPENAI_TTS_VOLUME=2.0 ./voice_speak.sh "Testing at maximum volume"
```

### Set Default Volume
Already added to `.env`:
```bash
OPENAI_TTS_VOLUME=1.75
```

## Files Updated
- `openai_tts_speaker.py` - Added volume support with `afplay -v`
- `speaker.py` - Updated docstring with volume info
- `toggle_voice.py` - Shows volume in status display
- `.env` - Added `OPENAI_TTS_VOLUME=1.75`

## Testing
```bash
# Quick test at recommended volume
just voice-test

# Compare volumes
./src/basic_checker_hardcoded/voice/demo_volume.sh

# Test specific volume
./src/basic_checker_hardcoded/voice/test_volume.py
```

## Result
✅ OpenAI TTS is now loud enough to hear over music at 1.75x volume (75% louder than normal).

