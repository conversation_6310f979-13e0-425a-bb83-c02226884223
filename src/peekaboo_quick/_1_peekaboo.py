#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
peekaboo_active_window.py
Returns: frontmost window identity (app + title), which display it's on, and a screenshot path.
Requires: macOS, Peekaboo CLI in PATH, Screen Recording permission granted to terminal/shell.  [1]
[1] See Peekaboo README "Basic Usage" + permissions notes.  (brew tap steipete/tap && brew install peekaboo)
"""

from __future__ import annotations
import subprocess, shlex, json, re, sys
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

# ────────────────────────────────────────────────────────────────────────────────
# CFG singleton (edit here)
# ────────────────────────────────────────────────────────────────────────────────
@dataclass(frozen=True)
class _CFG:
    PEEKABOO_BIN: str = "peekaboo"                               # 1) CLI entrypoint
    OUT_DIR: Path = Path.home() / "Pictures" / "peekaboo_shots"  # 2) Where screenshots go
    FILENAME_PREFIX: str = "active-window"                       # 3) Screenshot filename prefix
    IMAGE_EXT: str = "png"                                       # 4) png recommended
    VERBOSE: bool = False                                        # 5) Set True for debug

CFG = _CFG()  # Singleton instance


# ────────────────────────────────────────────────────────────────────────────────
# Utilities
# ────────────────────────────────────────────────────────────────────────────────
def _run(cmd: str) -> subprocess.CompletedProcess:
    """Run a shell command, return CompletedProcess. Raises on nonzero exit.  #1"""
    if CFG.VERBOSE:
        print(f"$ {cmd}", file=sys.stderr)
    cp = subprocess.run(shlex.split(cmd), stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    if cp.returncode != 0:
        raise RuntimeError(f"Command failed ({cp.returncode}): {cmd}\nSTDERR:\n{cp.stderr.strip()}")
    return cp  #2


def _frontmost_app() -> str:
    """Get name of the frontmost app (robust, OS-native).  #3"""
    osa = r'''osascript -e 'tell application "System Events" to get name of first application process whose frontmost is true' '''
    return _run(osa).stdout.strip()  #4


def _list_windows_for_app(app_name: str) -> str:
    """
    Ask Peekaboo to list windows for an app, textual output.  #5
    Peekaboo README documents: `peekaboo list windows --app "Visual Studio Code"`,
    and that window listings include which screen each window is on.  #6
    """
    cmd = f'{CFG.PEEKABOO_BIN} list windows --app "{app_name}"'
    return _run(cmd).stdout  #7


def _parse_front_window_info(list_output: str) -> dict:
    """
    Heuristic: Peekaboo lists app windows front-to-back; take the first.  #8
    We parse a line that typically contains the window title and 'Screen: <name>'.  #9
    """
    # Take the first non-empty line that looks like a window row.  #10
    line = next((ln for ln in list_output.splitlines() if ln.strip()), None)
    if not line:
        raise ValueError("No windows found in Peekaboo output.")  #11

    # Extract `title` (quoted) if present, else fallback to whole line.  #12
    m_title = re.search(r'"([^"]+)"', line)
    title = m_title.group(1) if m_title else line.strip()

    # Extract Screen name if present (e.g., 'Screen: Built-in Display' or 'Screen: External …').  #13
    m_screen = re.search(r'Screen:\s*([^|]+)$', line) or re.search(r'\bScreen:\s*([^,]+)', line)
    screen = (m_screen.group(1).strip() if m_screen else "Unknown").rstrip()  #14

    return {"title": title, "screen": screen, "raw": line}  #15


def _ensure_outdir() -> Path:
    """Create OUT_DIR if needed.  #16"""
    CFG.OUT_DIR.mkdir(parents=True, exist_ok=True)
    return CFG.OUT_DIR  #17


def _screenshot_frontmost() -> Path:
    """
    Capture the frontmost window via Peekaboo.  #18
    README documents: `peekaboo image --mode frontmost --path ...`  #19
    """
    _ensure_outdir()
    ts = datetime.now().strftime("%Y%m%d-%H%M%S")
    out = CFG.OUT_DIR / f"{CFG.FILENAME_PREFIX}-{ts}.{CFG.IMAGE_EXT}"
    cmd = f'{CFG.PEEKABOO_BIN} image --mode frontmost --path "{out}"'
    _run(cmd)
    return out  #20


# ────────────────────────────────────────────────────────────────────────────────
# Main
# ────────────────────────────────────────────────────────────────────────────────
def main() -> int:
    app = _frontmost_app()                          #21
    win_list = _list_windows_for_app(app)           #22
    win = _parse_front_window_info(win_list)        #23
    shot_path = _screenshot_frontmost()             #24

    result = {
        "app": app,
        "window_title": win["title"],
        "display": win["screen"],                   # From Peekaboo list output
        "screenshot_path": str(shot_path),
        "peekaboo_line": win["raw"],                # Raw line for auditability
    }  #25

    print(json.dumps(result, indent=2, ensure_ascii=False))  #26
    return 0  #27


if __name__ == "__main__":
    try:
        raise SystemExit(main())  #28
    except Exception as e:
        print(f"ERROR: {e}", file=sys.stderr)       #29
        raise SystemExit(1)                         #30
