#!/usr/bin/env python3
"""
Alerter - Voice and visual alerts for focus coaching

Provides contextual alerts when user gets distracted from their top task.
"""

import subprocess
import time
import logging
from typing import Optional, Dict
from pathlib import Path

from config import TaskFocusConfig

logger = logging.getLogger(__name__)

class Alerter:
    """<PERSON>les voice and visual alerts for focus coaching"""
    
    def __init__(self, config: TaskFocusConfig):
        self.config = config
        self.last_alert_time: Dict[str, float] = {}  # Alert type -> timestamp
        self.alert_count: Dict[str, int] = {}        # Alert type -> count
        
    def announce_monitoring_start(self, task: str):
        """Announce that monitoring has started"""
        if not self.config.VOICE_ALERTS_ENABLED:
            return
        
        message = f"Focus monitor started. Target task: {self._shorten_task(task)}"
        self._speak(message)
        logger.info(f"🎯 Announced monitoring start: '{task}'")
    
    def announce_task_change(self, old_task: Optional[str], new_task: Optional[str]):
        """Announce when the top task changes"""
        if not self.config.ANNOUNCE_TASK_CHANGES or not self.config.VOICE_ALERTS_ENABLED:
            return
        
        if new_task:
            message = f"Focus target changed to: {self._shorten_task(new_task)}"
            self._speak(message)
            logger.info(f"🔄 Announced task change: '{old_task}' → '{new_task}'")
    
    def send_distraction_alert(self, task: str, reasoning: str, distraction_count: int):
        """Send alert when distraction is detected"""
        alert_type = "distraction"
        
        # Check cooldown period
        if not self._should_send_alert(alert_type):
            logger.debug(f"Alert suppressed due to cooldown: {alert_type}")
            return
        
        # Determine alert intensity based on count and config
        if self.config.ESCALATING_ALERTS:
            intensity = min(distraction_count, 3)  # Cap at level 3
        else:
            intensity = 1
        
        # Create appropriate message
        message = self._create_distraction_message(task, reasoning, intensity)
        
        # Send voice alert if enabled
        if self.config.VOICE_ALERTS_ENABLED:
            self._speak(message)
        
        # Send visual alert if enabled  
        if self.config.VISUAL_ALERTS_ENABLED:
            self._show_notification(message)
        
        # Update alert tracking
        self._record_alert(alert_type)
        
        logger.warning(f"⚠️  Sent distraction alert (intensity {intensity}): {message}")
    
    def send_focus_encouragement(self, task: str):
        """Send encouraging message when user returns to focus"""
        if not self.config.VOICE_ALERTS_ENABLED:
            return
        
        alert_type = "encouragement"
        
        # Check cooldown (longer for positive messages)
        if not self._should_send_alert(alert_type, cooldown_multiplier=2):
            return
        
        messages = [
            f"Great! You're back on track with {self._shorten_task(task)}",
            f"Good focus on your priority task",
            f"Well done staying focused"
        ]
        
        # Choose message based on time of day or random
        import random
        message = random.choice(messages)
        
        self._speak(message)
        self._record_alert(alert_type)
        
        logger.info(f"✅ Sent encouragement: {message}")
    
    def _shorten_task(self, task: str, max_length: int = 50) -> str:
        """Shorten task description for voice output"""
        if len(task) <= max_length:
            return task
        return task[:max_length-3] + "..."
    
    def _create_distraction_message(self, task: str, reasoning: str, intensity: int) -> str:
        """Create appropriate distraction alert message based on intensity"""
        task_short = self._shorten_task(task)
        
        if intensity == 1:
            # Gentle reminder
            messages = [
                f"Remember your focus: {task_short}",
                f"Let's get back to: {task_short}",
                f"Your priority is: {task_short}"
            ]
        elif intensity == 2:
            # More direct
            messages = [
                f"Focus needed on: {task_short}",
                f"Please return to your priority task: {task_short}",
                f"Distraction detected. Focus on: {task_short}"
            ]
        else:
            # Strong reminder
            messages = [
                f"Multiple distractions detected. Please focus on: {task_short}",
                f"Important: Your top priority is {task_short}",
                f"Focus alert: Return to {task_short} immediately"
            ]
        
        import random
        return random.choice(messages)
    
    def _should_send_alert(self, alert_type: str, cooldown_multiplier: float = 1.0) -> bool:
        """Check if alert should be sent based on cooldown"""
        if alert_type not in self.last_alert_time:
            return True
        
        cooldown = self.config.ALERT_COOLDOWN_SECONDS * cooldown_multiplier
        time_since_last = time.time() - self.last_alert_time[alert_type]
        
        return time_since_last >= cooldown
    
    def _record_alert(self, alert_type: str):
        """Record that an alert was sent"""
        self.last_alert_time[alert_type] = time.time()
        self.alert_count[alert_type] = self.alert_count.get(alert_type, 0) + 1
    
    def _speak(self, message: str):
        """Use macOS 'say' command for voice alerts"""
        try:
            # Use macOS built-in TTS
            subprocess.run(['say', message], check=False, capture_output=True)
            logger.debug(f"🔊 Spoke: '{message}'")
            
        except Exception as e:
            logger.error(f"Failed to speak message: {e}")
    
    def _show_notification(self, message: str):
        """Show macOS notification"""
        try:
            # Use macOS notification system
            title = "Focus Monitor"
            subprocess.run([
                'osascript', '-e', 
                f'display notification "{message}" with title "{title}"'
            ], check=False, capture_output=True)
            
            logger.debug(f"📱 Showed notification: '{message}'")
            
        except Exception as e:
            logger.error(f"Failed to show notification: {e}")
    
    def get_alert_stats(self) -> Dict:
        """Get statistics about alerts sent"""
        return {
            'alert_counts': self.alert_count.copy(),
            'last_alert_times': {
                alert_type: time.time() - timestamp 
                for alert_type, timestamp in self.last_alert_time.items()
            },
            'config': {
                'voice_enabled': self.config.VOICE_ALERTS_ENABLED,
                'visual_enabled': self.config.VISUAL_ALERTS_ENABLED,
                'cooldown_seconds': self.config.ALERT_COOLDOWN_SECONDS,
                'escalating': self.config.ESCALATING_ALERTS
            }
        }
    
    def test_alerts(self):
        """Test alert functionality"""
        print("🧪 Testing Alert System")
        print("-" * 30)
        
        test_task = "watch baby unicorns on youtube"
        
        print("Testing monitoring start announcement...")
        self.announce_monitoring_start(test_task)
        time.sleep(2)
        
        print("Testing task change announcement...")
        self.announce_task_change(test_task, "write important report")
        time.sleep(2)
        
        print("Testing distraction alert (level 1)...")
        self.send_distraction_alert("write important report", "browsing social media", 1)
        time.sleep(2)
        
        print("Testing distraction alert (level 2)...")
        self.send_distraction_alert("write important report", "watching videos", 2)
        time.sleep(2)
        
        if self.config.VISUAL_ALERTS_ENABLED:
            print("Testing visual notification...")
            self._show_notification("Test notification from Focus Monitor")
        
        print("✅ Alert testing complete")
        
        # Show stats
        stats = self.get_alert_stats()
        print(f"\nAlert statistics: {stats}")

if __name__ == "__main__":
    # Test the alerter
    from config import CONFIG
    
    # Enable alerts for testing
    test_config = CONFIG
    test_config.VOICE_ALERTS_ENABLED = True
    test_config.VISUAL_ALERTS_ENABLED = True
    test_config.ALERT_COOLDOWN_SECONDS = 1  # Short cooldown for testing
    
    alerter = Alerter(test_config)
    alerter.test_alerts()