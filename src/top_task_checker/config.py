#!/usr/bin/env python3
"""
Task Focus Configuration - Settings for top task monitoring system
"""

import os
from dataclasses import dataclass
from typing import Literal

@dataclass
class TaskFocusConfig:
    """Configuration for top task focus monitoring system"""
    
    # === Task Management ===
    TASK_REFRESH_INTERVAL: int = 30          # Seconds between task refreshes
    ANNOUNCE_TASK_CHANGES: bool = True       # Voice alert when task changes
    TASK_FETCH_TIMEOUT: int = 10            # Timeout for Things3 script calls
    
    # === Monitoring Behavior ===
    SCREENSHOT_INTERVAL: float = 2.0         # Seconds between screen checks
    HASH_CHANGE_THRESHOLD: float = 0.15      # Screen change detection sensitivity
    MONITORING_ENABLED: bool = True          # Master switch for monitoring
    
    # === Screen Capture ===
    SCREENSHOT_RESOLUTION: Literal["full", "half", "quarter"] = "half"
    SCREENSHOT_COMPRESS: bool = True
    SCREENSHOT_COMPRESSION_METHOD: Literal["binarize", "bilateral"] = "binarize"
    
    # === Analysis Settings ===
    LLM_MODEL: str = "gpt-4o"               # Model for focus analysis
    LLM_ANALYZE_COMPRESSED: bool = True      # Use compressed images for LLM
    FOCUS_CONFIDENCE_THRESHOLD: float = 0.8  # Confidence level for focus detection
    CONTEXT_WINDOW_SECONDS: int = 30         # Context for analysis decisions
    
    # === Alert System ===
    VOICE_ALERTS_ENABLED: bool = True        # Enable voice notifications
    VISUAL_ALERTS_ENABLED: bool = False      # Enable visual notifications  
    ALERT_COOLDOWN_SECONDS: int = 300        # 5min between similar alerts
    ESCALATING_ALERTS: bool = True           # Increase alert intensity over time
    
    # === Performance ===
    HASH_DETECTION_ON_COMPRESSED: bool = True   # Use compressed for hash detection
    KEEP_ORIGINAL_AFTER_COMPRESSION: bool = False  # Space-saving mode
    CLEANUP_TEMP_FILES: bool = True          # Clean up temporary files
    MAX_SCREENSHOT_AGE_HOURS: int = 24       # Auto-cleanup old screenshots
    
    # === Logging ===
    LOG_LEVEL: str = "INFO"                  # DEBUG, INFO, WARNING, ERROR
    LOG_FOCUS_SESSIONS: bool = True          # Track focus session analytics
    LOG_DISTRACTIONS: bool = True            # Log distraction events
    
    # === Development ===
    DEBUG_MODE: bool = False                 # Extra debugging output
    MOCK_THINGS3: bool = False              # Use mock task data for testing
    DISABLE_SCREENSHOTS: bool = False        # Skip screenshots (testing only)
    
    def __post_init__(self):
        """Validate configuration after initialization"""
        if self.TASK_REFRESH_INTERVAL < 5:
            raise ValueError("TASK_REFRESH_INTERVAL must be at least 5 seconds")
            
        if self.SCREENSHOT_INTERVAL < 0.5:
            raise ValueError("SCREENSHOT_INTERVAL must be at least 0.5 seconds")
            
        if not 0.0 <= self.HASH_CHANGE_THRESHOLD <= 1.0:
            raise ValueError("HASH_CHANGE_THRESHOLD must be between 0.0 and 1.0")
            
        if not 0.0 <= self.FOCUS_CONFIDENCE_THRESHOLD <= 1.0:
            raise ValueError("FOCUS_CONFIDENCE_THRESHOLD must be between 0.0 and 1.0")
    
    @classmethod
    def create_performance_mode(cls) -> 'TaskFocusConfig':
        """Create config optimized for performance"""
        return cls(
            SCREENSHOT_INTERVAL=3.0,
            HASH_CHANGE_THRESHOLD=0.2,
            VOICE_ALERTS_ENABLED=False,
            SCREENSHOT_COMPRESS=True,
            KEEP_ORIGINAL_AFTER_COMPRESSION=False,
            TASK_REFRESH_INTERVAL=60,  # Less frequent task checks
            LOG_LEVEL="WARNING"
        )
    
    @classmethod 
    def create_focus_mode(cls) -> 'TaskFocusConfig':
        """Create config optimized for focus sessions"""
        return cls(
            SCREENSHOT_INTERVAL=1.0,     # More sensitive monitoring
            HASH_CHANGE_THRESHOLD=0.1,   # Detect smaller changes
            VOICE_ALERTS_ENABLED=True,
            ESCALATING_ALERTS=True,
            ALERT_COOLDOWN_SECONDS=180,  # 3min cooldown
            TASK_REFRESH_INTERVAL=15,    # More frequent task updates
            CONTEXT_WINDOW_SECONDS=60,   # Longer context
            LOG_LEVEL="DEBUG"
        )
    
    @classmethod
    def create_space_saving_mode(cls) -> 'TaskFocusConfig':
        """Create config optimized for storage efficiency"""
        return cls(
            SCREENSHOT_COMPRESS=True,
            KEEP_ORIGINAL_AFTER_COMPRESSION=False,
            LLM_ANALYZE_COMPRESSED=True,
            HASH_DETECTION_ON_COMPRESSED=True,
            CLEANUP_TEMP_FILES=True,
            MAX_SCREENSHOT_AGE_HOURS=4,   # Cleanup more aggressively
            SCREENSHOT_RESOLUTION="quarter"
        )

# Default configuration instance
CONFIG = TaskFocusConfig()

def load_config_from_env() -> TaskFocusConfig:
    """Load configuration from environment variables"""
    config = TaskFocusConfig()
    
    # Override with environment variables if present
    if os.getenv('FOCUS_TASK_REFRESH_INTERVAL'):
        config.TASK_REFRESH_INTERVAL = int(os.getenv('FOCUS_TASK_REFRESH_INTERVAL'))
    
    if os.getenv('FOCUS_SCREENSHOT_INTERVAL'):
        config.SCREENSHOT_INTERVAL = float(os.getenv('FOCUS_SCREENSHOT_INTERVAL'))
        
    if os.getenv('FOCUS_VOICE_ALERTS'):
        config.VOICE_ALERTS_ENABLED = os.getenv('FOCUS_VOICE_ALERTS').lower() == 'true'
    
    if os.getenv('FOCUS_DEBUG_MODE'):
        config.DEBUG_MODE = os.getenv('FOCUS_DEBUG_MODE').lower() == 'true'
        
    return config

if __name__ == "__main__":
    print("🔧 Task Focus Configuration")
    print("="*40)
    
    # Test default config
    print("\n📋 Default Configuration:")
    print(f"Task refresh: {CONFIG.TASK_REFRESH_INTERVAL}s")
    print(f"Screenshot interval: {CONFIG.SCREENSHOT_INTERVAL}s")
    print(f"Voice alerts: {CONFIG.VOICE_ALERTS_ENABLED}")
    
    # Test performance mode
    perf_config = TaskFocusConfig.create_performance_mode()
    print(f"\n⚡ Performance Mode:")
    print(f"Task refresh: {perf_config.TASK_REFRESH_INTERVAL}s")
    print(f"Screenshot interval: {perf_config.SCREENSHOT_INTERVAL}s")
    print(f"Voice alerts: {perf_config.VOICE_ALERTS_ENABLED}")
    
    # Test focus mode
    focus_config = TaskFocusConfig.create_focus_mode()  
    print(f"\n🎯 Focus Mode:")
    print(f"Task refresh: {focus_config.TASK_REFRESH_INTERVAL}s")
    print(f"Screenshot interval: {focus_config.SCREENSHOT_INTERVAL}s")
    print(f"Alert cooldown: {focus_config.ALERT_COOLDOWN_SECONDS}s")