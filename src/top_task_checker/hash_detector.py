#!/usr/bin/env python3
"""
Hash Detector - Optimized screen change detection using perceptual hashing

Reduces LLM API calls by ~80% by only analyzing when screen content actually changes.
"""

import hashlib
import time
import logging
from pathlib import Path
from typing import Optional, Dict
import numpy as np
from PIL import Image

from config import TaskFocusConfig

logger = logging.getLogger(__name__)

class HashDetector:
    """Detects screen changes using perceptual hashing to optimize API calls"""
    
    def __init__(self, config: TaskFocusConfig):
        self.config = config
        self.last_hash: Optional[str] = None
        self.last_change_time: float = 0
        self.hash_cache: Dict[str, str] = {}  # Path -> hash mapping
        
    def has_screen_changed(self, screenshot_path: Path) -> bool:
        """
        Check if screen has changed significantly since last check
        
        Returns True if screen changed enough to warrant analysis
        """
        try:
            # Calculate hash of current screenshot
            current_hash = self._calculate_perceptual_hash(screenshot_path)
            if not current_hash:
                # If we can't calculate hash, assume change to be safe
                return True
            
            # Check if this is the first screenshot
            if self.last_hash is None:
                self.last_hash = current_hash
                self.last_change_time = time.time()
                logger.debug("First screenshot, assuming change")
                return True
            
            # Calculate similarity
            similarity = self._calculate_similarity(self.last_hash, current_hash)
            change_detected = similarity < (1.0 - self.config.HASH_CHANGE_THRESHOLD)
            
            if change_detected:
                logger.debug(f"Screen change detected (similarity: {similarity:.3f})")
                self.last_hash = current_hash
                self.last_change_time = time.time()
            else:
                logger.debug(f"No significant change (similarity: {similarity:.3f})")
            
            return change_detected
            
        except Exception as e:
            logger.error(f"Error in change detection: {e}")
            # On error, assume change to avoid missing important screen updates
            return True
    
    def _calculate_perceptual_hash(self, image_path: Path) -> Optional[str]:
        """
        Calculate perceptual hash of image for similarity comparison
        
        Uses average hash algorithm - fast and effective for screen changes
        """
        try:
            # Check cache first
            cache_key = f"{image_path}_{image_path.stat().st_mtime}"
            if cache_key in self.hash_cache:
                return self.hash_cache[cache_key]
            
            # Load and process image
            with Image.open(image_path) as img:
                # Convert to grayscale and resize to 8x8 for hash
                img = img.convert('L').resize((8, 8), Image.Resampling.LANCZOS)
                
                # Convert to numpy array
                pixels = np.array(img)
                
                # Calculate average pixel value
                avg = pixels.mean()
                
                # Create hash string - 1 for pixels above average, 0 for below
                hash_bits = []
                for row in pixels:
                    for pixel in row:
                        hash_bits.append('1' if pixel > avg else '0')
                
                hash_string = ''.join(hash_bits)
                
                # Cache the result
                self.hash_cache[cache_key] = hash_string
                
                # Clean old cache entries
                if len(self.hash_cache) > 100:
                    self._cleanup_hash_cache()
                
                return hash_string
                
        except Exception as e:
            logger.error(f"Failed to calculate perceptual hash: {e}")
            return None
    
    def _calculate_similarity(self, hash1: str, hash2: str) -> float:
        """
        Calculate similarity between two perceptual hashes
        
        Returns value between 0.0 (completely different) and 1.0 (identical)
        """
        if len(hash1) != len(hash2):
            return 0.0
        
        # Count matching bits
        matching_bits = sum(1 for a, b in zip(hash1, hash2) if a == b)
        
        # Return similarity ratio
        return matching_bits / len(hash1)
    
    def _cleanup_hash_cache(self):
        """Clean up old entries from hash cache"""
        try:
            # Keep only the 50 most recent entries
            cache_items = list(self.hash_cache.items())
            cache_items.sort(key=lambda x: x[0].split('_')[-1])  # Sort by timestamp
            
            # Keep last 50 entries
            self.hash_cache = dict(cache_items[-50:])
            
            logger.debug(f"Cleaned hash cache, {len(self.hash_cache)} entries remaining")
            
        except Exception as e:
            logger.error(f"Error cleaning hash cache: {e}")
    
    def get_change_stats(self) -> Dict:
        """Get statistics about change detection"""
        return {
            'last_change_time': self.last_change_time,
            'time_since_last_change': time.time() - self.last_change_time if self.last_change_time else 0,
            'cache_size': len(self.hash_cache),
            'threshold': self.config.HASH_CHANGE_THRESHOLD
        }
    
    def force_change_detection(self):
        """Force next check to detect change (useful for testing)"""
        self.last_hash = None
        logger.debug("Forced change detection reset")
    
    def test_change_detection(self, image1_path: Path, image2_path: Path) -> Dict:
        """Test change detection between two specific images"""
        print(f"🧪 Testing Change Detection")
        print(f"Image 1: {image1_path}")
        print(f"Image 2: {image2_path}")
        print("-" * 50)
        
        # Calculate hashes
        hash1 = self._calculate_perceptual_hash(image1_path)
        hash2 = self._calculate_perceptual_hash(image2_path)
        
        if not hash1 or not hash2:
            print("❌ Failed to calculate hashes")
            return {}
        
        # Calculate similarity
        similarity = self._calculate_similarity(hash1, hash2)
        change_detected = similarity < (1.0 - self.config.HASH_CHANGE_THRESHOLD)
        
        result = {
            'hash1': hash1[:16] + '...',  # Show first 16 chars
            'hash2': hash2[:16] + '...',
            'similarity': similarity,
            'threshold': self.config.HASH_CHANGE_THRESHOLD,
            'change_detected': change_detected
        }
        
        print(f"Hash 1: {result['hash1']}")
        print(f"Hash 2: {result['hash2']}")
        print(f"Similarity: {similarity:.3f}")
        print(f"Threshold: {self.config.HASH_CHANGE_THRESHOLD}")
        print(f"Change detected: {change_detected}")
        
        return result

# Alternative simple hash detector for systems without PIL
class SimpleHashDetector:
    """Simplified hash detector using file-based hashing"""
    
    def __init__(self, config: TaskFocusConfig):
        self.config = config
        self.last_hash: Optional[str] = None
    
    def has_screen_changed(self, screenshot_path: Path) -> bool:
        """Simple file-based change detection"""
        try:
            # Calculate MD5 hash of file
            with open(screenshot_path, 'rb') as f:
                current_hash = hashlib.md5(f.read()).hexdigest()
            
            if self.last_hash is None or current_hash != self.last_hash:
                self.last_hash = current_hash
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error in simple change detection: {e}")
            return True

if __name__ == "__main__":
    # Test the hash detector
    from config import CONFIG
    
    detector = HashDetector(CONFIG)
    
    # Test with sample images if available
    test_dir = Path("tests/top_task_checker/test_data")
    test_images = list(test_dir.glob("*.png")) if test_dir.exists() else []
    
    if len(test_images) >= 2:
        detector.test_change_detection(test_images[0], test_images[1])
    else:
        print("❌ Need at least 2 test images in tests/top_task_checker/test_data/")
        print("Add some sample screenshots to test change detection")