#!/usr/bin/env python3
"""
Focus Analyzer - LLM-based analysis of task focus from screenshots

Determines if user activity aligns with their top priority task.
"""

import base64
import json
import logging
from pathlib import Path
from typing import Dict, Optional
import openai
from config import TaskFocusConfig

logger = logging.getLogger(__name__)

class FocusAnalyzer:
    """Analyzes screenshots to determine if user is focused on target task"""
    
    def __init__(self, config: TaskFocusConfig):
        self.config = config
        
        # Initialize OpenAI client (assumes API key in environment)
        try:
            self.client = openai.OpenAI()
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            self.client = None
    
    def analyze_focus(self, screenshot_path: Path, target_task: str) -> Dict:
        """
        Analyze if screenshot shows focus on target task
        
        Returns:
            {
                'status': 'focused'|'distracted'|'unclear',
                'confidence': float(0.0-1.0),
                'reasoning': str,
                'details': str
            }
        """
        if not self.client:
            return self._create_fallback_result("LLM client not available")
        
        try:
            # Read and encode image
            image_data = self._encode_image(screenshot_path)
            if not image_data:
                return self._create_fallback_result("Could not read screenshot")
            
            # Create analysis prompt
            prompt = self._create_analysis_prompt(target_task)
            
            # Call LLM for analysis
            response = self._call_llm(prompt, image_data)
            
            # Parse response
            return self._parse_llm_response(response, target_task)
            
        except Exception as e:
            logger.error(f"Error in focus analysis: {e}")
            return self._create_fallback_result(f"Analysis error: {e}")
    
    def _encode_image(self, image_path: Path) -> Optional[str]:
        """Encode image to base64 for LLM analysis"""
        try:
            with open(image_path, 'rb') as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to encode image: {e}")
            return None
    
    def _create_analysis_prompt(self, target_task: str) -> str:
        """Create the analysis prompt for the LLM"""
        return f"""You are a focus coach analyzing a user's screen to determine if they are working on their priority task.

TARGET TASK: "{target_task}"

Please analyze the screenshot and determine:
1. What activity is the user currently engaged in?
2. Does this activity directly relate to their target task?
3. Is this focused work or a distraction?

Consider these factors:
- Task-related research, planning, or execution = FOCUSED
- Social media, entertainment, unrelated browsing = DISTRACTED  
- System tasks, quick email checks, brief interruptions = UNCLEAR
- Context matters: "unicorn video" task means YouTube could be focused work

Respond with a JSON object:
{{
    "status": "focused|distracted|unclear",
    "confidence": 0.0-1.0,
    "current_activity": "brief description",
    "reasoning": "why you classified it this way",
    "task_alignment": "how current activity relates to target task"
}}

Be specific about what you see and why it indicates focus or distraction."""

    def _call_llm(self, prompt: str, image_data: str) -> Optional[str]:
        """Call the LLM with the analysis prompt and image"""
        try:
            response = self.client.chat.completions.create(
                model=self.config.LLM_MODEL,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url", 
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_data}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500,
                temperature=0.1  # Lower temperature for more consistent analysis
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"LLM API call failed: {e}")
            return None
    
    def _parse_llm_response(self, response: Optional[str], target_task: str) -> Dict:
        """Parse LLM response into structured result"""
        if not response:
            return self._create_fallback_result("No LLM response")
        
        try:
            # Try to extract JSON from response
            response_clean = response.strip()
            if response_clean.startswith('```json'):
                response_clean = response_clean[7:-3]
            elif response_clean.startswith('```'):
                response_clean = response_clean[3:-3]
            
            parsed = json.loads(response_clean)
            
            # Validate required fields
            status = parsed.get('status', 'unclear').lower()
            if status not in ['focused', 'distracted', 'unclear']:
                status = 'unclear'
            
            confidence = float(parsed.get('confidence', 0.5))
            confidence = max(0.0, min(1.0, confidence))  # Clamp to 0-1
            
            return {
                'status': status,
                'confidence': confidence,
                'reasoning': parsed.get('reasoning', 'No reasoning provided'),
                'current_activity': parsed.get('current_activity', 'Unknown activity'),
                'task_alignment': parsed.get('task_alignment', 'Unknown alignment'),
                'details': f"Activity: {parsed.get('current_activity', 'Unknown')}\n"
                          f"Alignment: {parsed.get('task_alignment', 'Unknown')}"
            }
            
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error(f"Failed to parse LLM response: {e}")
            logger.debug(f"Raw response: {response}")
            
            # Fallback: try to extract status from text
            response_lower = response.lower()
            if 'focused' in response_lower and 'distracted' not in response_lower:
                status = 'focused'
                confidence = 0.6
            elif 'distracted' in response_lower:
                status = 'distracted' 
                confidence = 0.6
            else:
                status = 'unclear'
                confidence = 0.3
            
            return {
                'status': status,
                'confidence': confidence,
                'reasoning': response[:200] + '...' if len(response) > 200 else response,
                'current_activity': 'Could not parse activity',
                'task_alignment': 'Could not parse alignment',
                'details': f"Raw LLM response (parsing failed): {response[:100]}..."
            }
    
    def _create_fallback_result(self, error_msg: str) -> Dict:
        """Create fallback result when analysis fails"""
        return {
            'status': 'unclear',
            'confidence': 0.0,
            'reasoning': error_msg,
            'current_activity': 'Analysis failed',
            'task_alignment': 'Unknown due to error',
            'details': error_msg
        }
    
    def test_analysis(self, test_image_path: Path, test_task: str) -> None:
        """Test the focus analyzer with a specific image and task"""
        print(f"🧪 Testing Focus Analyzer")
        print(f"Task: '{test_task}'")
        print(f"Image: {test_image_path}")
        print("-" * 50)
        
        result = self.analyze_focus(test_image_path, test_task)
        
        print(f"Status: {result['status']} (confidence: {result['confidence']:.2f})")
        print(f"Activity: {result['current_activity']}")
        print(f"Reasoning: {result['reasoning']}")
        print(f"Task Alignment: {result['task_alignment']}")

if __name__ == "__main__":
    # Test the focus analyzer
    from config import CONFIG
    
    analyzer = FocusAnalyzer(CONFIG)
    
    # Test with a sample task
    test_task = "watch baby unicorns on youtube"
    test_image = Path("tests/top_task_checker/test_data/sample_screenshot.png")
    
    if test_image.exists():
        analyzer.test_analysis(test_image, test_task)
    else:
        print(f"❌ Test image not found: {test_image}")
        print("Create a test screenshot to test the analyzer")