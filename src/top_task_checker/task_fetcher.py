#!/usr/bin/env python3
"""
Top Task Fetcher - Gets current top priority task from Things3

Handles task refresh logic and change detection for focus monitoring.
"""

import subprocess
import time
import logging
from typing import Optional, Tuple

logger = logging.getLogger(__name__)

class TaskFetcher:
    """Manages fetching and caching of top priority task from Things3"""
    
    def __init__(self, refresh_interval: int = 30):
        self.refresh_interval = refresh_interval
        self.last_fetch_time: float = 0
        self.current_task: Optional[str] = None
        self.task_changed_callback = None
        
    def set_task_change_callback(self, callback):
        """Set callback function to call when task changes"""
        self.task_changed_callback = callback
        
    def get_current_task(self) -> Optional[str]:
        """Get current top task, refreshing if needed"""
        now = time.time()
        
        # Force refresh if no current task or interval expired
        if (self.current_task is None or 
            now - self.last_fetch_time >= self.refresh_interval):
            return self._refresh_task()
            
        return self.current_task
    
    def force_refresh(self) -> Optional[str]:
        """Force immediate task refresh regardless of interval"""
        return self._refresh_task()
        
    def _refresh_task(self) -> Optional[str]:
        """Fetch latest top task from Things3"""
        try:
            logger.debug("Fetching top task from Things3...")
            
            # Call our optimized Things3 script
            result = subprocess.run([
                'python', 'src/tasklist/things3_import.py', '--top-task-only'
            ], capture_output=True, text=True, timeout=10, cwd='.')
            
            if result.returncode == 0:
                new_task = result.stdout.strip()
                
                # Check for task changes
                if new_task != self.current_task:
                    old_task = self.current_task
                    self.current_task = new_task
                    self.last_fetch_time = time.time()
                    
                    logger.info(f"Task changed: '{old_task}' → '{new_task}'")
                    
                    # Notify callback if registered
                    if self.task_changed_callback:
                        self.task_changed_callback(old_task, new_task)
                        
                    return new_task
                else:
                    # Task unchanged, just update timestamp
                    self.last_fetch_time = time.time()
                    logger.debug(f"Task unchanged: '{self.current_task}'")
                    return self.current_task
                    
            else:
                logger.error(f"Things3 script failed: {result.stderr}")
                return self.current_task  # Keep previous task on error
                
        except subprocess.TimeoutExpired:
            logger.error("Things3 script timed out")
            return self.current_task
        except Exception as e:
            logger.error(f"Error fetching task: {e}")
            return self.current_task
    
    def get_task_age(self) -> float:
        """Get seconds since last successful task fetch"""
        if self.last_fetch_time == 0:
            return float('inf')
        return time.time() - self.last_fetch_time
    
    def is_task_stale(self) -> bool:
        """Check if current task data is stale"""
        return self.get_task_age() > self.refresh_interval * 2

def get_top_task() -> Optional[str]:
    """Convenience function to get current top task"""
    fetcher = TaskFetcher()
    return fetcher.get_current_task()

if __name__ == "__main__":
    # Test the task fetcher
    logging.basicConfig(level=logging.DEBUG)
    
    def on_task_change(old_task, new_task):
        print(f"🔄 Task changed: {old_task} → {new_task}")
    
    fetcher = TaskFetcher(refresh_interval=5)  # 5s for testing
    fetcher.set_task_change_callback(on_task_change)
    
    print("🎯 Task Fetcher Test")
    print("="*30)
    
    for i in range(10):
        task = fetcher.get_current_task()
        age = fetcher.get_task_age()
        print(f"Cycle {i+1}: '{task}' (age: {age:.1f}s)")
        time.sleep(6)  # Trigger refresh every other cycle