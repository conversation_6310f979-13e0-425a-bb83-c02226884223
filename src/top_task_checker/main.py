#!/usr/bin/env python3
"""
Top Task Checker - Main monitoring system

Monitors user focus on their top priority task from Things3 using
screenshot analysis and LLM-based focus detection.
"""

import time
import logging
import signal
import sys
from pathlib import Path
from typing import Optional

from config import CONFIG, TaskFocusConfig
from task_fetcher import TaskFetcher
from focus_analyzer import <PERSON><PERSON><PERSON>yzer
from hash_detector import HashDetector
from alerter import Alerter

# Import screenshot functionality from existing system
sys.path.append(str(Path(__file__).parent.parent / "will_detector"))
from screenshot import take_screenshot, compress_image

logger = logging.getLogger(__name__)

class TopTaskChecker:
    """Main monitoring system for top task focus"""
    
    def __init__(self, config: Optional[TaskFocusConfig] = None):
        self.config = config or CONFIG
        self.running = False
        
        # Initialize components
        self.task_fetcher = TaskFetcher(self.config.TASK_REFRESH_INTERVAL)
        self.focus_analyzer = FocusAnalyzer(self.config)
        self.hash_detector = HashDetector(self.config)
        self.alerter = Alerter(self.config)
        
        # State tracking
        self.current_task: Optional[str] = None
        self.last_screenshot_time = 0
        self.focus_session_start: Optional[float] = None
        self.distraction_count = 0
        
        # Setup logging
        self._setup_logging()
        
        # Setup callbacks
        self.task_fetcher.set_task_change_callback(self._on_task_change)
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _setup_logging(self):
        """Configure logging based on config"""
        log_level = getattr(logging, self.config.LOG_LEVEL.upper())
        
        # Create logs directory
        log_dir = Path("src/top_task_checker/data/logs")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'focus_monitor.log'),
                logging.StreamHandler(sys.stdout) if self.config.DEBUG_MODE else logging.NullHandler()
            ]
        )
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)
    
    def _on_task_change(self, old_task: Optional[str], new_task: Optional[str]):
        """Handle task changes"""
        logger.info(f"Task change detected: '{old_task}' → '{new_task}'")
        
        # End current focus session if active
        if self.focus_session_start:
            session_duration = time.time() - self.focus_session_start
            logger.info(f"Focus session ended: {session_duration:.1f}s on '{old_task}'")
            self.focus_session_start = None
        
        # Update current task
        self.current_task = new_task
        
        # Reset distraction counter for new task
        self.distraction_count = 0
        
        # Announce new task
        if self.config.ANNOUNCE_TASK_CHANGES and new_task:
            self.alerter.announce_task_change(old_task, new_task)
    
    def start(self):
        """Start the monitoring system"""
        if self.running:
            logger.warning("Monitor already running")
            return
        
        logger.info("🎯 Starting Top Task Checker")
        logger.info(f"Config: {self.config.SCREENSHOT_INTERVAL}s intervals, {self.config.TASK_REFRESH_INTERVAL}s task refresh")
        
        # Get initial task
        self.current_task = self.task_fetcher.get_current_task()
        if not self.current_task:
            logger.error("❌ Could not fetch initial task from Things3")
            return
        
        logger.info(f"🎯 Initial focus target: '{self.current_task}'")
        self.alerter.announce_monitoring_start(self.current_task)
        
        self.running = True
        self.focus_session_start = time.time()
        
        # Start monitoring loop
        self._monitoring_loop()
    
    def stop(self):
        """Stop the monitoring system"""
        if not self.running:
            return
        
        logger.info("🛑 Stopping Top Task Checker")
        
        # Log final focus session
        if self.focus_session_start:
            session_duration = time.time() - self.focus_session_start
            logger.info(f"Final focus session: {session_duration:.1f}s")
        
        self.running = False
        
        # Cleanup if needed
        if self.config.CLEANUP_TEMP_FILES:
            self._cleanup_old_files()
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.running:
            try:
                current_time = time.time()
                
                # Update current task (handles refresh interval internally)
                self.task_fetcher.get_current_task()
                
                # Check if we need to take a screenshot
                if current_time - self.last_screenshot_time >= self.config.SCREENSHOT_INTERVAL:
                    self._process_screenshot()
                    self.last_screenshot_time = current_time
                
                # Short sleep to prevent busy waiting
                time.sleep(0.1)
                
            except KeyboardInterrupt:
                logger.info("Interrupted by user")
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                if self.config.DEBUG_MODE:
                    import traceback
                    traceback.print_exc()
                time.sleep(1)  # Prevent rapid error loops
    
    def _process_screenshot(self):
        """Take and analyze screenshot for focus detection"""
        if not self.current_task:
            return
        
        try:
            # Take screenshot
            screenshot_path = self._take_screenshot()
            if not screenshot_path:
                return
            
            # Check for screen changes using hash detection
            if not self.hash_detector.has_screen_changed(screenshot_path):
                logger.debug("No screen change detected, skipping analysis")
                return
            
            logger.debug("Screen change detected, analyzing focus...")
            
            # Analyze focus on the screenshot
            focus_result = self.focus_analyzer.analyze_focus(
                screenshot_path, 
                self.current_task
            )
            
            self._handle_focus_result(focus_result)
            
        except Exception as e:
            logger.error(f"Error processing screenshot: {e}")
    
    def _take_screenshot(self) -> Optional[Path]:
        """Take screenshot and handle compression"""
        try:
            # Create screenshots directory
            screenshot_dir = Path("src/top_task_checker/data/screenshots")
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            
            # Take screenshot
            timestamp = int(time.time())
            screenshot_path = screenshot_dir / f"focus_{timestamp}.png"
            
            if self.config.DISABLE_SCREENSHOTS:
                logger.debug("Screenshots disabled in config")
                return None
            
            # Use existing screenshot functionality
            take_screenshot(
                str(screenshot_path),
                resolution=self.config.SCREENSHOT_RESOLUTION
            )
            
            # Handle compression if enabled
            if self.config.SCREENSHOT_COMPRESS:
                compressed_path = screenshot_dir / f"focus_{timestamp}_compressed.png"
                compress_image(
                    str(screenshot_path),
                    str(compressed_path),
                    method=self.config.SCREENSHOT_COMPRESSION_METHOD
                )
                
                # Use compressed version for analysis if configured
                analysis_path = compressed_path if self.config.LLM_ANALYZE_COMPRESSED else screenshot_path
                
                # Clean up original if not keeping it
                if not self.config.KEEP_ORIGINAL_AFTER_COMPRESSION:
                    screenshot_path.unlink()
                    return compressed_path
                    
                return analysis_path
            
            return screenshot_path
            
        except Exception as e:
            logger.error(f"Failed to take screenshot: {e}")
            return None
    
    def _handle_focus_result(self, focus_result: dict):
        """Handle the result of focus analysis"""
        status = focus_result.get('status', 'unclear')
        confidence = focus_result.get('confidence', 0.0)
        reasoning = focus_result.get('reasoning', '')
        
        logger.info(f"Focus analysis: {status} (confidence: {confidence:.2f})")
        
        if self.config.LOG_FOCUS_SESSIONS:
            self._log_focus_event(status, confidence, reasoning)
        
        # Handle different focus states
        if status == 'focused' and confidence >= self.config.FOCUS_CONFIDENCE_THRESHOLD:
            # User is focused, reset distraction counter
            self.distraction_count = 0
            logger.debug("✅ User appears focused on target task")
            
        elif status == 'distracted' and confidence >= self.config.FOCUS_CONFIDENCE_THRESHOLD:
            # User is distracted
            self.distraction_count += 1
            logger.warning(f"⚠️  Distraction detected ({self.distraction_count}): {reasoning}")
            
            if self.config.LOG_DISTRACTIONS:
                self._log_distraction_event(reasoning)
            
            # Send alert based on configuration
            self.alerter.send_distraction_alert(
                self.current_task,
                reasoning,
                self.distraction_count
            )
        
        else:
            logger.debug(f"Unclear focus state: {reasoning}")
    
    def _log_focus_event(self, status: str, confidence: float, reasoning: str):
        """Log focus events for analytics"""
        session_duration = time.time() - (self.focus_session_start or time.time())
        
        focus_data = {
            'timestamp': time.time(),
            'task': self.current_task,
            'status': status,
            'confidence': confidence,
            'reasoning': reasoning,
            'session_duration': session_duration,
            'distraction_count': self.distraction_count
        }
        
        # Log to file (could be JSON for analysis)
        logger.info(f"FOCUS_EVENT: {focus_data}")
    
    def _log_distraction_event(self, reasoning: str):
        """Log distraction events"""
        distraction_data = {
            'timestamp': time.time(),
            'task': self.current_task,
            'reasoning': reasoning,
            'count': self.distraction_count
        }
        
        logger.warning(f"DISTRACTION_EVENT: {distraction_data}")
    
    def _cleanup_old_files(self):
        """Clean up old screenshots and logs"""
        try:
            screenshot_dir = Path("src/top_task_checker/data/screenshots")
            if not screenshot_dir.exists():
                return
            
            max_age = self.config.MAX_SCREENSHOT_AGE_HOURS * 3600
            current_time = time.time()
            
            deleted_count = 0
            for file_path in screenshot_dir.glob("focus_*.png"):
                if current_time - file_path.stat().st_mtime > max_age:
                    file_path.unlink()
                    deleted_count += 1
            
            if deleted_count > 0:
                logger.info(f"🧹 Cleaned up {deleted_count} old screenshot files")
                
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Top Task Focus Monitor')
    parser.add_argument('--config-mode', choices=['default', 'performance', 'focus', 'space-saving'],
                       default='default', help='Configuration preset to use')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    args = parser.parse_args()
    
    # Load configuration based on mode
    if args.config_mode == 'performance':
        config = TaskFocusConfig.create_performance_mode()
    elif args.config_mode == 'focus':
        config = TaskFocusConfig.create_focus_mode()
    elif args.config_mode == 'space-saving':
        config = TaskFocusConfig.create_space_saving_mode()
    else:
        config = CONFIG
    
    if args.debug:
        config.DEBUG_MODE = True
        config.LOG_LEVEL = 'DEBUG'
    
    # Start monitoring
    monitor = TopTaskChecker(config)
    
    try:
        monitor.start()
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
    finally:
        monitor.stop()

if __name__ == "__main__":
    main()