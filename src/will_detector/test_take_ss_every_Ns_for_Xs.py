#!/usr/bin/env python3
"""
Unit tests for the screenshot testing system.
Run with: python -m pytest src/will_detector/test_take_ss_every_Ns_for_Xs.py -v
"""

import asyncio
import time
from pathlib import Path
from unittest.mock import Mock, patch

import pytest

from .classifier import MessageDetector
from .run_config import CFG
from .screenshot import capture_active_window_screenshot
from .storage import SightingStorage
from .take_ss_every_Ns_for_Xs import ScreenshotTester


class TestScreenshotTester:
    """Test the ScreenshotTester class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.tester = ScreenshotTester(interval=1, duration=5)

    def test_init(self):
        """Test ScreenshotTester initialization."""
        assert self.tester.interval == 1
        assert self.tester.duration == 5
        assert isinstance(self.tester.detector, MessageDetector)
        assert isinstance(self.tester.storage, SightingStorage)
        assert self.tester.results == []
        assert self.tester.start_time is None

    @pytest.mark.asyncio
    @patch("src.will_detector.take_ss_every_Ns_for_Xs.capture_active_window_screenshot")
    @patch(
        "src.will_detector.take_ss_every_Ns_for_Xs.MessageDetector.analyze_screenshot"
    )
    @patch("src.will_detector.take_ss_every_Ns_for_Xs.SightingStorage.add_sighting")
    async def test_take_screenshot_and_analyze_success(
        self, mock_add_sighting, mock_analyze, mock_capture
    ):
        """Test successful screenshot and analysis."""
        # Mock screenshot capture
        mock_capture.return_value = {
            "path": "/test/screenshot.png",
            "timestamp": "20250101_120000",
            "app_name": "TestApp",
            "window_title": "Test Window",
        }

        # Mock LLM analysis
        mock_analyze.return_value = {
            "willSighting": True,
            "reasoning": "Test reasoning",
            "verbatimQuote": "Test quote",
        }

        # Mock storage
        mock_add_sighting.return_value = True

        result = await self.tester.take_screenshot_and_analyze(1)

        assert result["iteration"] == 1
        assert result["app_name"] == "TestApp"
        assert result["window_title"] == "Test Window"
        assert result["will_detected"] is True
        assert result["is_new_sighting"] is True
        assert result["reasoning"] == "Test reasoning"
        assert result["verbatim_quote"] == "Test quote"

    @pytest.mark.asyncio
    @patch("src.will_detector.take_ss_every_Ns_for_Xs.capture_active_window_screenshot")
    async def test_take_screenshot_and_analyze_error(self, mock_capture):
        """Test error handling in screenshot and analysis."""
        mock_capture.side_effect = Exception("Test error")

        result = await self.tester.take_screenshot_and_analyze(1)

        assert result["iteration"] == 1
        assert result["app_name"] == "ERROR"
        assert result["window_title"] == "ERROR"
        assert result["will_detected"] is False
        assert result["is_new_sighting"] is False
        assert "Test error" in result["reasoning"]

    def test_create_results_table(self):
        """Test table creation."""
        # Add some test results
        self.tester.results = [
            {
                "iteration": 1,
                "app_name": "TestApp",
                "window_title": "Test Window",
                "will_detected": True,
                "is_new_sighting": True,
                "verbatim_quote": "Test quote",
                "file_size_mb": 0.1,
            }
        ]

        table = self.tester.create_results_table()
        assert table.title == "Will Detection Results - 1 screenshots taken"
        assert (
            len(table.columns) == 7
        )  # Iter, App, Window, Will Detected, New Sighting, File Size, Quote

    def test_create_summary_panel(self):
        """Test summary panel creation."""
        # Add some test results
        self.tester.results = [
            {"will_detected": True, "is_new_sighting": True, "app_name": "App1"},
            {"will_detected": False, "is_new_sighting": False, "app_name": "App2"},
            {"will_detected": True, "is_new_sighting": False, "app_name": "App1"},
        ]

        panel = self.tester.create_summary_panel()
        assert "Total Screenshots: 3" in panel.renderable
        assert "Will Detections: 2" in panel.renderable
        assert "New Sightings: 1" in panel.renderable
        assert "Unique Apps Tested: 2" in panel.renderable


class TestTimingIssues:
    """Test timing-related issues that might cause early termination."""

    @pytest.mark.asyncio
    @patch("src.will_detector.take_ss_every_Ns_for_Xs.capture_active_window_screenshot")
    @patch(
        "src.will_detector.take_ss_every_Ns_for_Xs.MessageDetector.analyze_screenshot"
    )
    @patch("src.will_detector.take_ss_every_Ns_for_Xs.SightingStorage.add_sighting")
    @patch("src.will_detector.take_ss_every_Ns_for_Xs.asyncio.sleep")
    async def test_run_timing_accuracy(
        self, mock_sleep, mock_add_sighting, mock_analyze, mock_capture
    ):
        """Test that the run method respects timing constraints."""
        # Mock all the dependencies
        mock_capture.return_value = {
            "path": "/test/screenshot.png",
            "timestamp": "20250101_120000",
            "app_name": "TestApp",
            "window_title": "Test Window",
        }
        mock_analyze.return_value = {
            "willSighting": False,
            "reasoning": "Test reasoning",
            "verbatimQuote": None,
        }
        mock_add_sighting.return_value = False

        # Create a short test
        tester = ScreenshotTester(
            interval=0.1, duration=0.5
        )  # 0.5 seconds, 0.1 interval = ~5 screenshots

        start_time = time.time()
        await tester.run()
        end_time = time.time()

        # Should run for approximately the duration
        assert 0.4 <= (end_time - start_time) <= 0.8  # Allow some tolerance

        # Should have taken multiple screenshots
        assert (
            len(tester.results) >= 3
        )  # Should have at least 3 screenshots in 0.5s with 0.1s interval

        # Should have called sleep the right number of times
        assert mock_sleep.call_count >= 2  # At least 2 sleep calls for 3+ screenshots

    @pytest.mark.asyncio
    @patch("src.will_detector.take_ss_every_Ns_for_Xs.capture_active_window_screenshot")
    @patch(
        "src.will_detector.take_ss_every_Ns_for_Xs.MessageDetector.analyze_screenshot"
    )
    @patch("src.will_detector.take_ss_every_Ns_for_Xs.SightingStorage.add_sighting")
    async def test_run_with_slow_llm(
        self, mock_add_sighting, mock_analyze, mock_capture
    ):
        """Test that slow LLM responses don't break the timing."""

        # Mock slow LLM response
        async def slow_analyze(*args, **kwargs):
            await asyncio.sleep(0.2)  # Simulate slow LLM
            return {
                "willSighting": False,
                "reasoning": "Slow reasoning",
                "verbatimQuote": None,
            }

        mock_analyze.side_effect = slow_analyze
        mock_capture.return_value = {
            "path": "/test/screenshot.png",
            "timestamp": "20250101_120000",
            "app_name": "TestApp",
            "window_title": "Test Window",
        }
        mock_add_sighting.return_value = False

        # Create a test with longer duration to account for slow LLM
        tester = ScreenshotTester(interval=0.3, duration=1.0)

        start_time = time.time()
        await tester.run()
        end_time = time.time()

        # Should still complete within reasonable time
        assert (end_time - start_time) <= 2.0  # Allow extra time for slow LLM

        # Should have taken some screenshots despite slow LLM
        assert len(tester.results) >= 2


class TestIntegration:
    """Integration tests for the full system."""

    def test_screenshot_capture_integration(self):
        """Test that screenshot capture actually works."""
        # This test requires a real screenshot, so we'll test the function exists
        assert hasattr(capture_active_window_screenshot, "__call__")

        # Test that it returns the expected structure
        with patch("src.will_detector.screenshot._run_peekaboo") as mock_peekaboo:
            mock_peekaboo.return_value = Mock(stdout="Test output")

            result = capture_active_window_screenshot(Path("/tmp"))

            assert "path" in result
            assert "timestamp" in result
            assert "app_name" in result
            assert "window_title" in result

    def test_classifier_integration(self):
        """Test that the classifier can be instantiated."""
        detector = MessageDetector(provider="openai")
        assert detector.provider == "openai"

        # Test with anthropic
        detector = MessageDetector(provider="anthropic")
        assert detector.provider == "anthropic"

    def test_storage_integration(self):
        """Test that storage can be instantiated."""
        storage = SightingStorage(Path("/tmp/test_sightings.json"))
        assert storage.sightings == []

        # Test adding a sighting
        test_screenshot = {
            "path": "/test/path.png",
            "timestamp": "20250101_120000",
            "app_name": "TestApp",
            "window_title": "Test Window",
        }
        test_result = {
            "willSighting": True,
            "reasoning": "Test reasoning",
            "verbatimQuote": "Test quote",
        }

        result = storage.add_sighting(test_screenshot, test_result)
        assert result is True
        assert len(storage.sightings) == 1


class TestConfiguration:
    """Test configuration and environment setup."""

    def test_cfg_attributes(self):
        """Test that CFG has all required attributes."""
        assert hasattr(CFG, "LLM_PROVIDER")
        assert hasattr(CFG, "SCREENSHOT_DIR")
        assert hasattr(CFG, "SCREENSHOT_INTERVAL")
        assert hasattr(CFG, "SIGHTINGS_FILE")

    def test_screenshot_dir_exists(self):
        """Test that screenshot directory can be created."""
        test_dir = Path("/tmp/test_screenshots")
        test_dir.mkdir(exist_ok=True)

        assert test_dir.exists()
        assert test_dir.is_dir()


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
