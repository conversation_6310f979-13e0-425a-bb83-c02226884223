import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()


class CFG:
    LLM_PROVIDER = os.getenv("LLM_PROVIDER", "openai")
    SCREENSHOT_DIR = Path(os.getenv("SCREENSHOT_DIR", "src/will_detector/data/screenshots/"))
    SCREENSHOT_INTERVAL = int(os.getenv("SCREENSHOT_INTERVAL", "30"))
    SIGHTINGS_FILE = Path("src/will_detector/data/sightings.json")
    
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")