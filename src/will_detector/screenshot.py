import re
import subprocess
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for img_compression import
sys.path.append(str(Path(__file__).parent.parent))


def get_active_app_info():
    """Get active app name using AppleScript (reliable fallback)."""
    script = """
    tell application "System Events"
        set frontApp to name of first application process whose frontmost is true
        set windowTitle to ""
        try
            tell process frontApp
                set windowTitle to title of front window
            end tell
        end try
        return {frontApp, windowTitle}
    end tell
    """

    result = subprocess.run(["osascript", "-e", script], capture_output=True, text=True)

    output = result.stdout.strip()
    parts = output.split(", ")

    app_name = parts[0] if parts else "Unknown"
    window_title = ", ".join(parts[1:]) if len(parts) > 1 else ""

    return app_name, window_title


def _run_peekaboo(cmd: str) -> subprocess.CompletedProcess:
    """Run a Peekaboo command and return CompletedProcess."""
    return subprocess.run(
        cmd.split(),
        capture_output=True,
        text=True,
        cwd=Path.cwd(),  # Ensure we're in the right directory
    )


def _get_peekaboo_window_info(app_name: str) -> dict:
    """Get detailed window info from Peekaboo."""
    try:
        result = _run_peekaboo(f'peekaboo list windows --app "{app_name}"')
        output = result.stdout

        # Parse the first window (frontmost)
        lines = [line.strip() for line in output.splitlines() if line.strip()]
        if not lines:
            return {"title": "Unknown", "screen": "Unknown"}

        # Skip header lines and find first numbered window
        for line in lines:
            if re.match(r'^\d+\.\s*"', line):
                # Extract title from quotes
                title_match = re.search(r'"([^"]+)"', line)
                title = title_match.group(1) if title_match else line.strip()

                # Extract screen info if present
                screen_match = re.search(r"Screen:\s*([^,]+)", line)
                screen = screen_match.group(1).strip() if screen_match else "Unknown"

                return {"title": title, "screen": screen}

        return {"title": "Unknown", "screen": "Unknown"}

    except subprocess.CalledProcessError:
        return {"title": "Unknown", "screen": "Unknown"}


def capture_active_window_screenshot(output_dir: Path, compress: bool = False):
    """
    Capture ONLY the active window using Peekaboo.

    Args:
        output_dir: Directory to save screenshots
        compress: If True, apply fast binarization compression for OCR optimization
    """
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    screenshot_path = output_dir / f"{timestamp}.png"

    # Get active app info first
    app_name, fallback_window_title = get_active_app_info()

    # First try Peekaboo for active window capture
    peekaboo_success = False
    try:
        result = _run_peekaboo(
            f"peekaboo image --mode frontmost --path {screenshot_path.absolute()}"
        )

        # Check if Peekaboo actually succeeded
        if result.stderr and (
            "Error:" in result.stderr or "noWindowsFound" in result.stderr
        ):
            # Import console here to avoid circular imports
            try:
                from rich.console import Console
                console = Console()
                console.print(f"[dim red]Peekaboo failed with error: {result.stderr.strip()}[/dim red]")
            except ImportError:
                print(f"Peekaboo failed with error: {result.stderr.strip()}")
            raise subprocess.CalledProcessError(1, "peekaboo", output=result.stderr)

        # Check if file was actually created and has reasonable size
        if not screenshot_path.exists():
            try:
                from rich.console import Console
                console = Console()
                console.print("[dim red]Peekaboo: No screenshot file created[/dim red]")
            except ImportError:
                print("Peekaboo: No screenshot file created")
            raise subprocess.CalledProcessError(
                1, "peekaboo", output="No screenshot file created"
            )

        # Check file size - if too small, likely a failed capture
        file_size = screenshot_path.stat().st_size
        if file_size < 5000:  # Less than 5KB likely indicates a failed capture
            try:
                from rich.console import Console
                console = Console()
                console.print(f"[dim red]Peekaboo: Screenshot too small ({file_size} bytes), likely failed[/dim red]")
            except ImportError:
                print(f"Peekaboo: Screenshot too small ({file_size} bytes), likely failed")
            screenshot_path.unlink()  # Remove the small file
            raise subprocess.CalledProcessError(
                1, "peekaboo", output="Screenshot too small"
            )

        peekaboo_success = True
        file_size_mb = file_size / (1024 * 1024)
        print(f"Peekaboo: Success! Screenshot size: {file_size_mb:.2f} MB")

        # Get detailed window info from Peekaboo
        window_info = _get_peekaboo_window_info(app_name)
        window_title = window_info["title"]

        # Fallback to AppleScript if Peekaboo parsing fails
        if window_title == "Unknown" or window_title == "Windows for Cursor":
            window_title = fallback_window_title

    except subprocess.CalledProcessError as e:
        try:
            from rich.console import Console
            console = Console()
            console.print(f"[dim red]Peekaboo failed: {e}[/dim red]")
            # Add hint about log file for debugging
            logs_dir = Path(__file__).parent.parent / "basic_checker_tasklist" / "data" / "screenshots" / "logs"
            if logs_dir.exists():
                latest_log = max(logs_dir.glob("*.json"), key=lambda x: x.stat().st_mtime, default=None)
                if latest_log:
                    console.print(f"[dim yellow]📋 For debug details see: {latest_log}[/dim yellow]")
        except (ImportError, Exception):
            print(f"Peekaboo failed: {e}")
        peekaboo_success = False

    # If Peekaboo failed, use full desktop screencapture as fallback
    if not peekaboo_success:
        try:
            from rich.console import Console
            console = Console()
            console.print("[dim red]Using screencapture fallback for full desktop[/dim red]")
        except ImportError:
            print("Using screencapture fallback for full desktop")
        try:
            result = subprocess.run(
                [
                    "screencapture",
                    "-x",  # no sound
                    str(screenshot_path),
                ],
                capture_output=True,
                text=True,
                check=True,
            )

            # Verify screencapture worked
            if screenshot_path.exists():
                file_size = screenshot_path.stat().st_size
                file_size_mb = file_size / (1024 * 1024)
                print(f"Screencapture: Success! Screenshot size: {file_size_mb:.2f} MB")
            else:
                print("Screencapture: Failed to create file")

        except subprocess.CalledProcessError as e:
            print(f"Screencapture also failed: {e}")

        window_title = fallback_window_title

    # Apply compression if requested
    compressed_path = None
    compression_info = None

    if compress:
        try:
            from img_compression.compress_utils import compress_for_will_detector

            compression_info = compress_for_will_detector(str(screenshot_path))
            compressed_path = compression_info["output_path"]
        except ImportError:
            print("Warning: img_compression module not available, skipping compression")
        except Exception as e:
            print(f"Warning: Compression failed: {e}")

    return {
        "path": str(screenshot_path),
        "compressed_path": compressed_path,
        "compression_info": compression_info,
        "timestamp": timestamp,
        "app_name": app_name,
        "window_title": window_title,
    }


if __name__ == "__main__":
    screenshots_dir = Path(__file__).parent / "data" / "screenshots"
    result = capture_active_window_screenshot(screenshots_dir)
    print(f"Screenshot saved: {result['path']}")
    print(f"Active app: {result['app_name']}")
    print(f"Window title: {result['window_title']}")
