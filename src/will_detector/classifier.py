import base64
import json
import os
from pathlib import Path

from anthropic import Anthropic
from dotenv import load_dotenv
from openai import OpenAI

load_dotenv()

config = {
    "model_openai": "gpt-5",
    "model_anthropic": "sonnet-4-latest",
    "provider": "openai",
    "max_tokens": None,  # do not use max_tokens at all please.
    "image_url": "data:image/png;base64,{base64_image}",
}


class MessageDetector:
    def __init__(self, provider="openai"):
        self.provider = provider
        if provider == "openai":
            self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        elif provider == "anthropic":
            self.client = Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))

    def encode_image(self, image_path):
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode("utf-8")

    def analyze_screenshot(self, screenshot_path):
        base64_image = self.encode_image(screenshot_path)

        prompt = """Analyze this screenshot and determine if there is a message from <PERSON> visible.
        
        Return a JSON response with:
        - "reasoning": Your analysis of what you see
        - "willSighting": true if you see a message from <PERSON>, false otherwise
        - "verbatimQuote": If willSighting is true, provide the EXACT text of the message from William. If false, set to null.
            - if multiple messages from Will then return all of them e.g., 'Slack: Will: xxxxx'....'yyyyyyy'.
        Be very precise - only return willSighting=true if you can clearly see a message FROM William Hayes (not TO him).
        Look for indicators like sender names, profile pictures, or message headers showing William Hayes as the sender."""

        if (
            self.provider == "openai"
        ):  # TODO - not sure this should be here but ok for now.
            response = self.client.chat.completions.create(
                model=config["model_openai"],
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                },
                            },
                        ],
                    }
                ],
                response_format={"type": "json_object"},
                # max_tokens=config["max_tokens"],  # Removed as it's None
            )

            result = json.loads(response.choices[0].message.content)

        elif self.provider == "anthropic":
            response = self.client.messages.create(
                model=config["model_anthropic"],
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt + "\n\nRespond with valid JSON only.",
                            },
                            {
                                "type": "image",
                                "source": {
                                    "type": "base64",
                                    "media_type": "image/png",
                                    "data": base64_image,
                                },
                            },
                        ],
                    }
                ],
            )

            result = json.loads(response.content[0].text)

        return result


if __name__ == "__main__":
    detector = MessageDetector(provider="openai")

    test_screenshot = Path(__file__).parent / "data" / "screenshots"
    screenshots = list(test_screenshot.glob("*.png"))

    if screenshots:
        latest = max(screenshots, key=lambda p: p.stat().st_mtime)
        print(f"Analyzing: {latest}")
        result = detector.analyze_screenshot(latest)
        print(json.dumps(result, indent=2))
