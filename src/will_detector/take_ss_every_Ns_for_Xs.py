#!/usr/bin/env python3
"""
Quick screenshot taker for testing Will detection across different applications.
Usage: python take_ss_every_Ns_for_Xs.py <interval_seconds> <total_duration_seconds>
Example: python take_ss_every_Ns_for_Xs.py 1 30
"""

import asyncio
import sys
import time
from datetime import datetime
from pathlib import Path

from classifier import MessageDetector
from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.table import Table
from run_config import CFG
from screenshot import capture_active_window_screenshot
from storage import SightingStorage

console = Console()


class ScreenshotTester:
    def __init__(self, interval: int, duration: int):
        self.interval = interval
        self.duration = duration
        self.detector = MessageDetector(provider=CFG.LLM_PROVIDER)
        self.storage = SightingStorage(CFG.SIGHTINGS_FILE)
        self.results = []
        self.start_time = None

    async def take_screenshot_and_analyze(self, iteration: int) -> dict:
        """Take screenshot and analyze for Will sightings."""
        try:
            # Take screenshot
            console.print(f"[cyan]SS #{iteration}:[/cyan] Taking screenshot...")
            screenshot_info = capture_active_window_screenshot(CFG.SCREENSHOT_DIR)
            console.print(
                f"[cyan]SS #{iteration}:[/cyan] ✓ Screenshot saved - App: [blue]{screenshot_info['app_name']}[/blue], Window: [green]{screenshot_info['window_title']}[/green]"
            )

            # Analyze with LLM
            console.print(f"[cyan]SS #{iteration}:[/cyan] 🤖 Analyzing with LLM...")
            detection_result = self.detector.analyze_screenshot(screenshot_info["path"])

            will_status = (
                "✅ WILL DETECTED!"
                if detection_result.get("willSighting")
                else "❌ No Will"
            )
            console.print(f"[cyan]SS #{iteration}:[/cyan] {will_status}")
            console.print(
                f"[cyan]SS #{iteration}:[/cyan] 💭 Reasoning: {detection_result.get('reasoning', '')[:100]}..."
            )

            if detection_result.get("willSighting"):
                console.print(
                    f"[cyan]SS #{iteration}:[/cyan] 💬 Quote: [yellow]{detection_result.get('verbatimQuote', '')}[/yellow]"
                )

            # Check if it's a new sighting
            is_new_sighting = False
            if detection_result.get("willSighting"):
                is_new_sighting = self.storage.add_sighting(
                    screenshot_info, detection_result
                )
                sighting_status = (
                    "🆕 NEW SIGHTING" if is_new_sighting else "🔄 DUPLICATE"
                )
                console.print(f"[cyan]SS #{iteration}:[/cyan] {sighting_status}")

            console.print(f"[cyan]SS #{iteration}:[/cyan] [dim]Completed[/dim]")
            console.print("[dim]" + "─" * 50 + "[/dim]")

            result = {
                "iteration": iteration,
                "timestamp": datetime.now().isoformat(),
                "app_name": screenshot_info["app_name"],
                "window_title": screenshot_info["window_title"],
                "screenshot_path": screenshot_info["path"],
                "will_detected": detection_result.get("willSighting", False),
                "is_new_sighting": is_new_sighting,
                "reasoning": detection_result.get("reasoning", ""),
                "verbatim_quote": detection_result.get("verbatimQuote", ""),
                "file_size_mb": round(
                    Path(screenshot_info["path"]).stat().st_size / 1024 / 1024, 2
                ),
            }

            self.results.append(result)
            return result

        except Exception as e:
            console.print(f"[red]SS #{iteration}: ❌ ERROR: {e}[/red]")
            console.print("[dim]" + "─" * 50 + "[/dim]")
            return {
                "iteration": iteration,
                "timestamp": datetime.now().isoformat(),
                "app_name": "ERROR",
                "window_title": "ERROR",
                "screenshot_path": "ERROR",
                "will_detected": False,
                "is_new_sighting": False,
                "reasoning": f"Error: {e}",
                "verbatim_quote": "",
                "file_size_mb": 0,
            }

    def create_results_table(self) -> Table:
        """Create a rich table for displaying results."""
        table = Table(
            title=f"Will Detection Results - {len(self.results)} screenshots taken"
        )
        table.add_column("Iter", style="cyan", no_wrap=True)
        table.add_column("App", style="blue")
        table.add_column("Window", style="green", width=30)
        table.add_column("Will Detected", style="bold red")
        table.add_column("New Sighting", style="bold green")
        table.add_column("File Size (MB)", style="yellow")
        table.add_column("Quote", style="white", width=40)

        for result in self.results:
            will_status = "✅ YES" if result["will_detected"] else "❌ NO"
            new_status = "🆕 NEW" if result["is_new_sighting"] else "🔄 DUPE"

            # Truncate long window titles
            window_title = (
                result["window_title"][:27] + "..."
                if len(result["window_title"]) > 30
                else result["window_title"]
            )

            # Truncate quotes
            verbatim_quote = result["verbatim_quote"] or ""
            quote = (
                verbatim_quote[:37] + "..."
                if len(verbatim_quote) > 40
                else verbatim_quote
            )

            table.add_row(
                str(result["iteration"]),
                result["app_name"],
                window_title,
                will_status,
                new_status,
                str(result["file_size_mb"]),
                quote,
            )

        return table

    def create_summary_panel(self) -> Panel:
        """Create a summary panel."""
        total_screenshots = len(self.results)
        will_detections = sum(1 for r in self.results if r["will_detected"])
        new_sightings = sum(1 for r in self.results if r["is_new_sighting"])
        unique_apps = len(
            set(r["app_name"] for r in self.results if r["app_name"] != "ERROR")
        )

        will_percentage = (
            (will_detections / total_screenshots * 100) if total_screenshots > 0 else 0
        )
        summary_text = f"""
📊 SUMMARY:
• Total Screenshots: {total_screenshots}
• Will Detections: {will_detections} ({will_percentage:.1f}%)
• New Sightings: {new_sightings}
• Unique Apps Tested: {unique_apps}
• Duration: {self.duration}s
• Interval: {self.interval}s
        """

        return Panel(summary_text, title="Test Summary", border_style="blue")

    async def run(self):
        """Run the screenshot testing loop."""
        self.start_time = time.time()
        console.print("[bold blue]Starting Will Detection Test[/bold blue]")
        console.print(
            f"📸 Taking screenshots every {self.interval}s for {self.duration}s"
        )
        console.print(f"🤖 Using LLM Provider: {CFG.LLM_PROVIDER}")
        console.print(f"💾 Screenshots saved to: {CFG.SCREENSHOT_DIR}")
        console.print("-" * 60)

        iteration = 1
        with Live(self.create_results_table(), refresh_per_second=1) as live:
            while True:
                # Check if we've exceeded the duration
                elapsed_time = time.time() - self.start_time
                if elapsed_time >= self.duration:
                    break

                # Take screenshot and analyze
                result = await self.take_screenshot_and_analyze(iteration)

                # Update the live display
                live.update(self.create_results_table())

                # Wait for next iteration (but don't exceed duration)
                remaining_time = self.duration - elapsed_time
                if remaining_time > self.interval:
                    await asyncio.sleep(self.interval)
                else:
                    # Don't sleep if we're close to the end
                    break

                iteration += 1

        # Final summary
        console.print("\n" + "=" * 60)
        console.print(self.create_summary_panel())
        console.print("\n")
        console.print(self.create_results_table())

        # Save detailed results to file
        results_file = (
            CFG.SCREENSHOT_DIR.parent
            / f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        import json

        with open(results_file, "w") as f:
            json.dump(self.results, f, indent=2)
        console.print(f"\n📄 Detailed results saved to: {results_file}")


def main():
    """Take screenshots every N seconds for Y seconds and analyze for Will sightings."""
    if len(sys.argv) != 3:
        console.print(
            "[red]Usage: python take_ss_every_Ns_for_Xs.py <interval_seconds> <total_duration_seconds>[/red]"
        )
        console.print("Example: python take_ss_every_Ns_for_Xs.py 1 30")
        sys.exit(1)

    try:
        interval = int(sys.argv[1])
        duration = int(sys.argv[2])
    except ValueError:
        console.print("[red]Error: interval and duration must be integers[/red]")
        sys.exit(1)

    tester = ScreenshotTester(interval, duration)
    asyncio.run(tester.run())


if __name__ == "__main__":
    main()
