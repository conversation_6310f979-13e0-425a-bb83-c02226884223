from datetime import datetime
from pathlib import Path
from typing import Dict, List

import gspread
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow


class SheetsManager:
    """Manages Google Sheets operations for screenshot data."""

    SCOPES = [
        "https://www.googleapis.com/auth/spreadsheets",
        "https://www.googleapis.com/auth/drive",
    ]

    def __init__(
        self,
        credentials_path: str = "creds_personal.json",
        token_path: str = "token.json",
    ):
        self.credentials_path = Path(credentials_path)
        self.token_path = Path(token_path)
        self.gc = None
        self._authenticate()

    def _authenticate(self):
        """Authenticate with Google Sheets API using OAuth."""
        creds = None

        # Load existing token if available
        if self.token_path.exists():
            creds = Credentials.from_authorized_user_file(
                str(self.token_path), self.SCOPES
            )

        # If no valid credentials available, let user log in
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                flow = InstalledAppFlow.from_client_secrets_file(
                    str(self.credentials_path), self.SCOPES
                )
                creds = flow.run_local_server(port=0)

            # Save credentials for next run
            with open(self.token_path, "w") as token:
                token.write(creds.to_json())

        self.gc = gspread.authorize(creds)

    def open_or_create_sheet(self, sheet_title: str) -> gspread.Spreadsheet:
        """Open existing sheet or create new one."""
        try:
            return self.gc.open(sheet_title)
        except gspread.SpreadsheetNotFound:
            return self.gc.create(sheet_title)

    def log_screenshot_data(self, sheet_url: str, screenshot_data: Dict) -> bool:
        """Log screenshot data to Google Sheet."""
        try:
            # Extract sheet key from URL
            sheet_key = self._extract_sheet_key(sheet_url)
            sheet = self.gc.open_by_key(sheet_key)
            worksheet = sheet.sheet1

            # Prepare data row
            row_data = [
                screenshot_data.get("timestamp", ""),
                screenshot_data.get("app_name", ""),
                screenshot_data.get("window_title", ""),
                screenshot_data.get("path", ""),
                datetime.now().isoformat(),  # Log timestamp
            ]

            # Check if headers exist, add if not
            if not worksheet.get_all_values():
                headers = [
                    "Timestamp",
                    "App Name",
                    "Window Title",
                    "Screenshot Path",
                    "Log Time",
                ]
                worksheet.update([headers])
                worksheet.update([row_data], "A2")
            else:
                # Append to existing data
                worksheet.append_row(row_data)

            return True

        except Exception as e:
            print(f"Error logging to Google Sheets: {e}")
            return False

    def _extract_sheet_key(self, sheet_url: str) -> str:
        """Extract sheet key from Google Sheets URL."""
        # Handle different URL formats
        if "/spreadsheets/d/" in sheet_url:
            # New format: https://docs.google.com/spreadsheets/d/{key}/edit
            key = sheet_url.split("/spreadsheets/d/")[1].split("/")[0]
        elif "key=" in sheet_url:
            # Old format: https://docs.google.com/spreadsheet/ccc?key={key}
            key = sheet_url.split("key=")[1].split("&")[0]
        else:
            raise ValueError("Invalid Google Sheets URL format")

        return key

    def get_sheet_data(self, sheet_url: str) -> List[List]:
        """Get all data from sheet."""
        try:
            sheet_key = self._extract_sheet_key(sheet_url)
            sheet = self.gc.open_by_key(sheet_key)
            worksheet = sheet.sheet1
            return worksheet.get_all_values()
        except Exception as e:
            print(f"Error getting sheet data: {e}")
            return []


def log_screenshot_to_sheets(screenshot_data: Dict, sheet_url: str) -> bool:
    """Convenience function to log screenshot data to Google Sheets."""
    sheets_manager = SheetsManager()
    return sheets_manager.log_screenshot_data(sheet_url, screenshot_data)
