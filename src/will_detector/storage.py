import json
import hashlib
from pathlib import Path
from datetime import datetime


class SightingStorage:
    def __init__(self, storage_path: Path):
        self.storage_path = Path(storage_path)
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        self._load_sightings()
    
    def _load_sightings(self):
        if self.storage_path.exists():
            with open(self.storage_path, 'r') as f:
                self.sightings = json.load(f)
        else:
            self.sightings = []
    
    def _save_sightings(self):
        with open(self.storage_path, 'w') as f:
            json.dump(self.sightings, f, indent=2)
    
    def _generate_message_hash(self, message_text):
        if not message_text:
            return None
        return hashlib.sha256(message_text.encode()).hexdigest()[:16]
    
    def is_duplicate(self, verbatim_quote):
        if not verbatim_quote:
            return False
        
        message_hash = self._generate_message_hash(verbatim_quote)
        
        for sighting in self.sightings:
            if sighting.get('message_hash') == message_hash:
                return True
        
        return False
    
    def add_sighting(self, screenshot_info, detection_result):
        if not detection_result.get('willSighting'):
            return False
        
        verbatim_quote = detection_result.get('verbatimQuote')
        
        if self.is_duplicate(verbatim_quote):
            return False
        
        sighting = {
            'timestamp': datetime.now().isoformat(),
            'screenshot_path': screenshot_info['path'],
            'app_name': screenshot_info['app_name'],
            'window_title': screenshot_info['window_title'],
            'reasoning': detection_result['reasoning'],
            'verbatim_quote': verbatim_quote,
            'message_hash': self._generate_message_hash(verbatim_quote)
        }
        
        self.sightings.append(sighting)
        self._save_sightings()
        
        return True
    
    def get_recent_sightings(self, limit=10):
        return self.sightings[-limit:] if self.sightings else []


if __name__ == "__main__":
    storage = SightingStorage(Path(__file__).parent / "data" / "sightings.json")
    
    test_result = {
        'reasoning': 'Test detection',
        'willSighting': True,
        'verbatimQuote': 'Hey, this is Will!'
    }
    
    test_screenshot = {
        'path': '/test/path.png',
        'timestamp': '20250117_120000',
        'app_name': 'Messages',
        'window_title': 'Chat'
    }
    
    added = storage.add_sighting(test_screenshot, test_result)
    print(f"Sighting added: {added}")
    
    duplicate = storage.add_sighting(test_screenshot, test_result)
    print(f"Duplicate check (should be False): {duplicate}")