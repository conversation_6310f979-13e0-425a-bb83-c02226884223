# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

**Feisty5**: Productivity monitoring system that helps developers maintain focus on their top task through automated distraction detection and gentle voice alerts.

## Project Goal

Automated productivity monitoring system that captures screenshots every 5 seconds, uses LLM vision analysis (GPT-5) to detect if the user is focused on their current top task or distracted, and provides gentle voice nudges to maintain focus flow.

## Architecture

### Core Components (src/basic_checker_tasklist/)
1. **Screenshot Monitoring**: 
   - Captures active window screenshots every 5 seconds
   - Detects screen content changes via perceptual hashing
   - Saves to `data/screenshots/YYYYMMDD_HHMMSS.png`

2. **Distraction Detection**:
   - Input: Screenshot path + current top task context from Things3
   - LLM Analysis: GPT-5 vision analysis to assess if user is working on their top task
   - Output: JSON with reasoning and focus state relative to top task
   - Hash optimization: Skip analysis when screen unchanged (30-40% cost savings)

3. **Voice Alert System**:
   - Gentle voice reminders when distraction detected
   - Dual backend: macOS 'say' and OpenAI TTS
   - Supportive, encouraging messaging

4. **Things3 Integration**:
   - Fetches current top task to define what "focused" means
   - Provides task-specific context for distraction detection
   - Enables personalized focus assessment based on actual priorities

## Project Structure

```
src/basic_checker_tasklist/     # Main productivity monitoring system
├── data/
│   ├── screenshots/           # YYYYMMDD_HHMMSS.png files
│   ├── compressed/           # Optimized images for LLM analysis
│   └── logs/                 # Session monitoring data
├── main.py                   # Entry point
├── detector.py              # LLM-based distraction analysis
├── monitor.py               # Screenshot orchestration
├── speaker.py               # Voice alert system
└── hash_change_detection.py # Performance optimization
```

## Development Conventions

- Virtual environment: `.venv` (not `venv`)
- Configuration: `.env` for API keys, accessed via python-dotenv
- Global config: CFG singleton in run_config.py
- Build system: setuptools with pyproject.toml
- Package manager: pip with requirements.txt and uv.lock
- Task runner: justfile for common operations
- Spec management: `.kiro/specs/current/` and `.kiro/specs/history/`

## Key Requirements

- macOS-specific screenshot capabilities
- LLM integration for vision analysis (GPT-5 primary, Claude fallback)
- Hash-based change detection for cost optimization
- Voice alert system with dual backend support
- Things3 task management integration
- Image compression for performance optimization
- Python 3.12<x<3.13
- @inv is shorthand for @invisible.email, my organizations email domain

## Main Commands

- `just run` - Start Feisty5 productivity monitoring
- `just setup` - Initialize virtual environment and dependencies
- `just voice-toggle` - Switch between macOS say and OpenAI TTS
- `just clean-feisty-screenshots` - Clean monitoring screenshots
- `just voice-test` - Test current voice backend

## Current Specs

- **Active Spec**: `.kiro/specs/current/feisty5-productivity-monitor/`
- **Archived Specs**: `.kiro/specs/history/` (includes deprecated Will Detector)