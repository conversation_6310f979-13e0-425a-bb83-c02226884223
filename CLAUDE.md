# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

**Will Detector**: Automated screenshot-based message detection system that monitors for messages from specific contacts using LLM analysis.

## Project Goal

Simple script that takes regular screenshots and uses GPT-5 or <PERSON> to detect whether a message from <PERSON> has been received. The system logs detections and avoids duplication.

## Architecture

### Core Components (src/1_will_detector/)
1. **Screenshot Capture**: 
   - Detect active screen/window on macOS
   - Capture screenshot of ONLY the active application
   - Save to `data/screenshots/YYYYMMDD_HHMMSS.png`

2. **Classification System**:
   - Input: Screenshot path
   - Output: JSON with:
     - Reasoning for the detection
     - 'willSighting' boolean (true/false)
     - Verbatim quote of spotted message (verified)

3. **Data Storage**:
   - Sightings saved to `data/sightings.json`
   - Timestamp-based screenshot archival

## Project Structure

```
src/1_will_detector/
├── data/
│   ├── screenshots/      # YYYYMMDD_HHMMSS.png files
│   └── sightings.json    # Detection log
├── __init__.py
└── [main application files]
```

## Development Conventions

- Virtual environment: `.venv` (not `venv`)
- Configuration: `.env` for API keys, accessed via python-dotenv
- Global config: CFG singleton in run.py or run_config.py
- No defaults, fallbacks, or exception handling during development
- Scripts: `run_*.sh` naming convention
- Task tracking: `zTasks.md` in root (gitignored)

## Key Requirements

- macOS-specific screenshot capabilities
- LLM integration for image analysis (GPT-5/Claude)
- Deduplication logic to prevent repeated detections
- JSON structured output for tracking
- use Python 3.12<x<3.13
- @inv is shorthand for @invisible.email, my organizations email domain. E.g., Steve.Lloyd@inv = <EMAIL>