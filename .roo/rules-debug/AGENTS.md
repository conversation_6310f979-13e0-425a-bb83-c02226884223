# Debug Mode Rules (Non-Obvious Only)

## Hidden Debug Patterns
- Screenshot paths in `data/screenshots/` include compressed versions in `compressed/` subdirectory
- Distraction detector reasoning provides verbose analysis before boolean decision
- Hash comparison threshold is hardcoded to 10 (not configurable)

## Silent Failures
- Manual .env parsing fails silently if malformed (see detector.py:16-18)
- Missing OPENAI_API_KEY or ANTHROPIC_API_KEY causes runtime errors, not startup
- IntervalTimer continues even if processing exceeds interval time

## Non-Standard Logging
- No logging framework used - all debug output via print statements
- Sightings stored in JSON file, not database (src/will_detector/data/sightings.json)