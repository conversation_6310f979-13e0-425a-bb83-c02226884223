# Code Mode Rules (Non-Obvious Only)

## API Model Constraints
- MUST use `gpt-5-nano` for distraction detection (hardcoded in detector.py:9)
- Can override with `SS_MAIN_MODEL` env var but DO NOT change the default
- Voice alerts require rate=173 (not the standard 144)

## Required Patterns
- Screenshot operations MUST use `compress=True` parameter
- Hash-based change detection is mandatory before API calls
- Manual .env parsing required in some modules (avoid python-dotenv import)

## Module Execution
- Components run as modules: `python -m src.module_name`
- CFG singleton must be accessed from run_config.py