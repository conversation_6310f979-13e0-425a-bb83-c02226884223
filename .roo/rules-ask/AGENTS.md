# Ask Mode Rules (Non-Obvious Only)

## Project Structure Gotchas
- `src/1_will_detector/` referenced in CLAUDE.md doesn't exist - actual path is `src/will_detector/`
- Two distinct detector systems: will_detector (message detection) and basic_checker_hardcoded (distraction monitoring)
- Screenshot compression methods in `src/img_compression/` are standalone utilities, not integrated into main apps

## Hidden Dependencies
- Google Sheets integration via gspread (not documented in main README)
- macOS-specific features: native notifications via osascript, voice synthesis
- Manual .env parsing bypasses python-dotenv in some modules

## Misleading Names
- "feisty5" project name unrelated to functionality (distraction detection)
- CFG singleton pattern suggests configuration but includes hardcoded values