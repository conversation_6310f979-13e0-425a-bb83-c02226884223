# Architect Mode Rules (Non-Obvious Only)

## Architecture Constraints
- Two independent monitoring systems that don't share code:
  - will_detector: <PERSON> message detection
  - basic_checker_hardcoded: Distraction monitoring for "coding feisty" work
- CFG singleton must be imported from run_config.py, not instantiated elsewhere
- No shared utilities between modules despite similar functionality

## Non-Standard Design Decisions
- Print statements for all output (no logging framework)
- JSON file storage instead of database (sightings.json)
- Hardcoded model names prevent easy switching between LLM providers
- Manual .env parsing duplicated across modules instead of centralized