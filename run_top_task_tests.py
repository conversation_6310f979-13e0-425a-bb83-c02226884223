#!/usr/bin/env python3
"""Run all top task checker tests"""

import sys
import subprocess
from pathlib import Path

def run_tests():
    """Run all test modules for top task checker"""
    print("🧪 Running Top Task Checker Tests")
    print("="*50)
    
    test_modules = [
        "tests/top_task_checker/test_config.py",
        "tests/top_task_checker/test_task_fetcher.py", 
        "tests/top_task_checker/test_integration.py"
    ]
    
    total_passed = 0
    total_failed = 0
    
    for test_module in test_modules:
        if not Path(test_module).exists():
            print(f"⚠️  Test module not found: {test_module}")
            continue
            
        print(f"\n📋 Running {test_module}:")
        print("-" * 30)
        
        try:
            result = subprocess.run(
                [sys.executable, test_module],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print(f"✅ {test_module} - PASSED")
                total_passed += 1
                
                # Show successful test output
                if result.stdout:
                    lines = result.stdout.split('\n')
                    success_lines = [line for line in lines if '✓' in line or 'OK' in line]
                    for line in success_lines[-3:]:  # Show last 3 success messages
                        print(f"   {line}")
            else:
                print(f"❌ {test_module} - FAILED")
                total_failed += 1
                
                # Show error output
                if result.stderr:
                    print("Error output:")
                    print(result.stderr[:500])
                if result.stdout:
                    print("Standard output:")
                    print(result.stdout[:500])
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_module} - TIMEOUT")
            total_failed += 1
        except Exception as e:
            print(f"💥 {test_module} - ERROR: {e}")
            total_failed += 1
    
    print(f"\n📊 Test Summary:")
    print(f"✅ Passed: {total_passed}")
    print(f"❌ Failed: {total_failed}")
    print(f"📈 Success rate: {total_passed/(total_passed+total_failed)*100:.1f}%" if (total_passed+total_failed) > 0 else "No tests run")
    
    if total_failed == 0:
        print("\n🎉 All tests passed! System is ready.")
        return True
    else:
        print(f"\n⚠️  {total_failed} test(s) failed. Check output above.")
        return False

def test_components_individually():
    """Test individual components"""
    print("\n🔧 Testing Individual Components:")
    print("="*40)
    
    # Test config
    try:
        sys.path.insert(0, 'src/top_task_checker')
        from config import TaskFocusConfig, CONFIG
        
        config = TaskFocusConfig()
        print(f"✅ Config system: {config.TASK_REFRESH_INTERVAL}s refresh interval")
        
        focus_mode = TaskFocusConfig.create_focus_mode()
        print(f"✅ Focus mode: {focus_mode.SCREENSHOT_INTERVAL}s screenshot interval")
        
    except Exception as e:
        print(f"❌ Config system failed: {e}")
    
    # Test task fetcher (with mock)
    try:
        from unittest.mock import patch, MagicMock
        from task_fetcher import TaskFetcher
        
        with patch('subprocess.run') as mock_run:
            mock_result = MagicMock()
            mock_result.returncode = 0
            mock_result.stdout = "test task\n"
            mock_run.return_value = mock_result
            
            fetcher = TaskFetcher(refresh_interval=30)
            task = fetcher.get_current_task()
            
            if task == "test task":
                print("✅ Task fetcher: Mock integration successful")
            else:
                print(f"❌ Task fetcher: Expected 'test task', got '{task}'")
                
    except Exception as e:
        print(f"❌ Task fetcher failed: {e}")

if __name__ == "__main__":
    success = run_tests()
    test_components_individually()
    
    if success:
        print("\n🎯 Top Task Checker System Status: READY")
    else:
        print("\n⚠️  Top Task Checker System Status: NEEDS FIXES")
    
    sys.exit(0 if success else 1)