#!/usr/bin/env python3
"""Test voice configuration and validate everything is working."""

import subprocess
import sys
from pathlib import Path


def test_env_settings():
    """Check .env has all voice settings."""
    env_path = Path(".env")
    if not env_path.exists():
        print("❌ .env file not found")
        return False

    required_keys = [
        "VOICE_BACKEND",
        "OPENAI_TTS_MODEL",
        "OPENAI_TTS_VOICE",
        "OPENAI_TTS_SPEED",
        "OPENAI_TTS_VOLUME",
        "OPENAI_TTS_FALLBACK",
    ]

    env_vars = {}
    with open(env_path) as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith("#") and "=" in line:
                key, value = line.split("=", 1)
                env_vars[key] = value

    print("🔍 Checking .env voice settings...")
    all_present = True
    for key in required_keys:
        if key in env_vars:
            print(f"  ✅ {key} = {env_vars[key]}")
        else:
            print(f"  ❌ {key} is missing")
            all_present = False

    return all_present


def test_voice_speak():
    """Test voice_speak.sh script."""
    script_path = Path("src/basic_checker_hardcoded/voice/voice_speak.sh")
    if not script_path.exists():
        print("❌ voice_speak.sh not found")
        return False

    print("\n🔊 Testing voice output...")
    result = subprocess.run(
        ["./src/basic_checker_hardcoded/voice/voice_speak.sh", "Testing voice system"],
        capture_output=True,
        text=True,
    )

    if result.returncode == 0:
        print("  ✅ Voice speak successful")
        if result.stdout:
            print(f"  Output: {result.stdout.strip()}")
        return True
    else:
        print(f"  ❌ Voice speak failed: {result.stderr}")
        return False


def test_toggle_script():
    """Test toggle_voice.py functionality."""
    print("\n🔄 Testing toggle script...")

    # Test status command
    result = subprocess.run(
        ["python3", "src/basic_checker_hardcoded/voice/toggle_voice.py", "status"],
        capture_output=True,
        text=True,
    )

    if result.stdout:
        print("  ✅ Status command works")
        print(f"  Output preview: {result.stdout[:100]}...")
    else:
        print("  ⚠️ Status command produced no output")
        if result.stderr:
            print(f"  Error: {result.stderr}")

    return result.returncode == 0


def test_just_commands():
    """Test just commands."""
    print("\n🛠️ Testing just commands...")

    commands = ["voice-status", "voice-test"]
    for cmd in commands:
        result = subprocess.run(
            ["just", cmd], capture_output=True, text=True, timeout=5
        )

        if result.returncode == 0:
            print(f"  ✅ 'just {cmd}' works")
        else:
            print(f"  ❌ 'just {cmd}' failed")

    return True


def main():
    print("=" * 60)
    print("VOICE SYSTEM VALIDATION")
    print("=" * 60)

    tests = [
        ("Environment Settings", test_env_settings),
        ("Voice Speak Script", test_voice_speak),
        ("Toggle Script", test_toggle_script),
        ("Just Commands", test_just_commands),
    ]

    results = []
    for name, test_func in tests:
        try:
            results.append(test_func())
        except Exception as e:
            print(f"❌ {name} failed with error: {e}")
            results.append(False)

    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)

    if all(results):
        print("✅ All tests passed!")
    else:
        print("⚠️ Some tests failed - review output above")

    print("\n📋 Voice Configuration:")
    print("  • Backend: OpenAI TTS (nova voice)")
    print("  • Volume: 2.0x (maximum)")
    print("  • Speed: 1.2x")
    print("  • All settings in .env")
    print("  • Scripts organized in src/basic_checker_hardcoded/voice/")

    return all(results)


if __name__ == "__main__":
    sys.exit(0 if main() else 1)





