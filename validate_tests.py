#!/usr/bin/env python3
"""Validate that all tests work correctly"""

import sys
import os
import json
from unittest.mock import patch

# Add module path
sys.path.insert(0, os.path.join('src', 'tasklist'))

def validate_tests():
    print("🧪 Test Validation")
    print("="*40)
    
    try:
        from things3_import import main
        print("✅ Module import successful")
    except Exception as e:
        print(f"❌ Module import failed: {e}")
        return False
    
    tests_passed = 0
    tests_total = 6
    
    # Test 1: Default behavior (3 tasks)
    try:
        mock_output = "id1\nid2\nid3\nid4\n---\ntask1\ntask2\ntask3\ntask4"
        with patch('subprocess.check_output', return_value=mock_output.encode()):
            with patch('sys.argv', ['test']):
                with patch('builtins.print') as mock_print:
                    main()
                    
                    if mock_print.called:
                        output = json.loads(mock_print.call_args[0][0])
                        if len(output) == 3:
                            print("✅ Test 1: Default 3 tasks")
                            tests_passed += 1
                        else:
                            print(f"❌ Test 1: Expected 3 tasks, got {len(output)}")
                    else:
                        print("❌ Test 1: Print not called")
    except Exception as e:
        print(f"❌ Test 1: {e}")
    
    # Test 2: Custom n parameter
    try:
        mock_output = "id1\nid2\n---\ntask1\ntask2"
        with patch('subprocess.check_output', return_value=mock_output.encode()):
            with patch('sys.argv', ['test', '--n', '2']):
                with patch('builtins.print') as mock_print:
                    main()
                    
                    if mock_print.called:
                        args = mock_print.call_args_list
                        json_output = None
                        for call in args:
                            try:
                                json_output = json.loads(call[0][0])
                                break
                            except:
                                continue
                        
                        if json_output and len(json_output) == 2:
                            print("✅ Test 2: Custom n=2")
                            tests_passed += 1
                        else:
                            print(f"❌ Test 2: Expected 2 tasks")
                    else:
                        print("❌ Test 2: Print not called")
    except Exception as e:
        print(f"❌ Test 2: {e}")
    
    # Test 3: Top task only
    try:
        with patch('subprocess.check_output', return_value="First Task\n".encode()):
            with patch('sys.argv', ['test', '--top-task-only']):
                with patch('builtins.print') as mock_print:
                    main()
                    
                    if mock_print.called:
                        # Find the actual task output (not timing)
                        task_output = None
                        for call in mock_print.call_args_list:
                            call_output = call[0][0]
                            if not call_output.startswith("⏱️"):
                                task_output = call_output
                                break
                        
                        if task_output == "First Task":
                            print("✅ Test 3: Top task only")
                            tests_passed += 1
                        else:
                            print(f"❌ Test 3: Expected 'First Task', got '{task_output}'")
                    else:
                        print("❌ Test 3: Print not called")
    except Exception as e:
        print(f"❌ Test 3: {e}")
    
    # Test 4: Top task overrides n
    try:
        with patch('subprocess.check_output', return_value="Override Test\n".encode()):
            with patch('sys.argv', ['test', '--n', '5', '--top-task-only']):
                with patch('builtins.print') as mock_print:
                    main()
                    
                    if mock_print.called:
                        task_output = None
                        for call in mock_print.call_args_list:
                            call_output = call[0][0]
                            if not call_output.startswith("⏱️"):
                                task_output = call_output
                                break
                        
                        if task_output == "Override Test":
                            print("✅ Test 4: Top task overrides n")
                            tests_passed += 1
                        else:
                            print(f"❌ Test 4: Expected 'Override Test', got '{task_output}'")
                    else:
                        print("❌ Test 4: Print not called")
    except Exception as e:
        print(f"❌ Test 4: {e}")
    
    # Test 5: JSON structure
    try:
        mock_output = "id123\n---\ntask title"
        with patch('subprocess.check_output', return_value=mock_output.encode()):
            with patch('sys.argv', ['test']):
                with patch('builtins.print') as mock_print:
                    main()
                    
                    if mock_print.called:
                        json_output = None
                        for call in mock_print.call_args_list:
                            try:
                                json_output = json.loads(call[0][0])
                                break
                            except:
                                continue
                        
                        if json_output and len(json_output) == 1 and 'id' in json_output[0] and 'title' in json_output[0]:
                            print("✅ Test 5: JSON structure")
                            tests_passed += 1
                        else:
                            print("❌ Test 5: Invalid JSON structure")
                    else:
                        print("❌ Test 5: Print not called")
    except Exception as e:
        print(f"❌ Test 5: {e}")
    
    # Test 6: Pretty printing
    try:
        mock_output = "id1\n---\ntask1"
        with patch('subprocess.check_output', return_value=mock_output.encode()):
            with patch('sys.argv', ['test']):
                with patch('builtins.print') as mock_print:
                    main()
                    
                    if mock_print.called:
                        json_output = None
                        for call in mock_print.call_args_list:
                            call_output = call[0][0]
                            if '\n' in call_output and '  ' in call_output and not call_output.startswith("⏱️"):
                                json_output = call_output
                                break
                        
                        if json_output:
                            print("✅ Test 6: Pretty printing")
                            tests_passed += 1
                        else:
                            print("❌ Test 6: Not pretty printed")
                    else:
                        print("❌ Test 6: Print not called")
    except Exception as e:
        print(f"❌ Test 6: {e}")
    
    print(f"\n📊 Test Results: {tests_passed}/{tests_total} passed")
    
    if tests_passed == tests_total:
        print("🎉 All tests PASSING!")
        return True
    else:
        print(f"❌ {tests_total - tests_passed} tests failed")
        return False

if __name__ == "__main__":
    success = validate_tests()
    sys.exit(0 if success else 1)