[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "feisty5"
version = "0.1.0"
description = "Coding feisty checker with distraction detection and monitoring"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "openai==1.59.9",
    "anthropic==0.42.0",
    "python-dotenv==1.0.1",
    "typer==0.12.4",
    "rich==14.1.0",
    "gspread==6.0.2",
    "google-auth-oauthlib==1.2.0",
    "imagehash==4.3.2",
    "pillow==11.3.0",
    "opencv-python==*********",
    "pytest==8.4.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.2",
    "pytest-cov",
    "black",
    "flake8",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"












