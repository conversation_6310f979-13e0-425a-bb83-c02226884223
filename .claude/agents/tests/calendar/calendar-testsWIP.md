# Calendar Testing Agent

## Test Cases
// Format is 
// ```Instruction Input  
// ->  what to check to verify success.```

```
Add this to my calendar as a maybe https://lu.ma/dd0wh6us  
->  read my professional calendar via bash command. Is it there?
```

```
    Add this to my calendar 💥✨🍃 Wait.. what? It’s.. another party.. IN THE FRICKIN WOODS 🍃✨💥

    🗓️ Saturday 20 Sep 🌚 til 🌞+++
    🌳 Back up to NE London
    🚆 24 hour tube line
    🎚️ Sylvanian futurism, free party platinum classixxx, tough music for squishy people
    👁️ Vibeology Research Unit delivering the keynote
    🙊 Please only forward to people you know and trust
    🎒 Bring 🥤 WATER 🧥 layers 🍌 snacks 💵 cash 😜 humans who make you happy
    🚯 Leave no very very little trace
    🤍 Be decent, be respectful, be good
    ✊ Rekindle 🕯️ replenish 📈 recharge ⚡
    😶‍🌫️ There are no organisers of this party
    ✈️ Join here for info and updates https://t.me/+E2DZijTCUjY0MzE8

    🔙🔮🛸 C U ON THE FLOOR 🛸🔮🔜

    Include all details in description.
-> verify calendar event in calendar using api/bash. Verify description has the full details from above.
```