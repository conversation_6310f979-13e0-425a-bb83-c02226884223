Testing Agents are essentially quick integration tests but run by claude code.
- they are in-lieu of writing proper integrations tests.
- the point of these is that they are VERY quick to write, and are flexible.

These are the instructions that all testing agents should follow.
- simple declarative testing
- test cases from real situations that came up

requirements
- Agents are more random, so instructions / tests should rely less on the agent 'getting it right'. Closer to run x script, check output, verify real-result (eg calendar entry added)
