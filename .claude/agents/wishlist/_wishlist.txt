
Okay, what would be really cool is if we had a hook at the end of each cloned code interaction, and that hook would indicate whether we are currently on a branch. We'll call it "branch.getBranchGuardian." Its purpose is to solve the problem of remembering to split features cleanly into separate branches when developing solo, and to create sub‑branches when needed. 

Refactor <PERSON><PERSON>'s two scripts 🟠

Antrhopic Agent 'get you started' two options.

Find online someone who is vetting them / a review & comment board.

HOOK: EVERY INTERACTION ENDS / EVERY FEW
- JustFile: Is the core feature we're working on correctly callable in the projects' just-file? Does it work when called? **WILL CALL SCRIPT**
- Tests
- Feature branch
- Check each feature and project/product requirement from [prd / listed core requirements doc]. Work down checklist / rubric.

HOOK: CATCH EVERY TIME IT TRIES TO 'WORK AROUND MISSING API CREDS'. Be aware of tool limitations / human-required step, without it becoming a crutch, just catch them and then have a piece of logic decide classification for what to do.
CleanShot 2025-08-20 at <EMAIL>


HOOK - RUN THIS PROMPT. It's great: "Check against requirements in @CLAUDE_SPEC.md → ✅ and ❌ ". Do not ignore potential typos in specification.


DETECTIVE MODE -> i.e., hypotheses, gather evidence for them, completion via meeting some bar
> My keyboard keeps spontaenously just ignoring keystrokes for 1-2s. Been doing it for weeks. Research most likely causes then Run diagnostics to gather evidence for this theory.

PA - context saver (dynamicly saves new context in Tana hierarchy or md dir or mem0 BUT it's long context, so.) Keep it simple, the agents that route new context and refactor abstraction (REFACTOR MUST BE SAFE. Potentially Dump and build abstractions from raw dump, 'edit' abstraction quickly when adding new context ahead of refactor, log the 'edits')

QUALITY of OUTCOME prompts
- Structured Outputs MetaPrompt (which structure (fields) do I likely want? GIven context and prompt)
- PLANNING Please ultrathink and make a plan for me. Triple check all claims / facts and provide credible citations.
    - Martin Plan + O1 Plan. IDD that planning app from Nov/Dec. Check if still going.
- PLANNING Please make a PRD for this simple Python 3.12 app, Then add nice-to-have features and impact level in Second PRD (Same Canvas plz). 
    So we do the MVP first and extra features second. Make clear which feature is for which user story etc. output in Canvas. 
    Research thoroughly initially to get best options for APIs and document research but don't SPECIFY exactly which tools to use in the main PRD sections, 
    so the agent we get to make it doesn't have fallback options.
    Start Your doc with title then PRD for MVP: [name] then first section: GOAL. Body for goal should start: Goal: Allow a solo developer (Alex) to quickly....[rest from here]
- RESEARCH Please thoroughly research the most credible and easy to   │
│   install on claude code (~/.claude/settings.json) MCP for   │
│   delegating whole tasks to other providers' models,         │
│   primarily gpt-5-pro and gpt deep research, but also e.g.,  │
│   Cerebras' gpt-oss120b. Can be two MCPs, the one that can   │
│   reliably do openai models is top priority. Ultrathink.     │
│   Review reviews pros cons etc from July 2025 onwards        │
│   thoroughly before deciding and weight credible opinions /  │
│   signals heavily (over single negative reviews for          │
│   instance)   

EMAIL ABSTRACTIONS 


CONTEXT ABSTRACTIONS ------------------------------
>> MCP/CONTEXT EVAL. CORTEX/MINI CORTEX. BUILD REQ/PRD (CC), INSTALL IT (CC), need path + client hardcoded/routered, TEST CASES + CC TO INTERPRET. REQUIREMENTS -> EVALS.
- Needs to lookup / source the 'when to use this how'. OR DOES IT?
- ideal world works for most main MCP clients, tests in parallel.
- ideal world works out what's lacking, e.g., is it brittle? Needs extra prompts / resources built in?


SLACK CONTEXT GATHER FROM EMAIL
- Nylas.com to sync email to disk potentially? OR just a time period and retrieve many of them (but SO many). Could potentially chrome extension yourself the SH AI query.
- OR bite the bullet and have email all downloaded and graphed. Funnel all context into email via notifications etc. Nylas might make it easier.

CLICKUP - Can I access the other things ClickUp can query?

N8N -> is there just a pre-made agent that does everything I want?

ZAPIER -> other than gmail... anything else too private? If not, just use Zapier for everything. DockerMCP for the rest.


NOTIFICATIONS - stand alone agent that is part of Feisty, maybe just script, press f1 to open notifications. Press eg cmd ctrl shift F1 to open notifications, 
    screenshot them (that many px of RHS screen), then run extraction.
    - Expander to detect any stacks (see number or stack graphic, exact px detection?) click on all notification groups to expand them, then ss, [should get list of 
    stacks at the beginning] then scroll down until next stack is in shot and repeat. This won't capture below the fold for long expandsions. Oh well? Probs fine. Simplisity. 
    Could scroll one page at a time and ss each time to get to the next stack...
    - OCR for exact extraction.
    - Some notifications are in email. Move as many there as possible. These are far more easily accessible.
    - Chrome extension that when user AFK / on command, will open all notifications, open their hyperlinks in chrome tabs (batches of 10-20), then grabs content via ss or OCR / Llama parse.



COOL DESIRABLE BEHAVIOUR ? CHECK / INBOX
CleanShot 2025-08-20 at <EMAIL>
Doc string had a verbatim quote from documentation (vertically compressed)