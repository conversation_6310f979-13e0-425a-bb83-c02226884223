#!/usr/bin/env python3
"""
Demo script showing fast compression integration with Will Detector
"""
import sys
from pathlib import Path

sys.path.append('src')

from will_detector.screenshot import capture_active_window_screenshot
from img_compression.compress_utils import compress_screenshot_fast


def demo_compression_integration():
    """Demonstrate fast compression integration"""
    print("🚀 Fast Screenshot Compression Demo")
    print("=" * 50)
    
    screenshots_dir = Path('src/will_detector/data/screenshots')
    
    # Test both modes
    print("\n1. Standard screenshot (no compression)")
    result_normal = capture_active_window_screenshot(screenshots_dir, compress=False)
    print(f"   Screenshot: {Path(result_normal['path']).name}")
    print(f"   Size: {Path(result_normal['path']).stat().st_size:,} bytes")
    
    print("\n2. Screenshot with fast compression")
    result_compressed = capture_active_window_screenshot(screenshots_dir, compress=True)
    print(f"   Original: {Path(result_compressed['path']).name}")
    
    if result_compressed['compression_info']:
        info = result_compressed['compression_info']
        print(f"   Compressed: {Path(info['output_path']).name}")
        print(f"   Method: {info['method']} (fastest)")
        print(f"   Speed: {info['compression_time_sec']:.3f}s")
        print(f"   Size reduction: {info['compression_ratio']*100:.1f}%")
        print(f"   MB saved: {info['size_reduction_mb']:.2f} MB")
    
    print("\n✅ Integration successful!")
    print("\nSpeed comparison:")
    print("  • Binarization: 0.127s (fastest, 94% reduction, best OCR)")
    print("  • Bilateral: 0.183s (good edge preservation)")
    print("  • Sharpening: 0.222s (text clarity)")
    print("  • Contrast: 0.276s (visibility enhancement)")
    

if __name__ == "__main__":
    demo_compression_integration()