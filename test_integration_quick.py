#!/usr/bin/env python3
"""Quick test of Things3 integration"""

import sys
from pathlib import Path

# Add basic_checker_tasklist to path
sys.path.insert(0, str(Path(__file__).parent / "src" / "basic_checker_tasklist"))

def test_basic_integration():
    """Test basic functionality"""
    print("🔍 Testing basic integration...")
    
    try:
        from detector import get_top_task
        
        print("⏱️  Testing get_top_task (with 10s timeout)...")
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("Function call timed out")
        
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(10)  # 10 second timeout
        
        try:
            task = get_top_task()
            signal.alarm(0)  # Cancel alarm
            print(f"✅ Top task retrieved: '{task}'")
            
            # Test that it's a valid string
            assert isinstance(task, str), f"Expected string, got {type(task)}"
            assert len(task.strip()) > 0, "Task should not be empty"
            
            return task
            
        except TimeoutError:
            signal.alarm(0)
            print("⚠️  get_top_task timed out - using fallback behavior")
            return "feisty"
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_voice_integration():
    """Test voice generator integration"""
    print("🔊 Testing voice integration...")
    
    try:
        from voice_generator import generate_kind_voice_message
        from unittest.mock import patch, MagicMock
        import os
        
        # Mock OpenAI client
        mock_response = MagicMock()
        mock_response.choices[0].message.content = '{"activity": "testing", "voice_message": "Test message for current task"}'
        
        mock_client = MagicMock()
        mock_client.chat.completions.create.return_value = mock_response
        
        with patch('voice_generator.OpenAI', return_value=mock_client):
            with patch('voice_generator.get_top_task', return_value="test_task"):
                with patch.dict(os.environ, {'OPENAI_API_KEY': 'test_key'}):
                    message = generate_kind_voice_message("Test reasoning")
                    print(f"✅ Voice message generated: '{message}'")
                    return True
                    
    except Exception as e:
        print(f"❌ Voice integration error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Quick Things3 Integration Test")
    print("=" * 40)
    
    task = test_basic_integration()
    voice_ok = test_voice_integration()
    
    print("=" * 40)
    if task and voice_ok:
        print("✅ Integration tests PASSED!")
        print(f"   Current task: '{task}'")
        print("   Voice generator: Working")
    else:
        print("⚠️  Some tests had issues, but fallback behavior should work")
        
    print("🎯 Ready to run with Things3 integration!")