{"hooks": {"PostToolUse": [{"matcher": "Edit:*.py|Write:*.py", "hooks": [{"type": "command", "command": "python3 run_tests.py", "run_in_background": true}]}, {"matcher": "Edit:tests/*.py|Write:tests/*.py", "hooks": [{"type": "command", "command": "python -m pytest tests/test_simple.py tests/test_hello.py -v", "run_in_background": true}]}], "UserPromptSubmit": [{"matcher": "*", "hooks": [{"type": "command", "command": "python3 perf_test.py", "run_in_background": true}]}]}}