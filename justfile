# Will Detector - Minimalistic cheatsheet for running scripts, main app, tests, setup etc.

# Set up virtual environment and install dependencies
setup:
    python3 -m venv .venv
    source .venv/bin/activate && pip install -r requirements.txt

# Run the basic checker with Things3 integration
run:
    cd src/basic_checker_tasklist && python main.py

# Run the detector once (single screenshot)
test-capture:
    source .venv/bin/activate && python -m src.will_detector.screenshot

# Test classifier with latest screenshot
test-classify:
    source .venv/bin/activate && python -m src.will_detector.classifier

# Test storage module
test-storage:
    source .venv/bin/activate && python -m src.will_detector.storage

# View recent sightings
sightings:
    cat src/will_detector/data/sightings.json | python -m json.tool

# Clean up screenshots (keep sightings)
clean-screenshots:
    rm -f src/will_detector/data/screenshots/*.png

# Clean up coding feisty checker screenshots (keep test data and logs)
clean-feisty-screenshots:
    # Remove monitoring screenshots but preserve test data and logs
    find src/basic_checker_hardcoded/data/screenshots -name "*.png" -not -path "*/test_data/*" -not -path "*/dont-delete/*" -delete
    # Remove compressed screenshots but preserve test data
    find src/basic_checker_hardcoded/data/screenshots/compressed -name "*.png" -not -path "*/test_data/*" -not -path "*/dont-delete/*" -delete 2>/dev/null || true
    @echo "Cleaned up monitoring screenshots, preserved test data and logs"

# Voice Backend Commands
# ----------------------

# Toggle between voice backends (macOS ↔ OpenAI TTS)
voice-toggle:
    @python3 src/basic_checker_hardcoded/voice/toggle_voice.py

# Test current voice backend
voice-test:
    @./src/basic_checker_hardcoded/voice/voice_speak.sh "Testing voice backend. You're doing great! Stay focused on coding."

# Show voice configuration status
voice-status:
    @python3 src/basic_checker_hardcoded/voice/toggle_voice.py status

# Set voice to macOS say
voice-macos:
    @python3 src/basic_checker_hardcoded/voice/toggle_voice.py macos

# Set voice to OpenAI TTS
voice-openai:
    @python3 src/basic_checker_hardcoded/voice/toggle_voice.py openai

# Show help
help:
    @just --list