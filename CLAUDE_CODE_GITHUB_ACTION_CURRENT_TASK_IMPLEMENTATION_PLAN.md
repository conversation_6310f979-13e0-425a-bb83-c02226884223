# Implementation Plan for <PERSON>

**Status**: UPDATED
**Generated**: 2025-09-08T23:45:00Z
**Task ID**: GitHub Issue #5
**Target Model**: <PERSON>

## Task Summary

Validate existing image preprocessing algorithms in src/img_compression/, benchmark their performance for Will Detector use cases, select the top 2 performers, and integrate them into the Will Detector workflow. The algorithms to test are: bilateral_filtering.py, contrast_enhancement.py, sharpening.py, binarization.py, and simple_binarization.py. The goal is to improve screenshot analysis accuracy for detecting messages from <PERSON>.

## Implementation Instructions

### Pre-Implementation Checks
1. Verify working directory is: `/Users/<USER>/Code3b/Feisty5`
2. Ensure these files exist:
   - [ ] `src/img_compression/bilateral_filtering.py`
   - [ ] `src/img_compression/contrast_enhancement.py`
   - [ ] `src/img_compression/sharpening.py`
   - [ ] `src/img_compression/binarization.py`
   - [ ] `src/img_compression/simple_binarization.py`
   - [ ] `src/img_compression/data/test_input.png`
   - [ ] `src/will_detector/classifier.py`
3. Activate virtual environment if needed: `source .venv/bin/activate`
4. Install required dependencies: `pip install opencv-python pillow numpy anthropic openai python-dotenv`

### New File Creation

#### Create: `src/img_compression/benchmark_algorithms.py`
**Purpose**: Comprehensive benchmarking system for image preprocessing algorithms
**Content Structure**:
```python
"""
Benchmark system for image preprocessing algorithms in Will Detector workflow.
Tests performance, quality metrics, and integration compatibility.
"""

import time
import os
import json
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Callable
from PIL import Image, ImageStat
import sys

# Import all algorithms to test
from bilateral_filtering import bilateral_filter_image
from contrast_enhancement import enhance_contrast
from sharpening import sharpen_image
from binarization import binarize_image
from simple_binarization import binarize_image_pil

# Add will_detector to path
sys.path.append(str(Path(__file__).parent.parent))
from will_detector.classifier import MessageDetector


class ImageQualityMetrics:
    """Calculate image quality metrics for benchmarking."""
    
    @staticmethod
    def calculate_sharpness(image_path: str) -> float:
        """Calculate Laplacian variance as sharpness measure."""
        img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        return cv2.Laplacian(img, cv2.CV_64F).var()
    
    @staticmethod
    def calculate_contrast(image_path: str) -> float:
        """Calculate RMS contrast."""
        img = Image.open(image_path).convert('L')
        stat = ImageStat.Stat(img)
        return stat.stddev[0]
    
    @staticmethod
    def calculate_snr(image_path: str) -> float:
        """Calculate Signal-to-Noise Ratio."""
        img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE).astype(np.float64)
        signal = np.mean(img)
        noise = np.std(img)
        return signal / noise if noise > 0 else float('inf')


class AlgorithmBenchmark:
    """Benchmark individual preprocessing algorithms."""
    
    def __init__(self):
        self.test_images_dir = Path("src/img_compression/data")
        self.output_dir = Path("src/img_compression/data/benchmark_results")
        self.output_dir.mkdir(exist_ok=True)
        
        # Algorithm mapping
        self.algorithms = {
            'bilateral_filtering': bilateral_filter_image,
            'contrast_enhancement': enhance_contrast,
            'sharpening': sharpen_image,
            'binarization': binarize_image,
            'simple_binarization': binarize_image_pil
        }
        
        self.metrics = ImageQualityMetrics()
        
    def benchmark_algorithm(self, algo_name: str, algo_func: Callable, 
                          input_path: str, runs: int = 5) -> Dict:
        """Benchmark a single algorithm."""
        output_path = self.output_dir / f"{algo_name}_output.png"
        
        # Performance benchmarking
        times = []
        for _ in range(runs):
            start_time = time.perf_counter()
            result_path = algo_func(input_path, str(output_path))
            end_time = time.perf_counter()
            times.append(end_time - start_time)
        
        # Quality metrics
        sharpness = self.metrics.calculate_sharpness(str(output_path))
        contrast = self.metrics.calculate_contrast(str(output_path))
        snr = self.metrics.calculate_snr(str(output_path))
        
        # File size
        file_size = os.path.getsize(output_path)
        
        return {
            'algorithm': algo_name,
            'avg_processing_time': np.mean(times),
            'min_processing_time': np.min(times),
            'max_processing_time': np.max(times),
            'std_processing_time': np.std(times),
            'sharpness_score': sharpness,
            'contrast_score': contrast,
            'snr_score': snr,
            'output_file_size': file_size,
            'output_path': str(output_path)
        }
    
    def run_comprehensive_benchmark(self) -> Dict:
        """Run benchmarks on all algorithms."""
        input_image = self.test_images_dir / "test_input.png"
        
        if not input_image.exists():
            raise FileNotFoundError(f"Test image not found: {input_image}")
        
        results = {}
        baseline_metrics = {
            'sharpness': self.metrics.calculate_sharpness(str(input_image)),
            'contrast': self.metrics.calculate_contrast(str(input_image)),
            'snr': self.metrics.calculate_snr(str(input_image))
        }
        
        for algo_name, algo_func in self.algorithms.items():
            try:
                print(f"Benchmarking {algo_name}...")
                result = self.benchmark_algorithm(algo_name, algo_func, str(input_image))
                
                # Calculate improvement ratios
                result['sharpness_improvement'] = result['sharpness_score'] / baseline_metrics['sharpness']
                result['contrast_improvement'] = result['contrast_score'] / baseline_metrics['contrast']
                result['snr_improvement'] = result['snr_score'] / baseline_metrics['snr']
                
                results[algo_name] = result
                
            except Exception as e:
                print(f"Error benchmarking {algo_name}: {e}")
                results[algo_name] = {'error': str(e)}
        
        return {
            'baseline_metrics': baseline_metrics,
            'algorithm_results': results,
            'benchmark_timestamp': time.time()
        }


class WillDetectorIntegrationTest:
    """Test integration with Will Detector classification system."""
    
    def __init__(self):
        self.detector = MessageDetector(provider="anthropic")  # Use Claude for testing
        self.benchmark = AlgorithmBenchmark()
    
    def test_classification_accuracy(self, preprocessed_image_path: str) -> Dict:
        """Test Will Detector classification on preprocessed image."""
        try:
            result = self.detector.analyze_screenshot(preprocessed_image_path)
            return {
                'classification_success': True,
                'reasoning': result.get('reasoning', ''),
                'will_sighting': result.get('willSighting', False),
                'verbatim_quote': result.get('verbatimQuote', ''),
                'response_valid': all(key in result for key in ['reasoning', 'willSighting', 'verbatimQuote'])
            }
        except Exception as e:
            return {
                'classification_success': False,
                'error': str(e),
                'reasoning': '',
                'will_sighting': False,
                'verbatim_quote': '',
                'response_valid': False
            }
    
    def integration_benchmark(self) -> Dict:
        """Run integration tests with all preprocessing algorithms."""
        benchmark_results = self.benchmark.run_comprehensive_benchmark()
        integration_results = {}
        
        for algo_name, algo_data in benchmark_results['algorithm_results'].items():
            if 'error' in algo_data:
                integration_results[algo_name] = {'integration_error': algo_data['error']}
                continue
                
            output_path = algo_data['output_path']
            if os.path.exists(output_path):
                classification_result = self.test_classification_accuracy(output_path)
                integration_results[algo_name] = {
                    'performance': algo_data,
                    'classification': classification_result
                }
        
        return {
            'integration_results': integration_results,
            'baseline_metrics': benchmark_results['baseline_metrics']
        }


def main():
    """Run complete benchmarking suite."""
    print("Starting comprehensive image preprocessing benchmark...")
    
    # Run integration benchmark
    integration_test = WillDetectorIntegrationTest()
    results = integration_test.integration_benchmark()
    
    # Save results
    results_file = Path("src/img_compression/data/benchmark_results/comprehensive_results.json")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"Benchmark complete. Results saved to: {results_file}")
    
    # Print summary
    print("\n=== BENCHMARK SUMMARY ===")
    for algo_name, data in results['integration_results'].items():
        if 'integration_error' in data:
            print(f"{algo_name}: ERROR - {data['integration_error']}")
            continue
            
        perf = data['performance']
        classif = data['classification']
        
        print(f"\n{algo_name.upper()}:")
        print(f"  Processing Time: {perf['avg_processing_time']:.4f}s")
        print(f"  Sharpness Improvement: {perf['sharpness_improvement']:.2f}x")
        print(f"  Contrast Improvement: {perf['contrast_improvement']:.2f}x")
        print(f"  Classification Success: {classif['classification_success']}")
        print(f"  Response Valid: {classif['response_valid']}")


if __name__ == "__main__":
    main()
```

#### Create: `src/img_compression/algorithm_selector.py`
**Purpose**: Algorithm selection logic based on benchmark results
**Content Structure**:
```python
"""
Algorithm selection system based on benchmark results.
Selects top 2 performing algorithms for Will Detector integration.
"""

import json
from pathlib import Path
from typing import Dict, List, Tuple


class AlgorithmSelector:
    """Select best performing algorithms based on benchmark results."""
    
    def __init__(self, results_path: str = "src/img_compression/data/benchmark_results/comprehensive_results.json"):
        self.results_path = Path(results_path)
        self.weights = {
            'processing_time': -0.3,  # Negative because lower is better
            'sharpness_improvement': 0.25,
            'contrast_improvement': 0.25,
            'classification_success': 0.15,
            'response_valid': 0.05
        }
    
    def calculate_algorithm_score(self, algo_data: Dict) -> float:
        """Calculate weighted score for an algorithm."""
        if 'integration_error' in algo_data or 'error' in algo_data.get('performance', {}):
            return 0.0
        
        perf = algo_data['performance']
        classif = algo_data['classification']
        
        # Normalize processing time (convert to score where lower time = higher score)
        time_score = 1.0 / (1.0 + perf['avg_processing_time'])
        
        score = (
            self.weights['processing_time'] * time_score +
            self.weights['sharpness_improvement'] * perf.get('sharpness_improvement', 1.0) +
            self.weights['contrast_improvement'] * perf.get('contrast_improvement', 1.0) +
            self.weights['classification_success'] * (1.0 if classif['classification_success'] else 0.0) +
            self.weights['response_valid'] * (1.0 if classif['response_valid'] else 0.0)
        )
        
        return score
    
    def select_top_algorithms(self, num_algorithms: int = 2) -> List[Tuple[str, float, Dict]]:
        """Select top N algorithms based on weighted scoring."""
        if not self.results_path.exists():
            raise FileNotFoundError(f"Benchmark results not found: {self.results_path}")
        
        with open(self.results_path, 'r') as f:
            results = json.load(f)
        
        algorithm_scores = []
        
        for algo_name, algo_data in results['integration_results'].items():
            score = self.calculate_algorithm_score(algo_data)
            algorithm_scores.append((algo_name, score, algo_data))
        
        # Sort by score (descending)
        algorithm_scores.sort(key=lambda x: x[1], reverse=True)
        
        return algorithm_scores[:num_algorithms]
    
    def generate_selection_report(self) -> Dict:
        """Generate detailed selection report."""
        all_scores = self.select_top_algorithms(num_algorithms=10)  # Get all algorithms
        top_2 = all_scores[:2]
        
        report = {
            'selection_criteria': self.weights,
            'top_algorithms': [],
            'all_algorithms': []
        }
        
        for algo_name, score, data in all_scores:
            algo_info = {
                'name': algo_name,
                'score': score,
                'selected': algo_name in [top_2[0][0], top_2[1][0]] if len(top_2) >= 2 else False
            }
            
            if 'performance' in data:
                algo_info.update({
                    'avg_processing_time': data['performance']['avg_processing_time'],
                    'sharpness_improvement': data['performance'].get('sharpness_improvement', 1.0),
                    'contrast_improvement': data['performance'].get('contrast_improvement', 1.0),
                    'classification_success': data['classification']['classification_success'],
                    'response_valid': data['classification']['response_valid']
                })
            
            report['all_algorithms'].append(algo_info)
        
        # Extract top 2 for integration
        report['top_algorithms'] = report['all_algorithms'][:2]
        
        return report


def main():
    """Run algorithm selection."""
    selector = AlgorithmSelector()
    
    try:
        report = selector.generate_selection_report()
        
        # Save selection report
        report_path = Path("src/img_compression/data/benchmark_results/selection_report.json")
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print("=== ALGORITHM SELECTION RESULTS ===")
        print(f"Selection criteria weights: {selector.weights}")
        print(f"\nTop 2 Selected Algorithms:")
        
        for i, algo in enumerate(report['top_algorithms'], 1):
            print(f"{i}. {algo['name'].upper()}")
            print(f"   Score: {algo['score']:.4f}")
            print(f"   Processing Time: {algo.get('avg_processing_time', 'N/A')}s")
            print(f"   Sharpness Improvement: {algo.get('sharpness_improvement', 'N/A')}x")
            print(f"   Contrast Improvement: {algo.get('contrast_improvement', 'N/A')}x")
            print(f"   Classification Success: {algo.get('classification_success', 'N/A')}")
            print()
        
        print(f"Selection report saved to: {report_path}")
        
        return report['top_algorithms']
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Please run benchmark_algorithms.py first to generate benchmark results.")
        return []


if __name__ == "__main__":
    main()
```

### File Modifications

#### File 1: `src/will_detector/classifier.py`
**Current State**: Existing MessageDetector class with OpenAI/Anthropic support
**Required Changes**:

1. **After Line 97**: Add preprocessing integration method:
   ```python
   def analyze_screenshot_with_preprocessing(self, screenshot_path, preprocessing_functions=None):
       """
       Analyze screenshot with optional preprocessing.
       
       Args:
           screenshot_path: Path to screenshot
           preprocessing_functions: List of preprocessing functions to apply
           
       Returns:
           Analysis result with preprocessing info
       """
       import tempfile
       import os
       from pathlib import Path
       
       if not preprocessing_functions:
           return self.analyze_screenshot(screenshot_path)
       
       # Apply preprocessing pipeline
       processed_paths = []
       current_path = screenshot_path
       
       with tempfile.TemporaryDirectory() as temp_dir:
           for i, preprocess_func in enumerate(preprocessing_functions):
               output_path = os.path.join(temp_dir, f"processed_{i}.png")
               current_path = preprocess_func(current_path, output_path)
               processed_paths.append(current_path)
           
           # Analyze the final processed image
           result = self.analyze_screenshot(current_path)
           
           # Add preprocessing info to result
           result['preprocessing_applied'] = [func.__name__ for func in preprocessing_functions]
           result['preprocessing_steps'] = len(preprocessing_functions)
           
           return result
   ```

#### File 2: `src/will_detector/run_config.py`
**Current State**: Configuration file (assumed to exist based on file listing)
**Required Changes**:

1. **Read current content first, then add after existing config**:
   ```python
   # Image preprocessing configuration
   PREPROCESSING_CONFIG = {
       'enabled': True,
       'selected_algorithms': [],  # Will be populated by algorithm selector
       'algorithm_functions': {},  # Will be populated during integration
       'fallback_to_original': True,  # Use original image if preprocessing fails
       'temp_dir': 'data/temp_preprocessing',
       'save_processed_images': False,  # Set to True for debugging
   }
   ```

### New Test File Creation

#### Create: `tests/img_compression/test_benchmark_algorithms.py`
**Purpose**: Test the benchmarking system
**Content Structure**:
```python
"""Tests for image preprocessing algorithm benchmarking system."""

import unittest
import json
import os
import tempfile
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from img_compression.benchmark_algorithms import AlgorithmBenchmark, ImageQualityMetrics, WillDetectorIntegrationTest


class TestBenchmarkSystem(unittest.TestCase):
    
    def setUp(self):
        self.test_image = Path("src/img_compression/data/test_input.png")
        
    def test_image_quality_metrics(self):
        """if ImageQualityMetrics doesn't calculate metrics then broken"""
        if not self.test_image.exists():
            self.skipTest(f"Test image not found: {self.test_image}")
        
        metrics = ImageQualityMetrics()
        
        sharpness = metrics.calculate_sharpness(str(self.test_image))
        contrast = metrics.calculate_contrast(str(self.test_image))
        snr = metrics.calculate_snr(str(self.test_image))
        
        self.assertIsInstance(sharpness, float)
        self.assertIsInstance(contrast, float)
        self.assertIsInstance(snr, float)
        self.assertGreater(sharpness, 0)
        self.assertGreater(contrast, 0)
        print("✓ ImageQualityMetrics calculates all metrics correctly")
    
    def test_algorithm_benchmark_initialization(self):
        """if AlgorithmBenchmark doesn't initialize correctly then broken"""
        benchmark = AlgorithmBenchmark()
        
        self.assertEqual(len(benchmark.algorithms), 5)
        self.assertIn('bilateral_filtering', benchmark.algorithms)
        self.assertIn('contrast_enhancement', benchmark.algorithms)
        self.assertIn('sharpening', benchmark.algorithms)
        self.assertIn('binarization', benchmark.algorithms)
        self.assertIn('simple_binarization', benchmark.algorithms)
        print("✓ AlgorithmBenchmark initializes with all 5 algorithms")
    
    def test_benchmark_results_structure(self):
        """if benchmark results don't have required structure then broken"""
        if not self.test_image.exists():
            self.skipTest(f"Test image not found: {self.test_image}")
        
        benchmark = AlgorithmBenchmark()
        
        # Test single algorithm benchmarking
        algo_func = benchmark.algorithms['bilateral_filtering']
        result = benchmark.benchmark_algorithm('bilateral_filtering', algo_func, str(self.test_image), runs=1)
        
        required_keys = [
            'algorithm', 'avg_processing_time', 'sharpness_score', 
            'contrast_score', 'snr_score', 'output_file_size', 'output_path'
        ]
        
        for key in required_keys:
            self.assertIn(key, result)
        
        self.assertIsInstance(result['avg_processing_time'], float)
        self.assertGreater(result['avg_processing_time'], 0)
        print("✓ Benchmark results have correct structure and valid data")


class TestAlgorithmSelector(unittest.TestCase):
    
    def test_selector_initialization(self):
        """if AlgorithmSelector doesn't initialize with correct weights then broken"""
        from img_compression.algorithm_selector import AlgorithmSelector
        
        selector = AlgorithmSelector()
        
        # Check that weights sum to reasonable value and include all criteria
        required_criteria = ['processing_time', 'sharpness_improvement', 'contrast_improvement', 
                           'classification_success', 'response_valid']
        
        for criterion in required_criteria:
            self.assertIn(criterion, selector.weights)
        
        print("✓ AlgorithmSelector initializes with all required criteria")
    
    def test_score_calculation(self):
        """if AlgorithmSelector doesn't calculate valid scores then broken"""
        from img_compression.algorithm_selector import AlgorithmSelector
        
        selector = AlgorithmSelector()
        
        # Mock algorithm data
        mock_data = {
            'performance': {
                'avg_processing_time': 0.5,
                'sharpness_improvement': 1.2,
                'contrast_improvement': 1.1
            },
            'classification': {
                'classification_success': True,
                'response_valid': True
            }
        }
        
        score = selector.calculate_algorithm_score(mock_data)
        
        self.assertIsInstance(score, float)
        self.assertGreater(score, 0)
        print("✓ AlgorithmSelector calculates valid scores")


if __name__ == '__main__':
    unittest.main()
```

#### Create: `src/img_compression/integration_pipeline.py`
**Purpose**: Integration pipeline for Will Detector preprocessing
**Content Structure**:
```python
"""
Integration pipeline for selected preprocessing algorithms with Will Detector.
"""

import json
import sys
from pathlib import Path
from typing import List, Callable, Dict, Optional

# Add will_detector to path
sys.path.append(str(Path(__file__).parent.parent))

from will_detector.classifier import MessageDetector


class PreprocessingPipeline:
    """Manages preprocessing pipeline for Will Detector."""
    
    def __init__(self, config_path: str = "src/img_compression/data/benchmark_results/selection_report.json"):
        self.config_path = Path(config_path)
        self.selected_algorithms = []
        self.algorithm_functions = {}
        self.detector = MessageDetector(provider="anthropic")
        
        self._load_selected_algorithms()
    
    def _load_selected_algorithms(self):
        """Load selected algorithms from benchmark results."""
        if not self.config_path.exists():
            print(f"Warning: Selection report not found at {self.config_path}")
            print("Please run algorithm selection first.")
            return
        
        with open(self.config_path, 'r') as f:
            selection_data = json.load(f)
        
        # Import algorithm functions
        from bilateral_filtering import bilateral_filter_image
        from contrast_enhancement import enhance_contrast
        from sharpening import sharpen_image
        from binarization import binarize_image
        from simple_binarization import binarize_image_pil
        
        function_map = {
            'bilateral_filtering': bilateral_filter_image,
            'contrast_enhancement': enhance_contrast,
            'sharpening': sharpen_image,
            'binarization': binarize_image,
            'simple_binarization': binarize_image_pil
        }
        
        # Get top 2 algorithms
        for algo_info in selection_data.get('top_algorithms', [])[:2]:
            algo_name = algo_info['name']
            if algo_name in function_map:
                self.selected_algorithms.append(algo_name)
                self.algorithm_functions[algo_name] = function_map[algo_name]
        
        print(f"Loaded {len(self.selected_algorithms)} selected algorithms: {self.selected_algorithms}")
    
    def apply_preprocessing(self, image_path: str) -> Dict:
        """Apply preprocessing pipeline and return Will Detector analysis."""
        if not self.selected_algorithms:
            print("No algorithms selected, using original image")
            return self.detector.analyze_screenshot(image_path)
        
        # Apply preprocessing with selected algorithms
        preprocessing_functions = [self.algorithm_functions[name] for name in self.selected_algorithms]
        
        return self.detector.analyze_screenshot_with_preprocessing(
            image_path, 
            preprocessing_functions=preprocessing_functions
        )
    
    def get_pipeline_info(self) -> Dict:
        """Get information about current preprocessing pipeline."""
        return {
            'selected_algorithms': self.selected_algorithms,
            'pipeline_length': len(self.selected_algorithms),
            'config_source': str(self.config_path)
        }


def main():
    """Test the integration pipeline."""
    pipeline = PreprocessingPipeline()
    
    print("=== PREPROCESSING PIPELINE INFO ===")
    info = pipeline.get_pipeline_info()
    for key, value in info.items():
        print(f"{key}: {value}")
    
    # Test with sample image if available
    test_image = Path("src/img_compression/data/test_input.png")
    if test_image.exists():
        print(f"\n=== TESTING WITH {test_image} ===")
        result = pipeline.apply_preprocessing(str(test_image))
        
        print(f"Preprocessing applied: {result.get('preprocessing_applied', 'None')}")
        print(f"Will sighting detected: {result.get('willSighting', False)}")
        print(f"Classification reasoning: {result.get('reasoning', 'N/A')[:100]}...")
    else:
        print(f"\nTest image not found: {test_image}")


if __name__ == "__main__":
    main()
```

### Commands to Execute

Execute these commands in order:
```bash
# 1. Create benchmark results directory
mkdir -p src/img_compression/data/benchmark_results

# 2. Run comprehensive benchmarking
cd src/img_compression && python benchmark_algorithms.py

# 3. Select top 2 algorithms
python algorithm_selector.py

# 4. Test integration pipeline
python integration_pipeline.py

# 5. Run validation tests
cd ../../tests/img_compression && python test_benchmark_algorithms.py

# 6. Verify all files created
find src/img_compression -name "*.py" | grep -E "(benchmark|selector|integration)"

# 7. Check benchmark results
ls -la src/img_compression/data/benchmark_results/

# 8. Verify git status
git status
```

### Important Patterns to Follow

1. **Import Style**: 
   ```python
   # Local imports
   from bilateral_filtering import bilateral_filter_image
   # System imports
   import sys
   from pathlib import Path
   sys.path.append(str(Path(__file__).parent.parent))
   ```

2. **Error Handling**:
   ```python
   try:
       result = algorithm_function(input_path, output_path)
       return {'success': True, 'result': result}
   except Exception as e:
       return {'success': False, 'error': str(e)}
   ```

3. **File Path Handling**:
   ```python
   # Always use Path objects for file operations
   from pathlib import Path
   input_path = Path("src/img_compression/data/test_input.png")
   output_dir = Path("src/img_compression/data/benchmark_results")
   output_dir.mkdir(exist_ok=True)
   ```

4. **JSON Results Structure**:
   ```python
   result = {
       'timestamp': time.time(),
       'algorithm': algo_name,
       'metrics': {...},
       'status': 'success'
   }
   ```

### Edge Cases to Handle

1. **Missing Test Images**: 
   - **Solution**: Check for test_input.png existence before running benchmarks, provide clear error message

2. **Algorithm Import Failures**: 
   - **Solution**: Wrap imports in try-catch, continue with available algorithms

3. **Classification API Failures**: 
   - **Solution**: Return structured error response, don't crash entire benchmark

4. **Temporary File Cleanup**: 
   - **Solution**: Use context managers and tempfile.TemporaryDirectory()

### DO NOT Do These Things
- ❌ Do not modify existing algorithm implementations
- ❌ Do not change Will Detector's core classification logic
- ❌ Do not hardcode algorithm selections (use dynamic selection)
- ❌ Do not skip error handling in benchmarking
- ❌ Do not create new API dependencies

### Success Validation

After implementation, verify:
1. [ ] All 5 algorithms can be benchmarked successfully
2. [ ] Quality metrics are calculated correctly
3. [ ] Integration with Will Detector works without errors
4. [ ] Top 2 algorithms are selected based on scoring
5. [ ] Integration pipeline applies selected algorithms
6. [ ] All test files pass
7. [ ] JSON results files are created with proper structure
8. [ ] No existing functionality is broken

### Rollback Instructions

If implementation fails:
```bash
# Remove all new files
rm -f src/img_compression/benchmark_algorithms.py
rm -f src/img_compression/algorithm_selector.py
rm -f src/img_compression/integration_pipeline.py
rm -f tests/img_compression/test_benchmark_algorithms.py
rm -rf src/img_compression/data/benchmark_results
git checkout -- src/will_detector/classifier.py src/will_detector/run_config.py
```

## Implementation Order

Sonnet should implement in this exact order:
1. First implement: Create ImageQualityMetrics and AlgorithmBenchmark classes
2. Then implement: Create benchmark_algorithms.py and run initial benchmark
3. Then implement: Create algorithm_selector.py and select top algorithms
4. Then implement: Modify classifier.py to add preprocessing support
5. Then implement: Create integration_pipeline.py for end-to-end preprocessing
6. Finally implement: Create comprehensive test files and validate

This order is important because each component builds on the previous one and the integration depends on having benchmark results.

## Notes for Sonnet

- The benchmark system tests both technical metrics (speed, image quality) and functional metrics (Will Detector classification accuracy)
- Use the existing test_input.png for consistent benchmarking across algorithms
- The scoring system weights classification success heavily since that's the ultimate goal
- Integration with Will Detector should be seamless - no changes to existing screenshot capture workflow
- Focus on the top 2 algorithms to keep processing pipeline efficient
- All preprocessing should be optional and fallback to original image if it fails
- This is a single repo - all paths are relative to `/Users/<USER>/Code3b/Feisty5`

---
End of Implementation Plan