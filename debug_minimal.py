#!/usr/bin/env python3
"""Minimal debug script to isolate the hanging issue"""

import subprocess
import time
import sys

def test_minimal():
    print("🔍 Minimal Things3 Test")
    
    # Test 1: Direct AppleScript
    print("\n1. Direct osascript test:")
    start = time.time()
    try:
        cmd = ['osascript', '-e', 'tell application "Things3" to get name of first to do of list "Today" whose status is open']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
        duration = time.time() - start
        print(f"   Result: {result.stdout.strip()}")
        print(f"   Time: {duration:.3f}s")
        print(f"   Status: {'✅ Fast' if duration < 1 else '⚠️ Slow'}")
    except subprocess.TimeoutExpired:
        print("   ❌ TIMEOUT - AppleScript hanging")
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
    
    # Test 2: Argparse overhead
    print("\n2. Argparse overhead test:")
    start = time.time()
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--top-task-only', action='store_true')
    # Simulate the args that would be passed
    args = parser.parse_args(['--top-task-only'])
    duration = time.time() - start
    print(f"   Time: {duration:.6f}s (should be ~0.001s)")
    
    # Test 3: Combined minimal script
    print("\n3. Combined test (like our script):")
    start = time.time()
    try:
        import argparse
        parser = argparse.ArgumentParser()
        parser.add_argument('--top-task-only', action='store_true')
        args = parser.parse_args(['--top-task-only'])
        
        if args.top_task_only:
            AS = 'tell application "Things3" to get name of first to do of list "Today" whose status is open'
            result = subprocess.check_output(["osascript", "-e", AS], timeout=5).decode().strip()
            print(f"   Output: {result}")
        
        duration = time.time() - start
        print(f"   Total time: {duration:.3f}s")
        print(f"   Performance: {'✅ Under 1s' if duration < 1 else '⚠️ Over 1s'}")
        
    except subprocess.TimeoutExpired:
        print("   ❌ TIMEOUT in combined test")
    except Exception as e:
        print(f"   ❌ ERROR: {e}")

if __name__ == "__main__":
    test_minimal()