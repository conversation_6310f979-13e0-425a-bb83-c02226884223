# Cerebras GPT-OSS-120B Medium Reasoning Setup

## Configuration for Medium Reasoning

Use this prompt to configure Cerebras GPT-OSS-120B with medium reasoning effort:

```python
import os
from cerebras.cloud.sdk import Cerebras

# Initialize Cerebras client
client = Cerebras(
    api_key=os.environ.get("CEREBRAS_API_KEY"),
)

# Configure for medium reasoning
completion = client.chat.completions.create(
    messages=[
        {
            "role": "system",
            "content": "You are a helpful assistant with balanced reasoning capabilities."
        },
        {
            "role": "user", 
            "content": "Your question here"
        }
    ],
    model="gpt-oss-120b",
    stream=False,
    max_completion_tokens=65536,
    temperature=1,
    top_p=1,
    reasoning_effort="medium"  # Medium reasoning for balanced performance
)
```

## Alternative OpenAI-Compatible Setup

```python
import os
import openai

client = openai.OpenAI(
    base_url="https://api.cerebras.ai/v1",
    api_key=os.environ.get("CEREBRAS_API_KEY")
)

response = client.chat.completions.create(
    model="gpt-oss-120b",
    messages=[...],
    reasoning_effort="medium"
)
```

## Environment Setup

Make sure to set your Cerebras API key:
```bash
export CEREBRAS_API_KEY="your_api_key_here"
```

## Reasoning Effort Levels

- `"low"`: Minimal reasoning, faster responses
- `"medium"`: Moderate reasoning (balanced performance)
- `"high"`: Extensive reasoning, more thorough analysis

## OpenCode Configuration

The project includes an `opencode.json` file that provides instruction files to guide the model's reasoning behavior.
