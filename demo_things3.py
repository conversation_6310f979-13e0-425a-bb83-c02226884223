#!/usr/bin/env python3
"""Demo script to show Things3 import functionality"""

import subprocess
import json
import sys

def demo_script():
    print("🔍 Demonstrating Things3 Import Script")
    print("="*50)
    
    # Test 1: Get first task only
    print("\n1. Get top task only (--top-task-only):")
    try:
        result = subprocess.run([
            'osascript', '-e', 
            'tell application "Things3" to get name of first to do of list "Today" whose status is open'
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print(f"   → '{result.stdout.strip()}'")
        else:
            print("   → No tasks found")
    except Exception as e:
        print(f"   → Error: {e}")
    
    # Test 2: Get 3 tasks (default)
    print("\n2. Get default 3 tasks (JSON format):")
    try:
        # Simple version that gets first 3 tasks
        script = '''
tell application "Things3"
  set L to list "Today"
  set openTodos to (to dos of L whose status is open)
  set result to {}
  repeat with i from 1 to (count of openTodos)
    if i > 3 then exit repeat
    set todo to item i of openTodos
    set end of result to "{\\"id\\": \\"" & (id of todo) & "\\", \\"title\\": \\"" & (name of todo) & "\\"}"
  end repeat
end tell
return "[" & (result as string) & "]"
'''
        result = subprocess.run(['osascript', '-e', script], capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print("   → Sample JSON structure created")
            print("   → [{'id': '...', 'title': '...'}, ...]")
        else:
            print("   → Could not create JSON structure")
    except Exception as e:
        print(f"   → Error: {e}")
    
    # Test 3: Show script usage
    print("\n3. Script Usage Examples:")
    print("   python src/tasklist/things3_import.py                    # Default: 3 tasks, JSON")
    print("   python src/tasklist/things3_import.py --n 5              # 5 tasks, JSON")  
    print("   python src/tasklist/things3_import.py --top-task-only    # First task title only")
    print("   python src/tasklist/things3_import.py --n 10 --top-task-only  # top-task-only overrides n")
    
    print("\n✅ Script is ready for use from other scripts!")

if __name__ == "__main__":
    demo_script()