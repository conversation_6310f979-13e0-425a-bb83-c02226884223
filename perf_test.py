#!/usr/bin/env python3
"""Test performance of things3_import script"""

import subprocess
import sys
import time

def test_performance():
    print("🚀 Performance Test")
    print("="*40)
    
    test_cases = [
        (["--top-task-only"], "Top task only"),
        (["--n", "1"], "Single task"), 
        (["--n", "3"], "Default 3 tasks")
    ]
    
    for args, desc in test_cases:
        cmd = [sys.executable, "src/tasklist/things3_import.py"] + args
            
        print(f"\n🔍 Testing {desc}:")
        
        # Time the execution
        start_time = time.time()
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"⏱️  External timing: {duration:.3f}s")
            
            # Check if script reported its own timing
            if result.stderr and "Execution time:" in result.stderr:
                print(f"📊 Script timing: {result.stderr.strip()}")
            
            # Performance assessment
            if duration < 1.0:
                print("✅ FAST - Under 1 second")
            elif duration < 3.0:
                print("⚠️  ACCEPTABLE - Under 3 seconds") 
            else:
                print("❌ SLOW - Over 3 seconds")
                
            # Show sample output (first line only)
            if result.stdout:
                first_line = result.stdout.split('\n')[0]
                if len(first_line) > 50:
                    first_line = first_line[:47] + "..."
                print(f"📄 Output: {first_line}")
                
        except subprocess.TimeoutExpired:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ TIMEOUT after {duration:.1f}s - Script hanging!")
            
        except Exception as e:
            print(f"❌ ERROR: {e}")
    
    print(f"\n🎯 TARGET: All operations should complete in <1 second")
    print("="*40)

if __name__ == "__main__":
    test_performance()