{"files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/.hg/store/**": true, "**/.trunk/*actions/": true, "**/.trunk/*logs/": true, "**/.trunk/*notifications/": true, "**/.trunk/*out/": true, "**/.trunk/*plugins/": true, "**/node_modules/**": true, "**/.venv/**": true, "**/__pycache__/**": true, "**/.git/**": true, "**/.next/**": true, "_tmp/**": true, "af/**/node_modules/**": true, "af/**/.next/**": true, "**/.vscode/**": true}, "python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true}