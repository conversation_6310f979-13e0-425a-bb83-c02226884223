#!/usr/bin/env python3
"""Validate the top task checker system"""

import sys
from pathlib import Path

def validate_system():
    """Validate that all components of the top task system exist and work"""
    print("🎯 Top Task Checker System Validation")
    print("="*50)
    
    # Check directory structure
    base_dir = Path("src/top_task_checker")
    required_files = [
        "main.py",
        "config.py", 
        "task_fetcher.py",
        "focus_analyzer.py",
        "hash_detector.py",
        "alerter.py",
        "__init__.py"
    ]
    
    print("\n📁 Directory Structure:")
    missing_files = []
    for file in required_files:
        file_path = base_dir / file
        if file_path.exists():
            size_kb = file_path.stat().st_size / 1024
            print(f"✅ {file} ({size_kb:.1f} KB)")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(file)
    
    # Check test structure
    test_dir = Path("tests/top_task_checker")
    test_files = [
        "test_config.py",
        "test_task_fetcher.py", 
        "test_integration.py",
        "__init__.py"
    ]
    
    print("\n🧪 Test Structure:")
    for file in test_files:
        file_path = test_dir / file
        if file_path.exists():
            size_kb = file_path.stat().st_size / 1024
            print(f"✅ {file} ({size_kb:.1f} KB)")
        else:
            print(f"❌ {file} - MISSING")
            missing_files.append(f"tests/{file}")
    
    # Check data directories
    data_dirs = [
        base_dir / "data" / "screenshots",
        base_dir / "data" / "logs", 
        test_dir / "test_data"
    ]
    
    print("\n📊 Data Directories:")
    for dir_path in data_dirs:
        if dir_path.exists():
            print(f"✅ {dir_path}")
        else:
            print(f"❌ {dir_path} - MISSING")
    
    # Test basic imports
    print("\n🔧 Component Imports:")
    sys.path.insert(0, str(base_dir))
    
    components = [
        ("config", "TaskFocusConfig"),
        ("task_fetcher", "TaskFetcher"),
        ("focus_analyzer", "FocusAnalyzer"), 
        ("hash_detector", "HashDetector"),
        ("alerter", "Alerter")
    ]
    
    import_errors = []
    for module, class_name in components:
        try:
            mod = __import__(module)
            cls = getattr(mod, class_name)
            print(f"✅ {module}.{class_name}")
        except Exception as e:
            print(f"❌ {module}.{class_name} - {e}")
            import_errors.append(f"{module}.{class_name}")
    
    # Test configuration modes
    print("\n⚙️  Configuration Modes:")
    try:
        from config import TaskFocusConfig
        
        modes = [
            ("Default", TaskFocusConfig()),
            ("Performance", TaskFocusConfig.create_performance_mode()),
            ("Focus", TaskFocusConfig.create_focus_mode()), 
            ("Space-saving", TaskFocusConfig.create_space_saving_mode())
        ]
        
        for mode_name, config in modes:
            refresh = config.TASK_REFRESH_INTERVAL
            screenshot = config.SCREENSHOT_INTERVAL
            print(f"✅ {mode_name}: {refresh}s task refresh, {screenshot}s screenshots")
            
    except Exception as e:
        print(f"❌ Configuration modes failed: {e}")
        import_errors.append("config modes")
    
    # Test Things3 integration
    print("\n🎯 Things3 Integration:")
    things3_script = Path("src/tasklist/things3_import.py")
    if things3_script.exists():
        print(f"✅ Things3 script exists")
        
        # Check if it has the required functionality
        with open(things3_script, 'r') as f:
            content = f.read()
            if '--top-task-only' in content:
                print("✅ --top-task-only parameter supported")
            else:
                print("❌ --top-task-only parameter missing")
                missing_files.append("things3 --top-task-only support")
    else:
        print("❌ Things3 script missing")
        missing_files.append("src/tasklist/things3_import.py")
    
    # Summary
    print(f"\n📋 Validation Summary:")
    total_issues = len(missing_files) + len(import_errors)
    
    if total_issues == 0:
        print("🎉 ALL CHECKS PASSED - System is ready!")
        
        print(f"\n🚀 Usage:")
        print("python src/top_task_checker/main.py                    # Default mode")
        print("python src/top_task_checker/main.py --config-mode focus # Focus mode")
        print("python src/top_task_checker/main.py --debug            # Debug mode")
        
        return True
    else:
        print(f"⚠️  {total_issues} issues found:")
        
        if missing_files:
            print("Missing files:")
            for file in missing_files:
                print(f"  - {file}")
        
        if import_errors:
            print("Import errors:")
            for error in import_errors:
                print(f"  - {error}")
                
        return False

if __name__ == "__main__":
    success = validate_system()
    sys.exit(0 if success else 1)