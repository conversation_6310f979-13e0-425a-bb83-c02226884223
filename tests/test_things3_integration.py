#!/usr/bin/env python3
"""Test Things3 integration with basic_checker_tasklist"""

import sys
import os
from pathlib import Path
from unittest.mock import patch, MagicMock
import subprocess

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src" / "basic_checker_tasklist"))

from detector import get_top_task, analyze_distraction
from voice_generator import generate_kind_voice_message

def test_get_top_task_fallback():
    """if get_top_task doesn't return a string then broken"""
    task = get_top_task()
    assert isinstance(task, str), f"Expected string, got {type(task)}"
    assert len(task.strip()) > 0, "Task should not be empty"
    print(f"✓ get_top_task returns: '{task}'")

def test_get_top_task_with_mock_success():
    """if get_top_task doesn't use Things3 output when available then broken"""
    mock_result = MagicMock()
    mock_result.returncode = 0
    mock_result.stdout = "Important Project Task"
    
    with patch('subprocess.run', return_value=mock_result):
        task = get_top_task()
        assert task == "Important Project Task", f"Expected 'Important Project Task', got '{task}'"
    print("✓ get_top_task uses Things3 output when available")

def test_get_top_task_with_mock_failure():
    """if get_top_task doesn't fallback to 'feisty' when Things3 fails then broken"""
    mock_result = MagicMock()
    mock_result.returncode = 1
    mock_result.stdout = ""
    
    with patch('subprocess.run', return_value=mock_result):
        task = get_top_task()
        assert task == "feisty", f"Expected 'feisty' fallback, got '{task}'"
    print("✓ get_top_task falls back to 'feisty' on failure")

def test_get_top_task_with_exception():
    """if get_top_task doesn't handle exceptions gracefully then broken"""
    with patch('subprocess.run', side_effect=subprocess.TimeoutExpired(['cmd'], 5)):
        task = get_top_task()
        assert task == "feisty", f"Expected 'feisty' fallback on exception, got '{task}'"
    print("✓ get_top_task handles exceptions gracefully")

def test_voice_generator_uses_dynamic_task():
    """if voice_generator doesn't use dynamic task name then broken"""
    
    # Mock the OpenAI client and get_top_task
    mock_response = MagicMock()
    mock_response.choices[0].message.content = '{"activity": "browsing", "voice_message": "Focus on your custom task now!"}'
    
    mock_client = MagicMock()
    mock_client.chat.completions.create.return_value = mock_response
    
    with patch('voice_generator.OpenAI', return_value=mock_client):
        with patch('voice_generator.get_top_task', return_value="custom_task"):
            with patch.dict(os.environ, {'OPENAI_API_KEY': 'test_key'}):
                message = generate_kind_voice_message("User is browsing social media")
                
                # Verify the task name was used in the API call
                call_args = mock_client.chat.completions.create.call_args
                content = call_args[1]['messages'][0]['content']
                assert "coding custom_task" in content, f"Expected 'coding custom_task' in prompt, got: {content}"
                
    print("✓ voice_generator uses dynamic task name in prompts")

def test_integration_end_to_end():
    """if integration doesn't work end-to-end then broken"""
    
    # Test that detector and voice generator both use the same task
    with patch('detector.subprocess.run') as mock_run:
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "test_task"
        mock_run.return_value = mock_result
        
        # Test detector uses dynamic task
        task_from_detector = get_top_task()
        assert task_from_detector == "test_task", f"Detector should return 'test_task', got '{task_from_detector}'"
        
        # Test voice generator would use the same task
        with patch('voice_generator.get_top_task', return_value="test_task"):
            with patch('voice_generator.OpenAI') as mock_openai:
                mock_response = MagicMock()
                mock_response.choices[0].message.content = '{"activity": "distracted", "voice_message": "Back to test_task!"}'
                mock_openai.return_value.chat.completions.create.return_value = mock_response
                
                with patch.dict(os.environ, {'OPENAI_API_KEY': 'test_key'}):
                    voice_message = generate_kind_voice_message("User distracted")
                    assert "Back to test_task!" in voice_message, f"Voice message should mention task: {voice_message}"
    
    print("✓ End-to-end integration works correctly")

if __name__ == "__main__":
    print("Testing Things3 integration...")
    test_get_top_task_fallback()
    test_get_top_task_with_mock_success() 
    test_get_top_task_with_mock_failure()
    test_get_top_task_with_exception()
    test_voice_generator_uses_dynamic_task()
    test_integration_end_to_end()
    print("✅ All Things3 integration tests passed!")