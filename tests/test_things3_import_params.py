import json
import subprocess
import sys
from unittest.mock import patch, MagicMock
import os
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src', 'tasklist'))
from things3_import import main

def test_default_n_parameter():
    """if default n doesn't return 3 tasks then broken"""
    mock_output = "id1\nid2\nid3\nid4\nid5\n---\ntask1\ntask2\ntask3\ntask4\ntask5"
    
    with patch('subprocess.check_output', return_value=mock_output.encode()):
        with patch('sys.argv', ['script']):
            with patch('builtins.print') as mock_print:
                main()
                
                args, kwargs = mock_print.call_args
                output = json.loads(args[0])
                
                assert len(output) == 3, f"Expected 3 tasks by default, got {len(output)}"
                print("✓ Default n=3 works")

def test_custom_n_parameter():
    """if n=5 doesn't return 5 tasks then broken"""
    mock_output = "id1\nid2\nid3\nid4\nid5\n---\ntask1\ntask2\ntask3\ntask4\ntask5"
    
    with patch('subprocess.check_output', return_value=mock_output.encode()):
        with patch('sys.argv', ['script', '--n', '5']):
            with patch('builtins.print') as mock_print:
                main()
                
                args, kwargs = mock_print.call_args
                output = json.loads(args[0])
                
                assert len(output) == 5, f"Expected 5 tasks, got {len(output)}"
                print("✓ Custom n=5 works")

def test_top_task_only_flag():
    """if top-task-only doesn't return only the first task title as string then broken"""
    # Mock the direct AppleScript call that --top-task-only uses
    with patch('subprocess.check_output', return_value="First Task\n".encode()):
        with patch('sys.argv', ['script', '--top-task-only']):
            with patch('builtins.print') as mock_print:
                main()
                
                args, kwargs = mock_print.call_args
                output = args[0]
                
                assert output == "First Task", f"Expected 'First Task', got '{output}'"
                assert isinstance(output, str), "Output should be a string, not JSON"
                print("✓ Top task only returns string")

def test_top_task_only_overrides_n():
    """if top-task-only with n=5 doesn't return only first task then broken"""
    # Mock the direct AppleScript call that --top-task-only uses
    with patch('subprocess.check_output', return_value="First Task\n".encode()):
        with patch('sys.argv', ['script', '--n', '5', '--top-task-only']):
            with patch('builtins.print') as mock_print:
                main()
                
                args, kwargs = mock_print.call_args
                output = args[0]
                
                assert output == "First Task", "top-task-only should override n parameter"
                print("✓ Top task only overrides n parameter")

if __name__ == "__main__":
    test_default_n_parameter()
    test_custom_n_parameter()
    test_top_task_only_flag()
    test_top_task_only_overrides_n()
    print("All parameter tests passed!")