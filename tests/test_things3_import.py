import json
import subprocess
from unittest.mock import patch, MagicMock
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src', 'tasklist'))
from things3_import import main

def test_returns_top_3_tasks():
    """if script returns more than 3 tasks then broken"""
    mock_output = "id1\nid2\nid3\nid4\nid5\n---\ntask1\ntask2\ntask3\ntask4\ntask5"
    
    with patch('subprocess.check_output', return_value=mock_output.encode()):
        with patch('sys.argv', ['test']):
            with patch('builtins.print') as mock_print:
                main()
                
                args, kwargs = mock_print.call_args
                output = json.loads(args[0])
                
                assert len(output) == 3, f"Expected 3 tasks, got {len(output)}"
                print("✓ Returns exactly 3 tasks")

def test_pretty_print_format():
    """if output isn't pretty printed then broken"""
    mock_output = "id1\n---\ntask1"
    
    with patch('subprocess.check_output', return_value=mock_output.encode()):
        with patch('sys.argv', ['test']):
            with patch('builtins.print') as mock_print:
                main()
                
                args, kwargs = mock_print.call_args
                output_str = args[0]
                
                assert '\n' in output_str, "Output should be pretty printed with newlines"
                assert '  ' in output_str, "Output should have indentation"
                print("✓ Output is pretty printed")

def test_correct_json_structure():
    """if json structure doesn't have id and title then broken"""
    mock_output = "id1\nid2\n---\ntask1\ntask2"
    
    with patch('subprocess.check_output', return_value=mock_output.encode()):
        with patch('sys.argv', ['test']):
            with patch('builtins.print') as mock_print:
                main()
                
                args, kwargs = mock_print.call_args
                output = json.loads(args[0])
                
                assert all('id' in task and 'title' in task for task in output), "Each task should have id and title"
                print("✓ JSON has correct structure")

if __name__ == "__main__":
    test_returns_top_3_tasks()
    test_pretty_print_format()
    test_correct_json_structure()
    print("All tests passed!")