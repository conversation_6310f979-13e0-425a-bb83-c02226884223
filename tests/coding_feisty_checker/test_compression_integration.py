import sys
import tempfile
import time
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from basic_checker_hardcoded.hash_change_detection import calculate_perceptual_hash
from basic_checker_hardcoded.main import CONFIG
from will_detector.screenshot import capture_active_window_screenshot


def test_compression_file_handling():
    """Test that compression creates both original and compressed files correctly."""

    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Test with compression enabled
        CONFIG.SCREENSHOT_COMPRESS = True
        CONFIG.KEEP_ORIGINAL_AFTER_COMPRESSION = True

        screenshot_data = capture_active_window_screenshot(
            output_dir=temp_path, compress=True
        )

        assert screenshot_data is not None
        assert "path" in screenshot_data
        assert "compressed_path" in screenshot_data

        original_path = Path(screenshot_data["path"])
        compressed_path = Path(screenshot_data["compressed_path"])

        # Both files should exist
        assert original_path.exists(), f"Original file not found: {original_path}"
        assert compressed_path.exists(), f"Compressed file not found: {compressed_path}"

        # Compressed file should be smaller
        original_size = original_path.stat().st_size
        compressed_size = compressed_path.stat().st_size
        assert compressed_size < original_size, (
            f"Compressed file not smaller: {compressed_size} >= {original_size}"
        )

        print(
            f"✓ Compression test: {original_size} -> {compressed_size} bytes ({compressed_size / original_size * 100:.1f}%)"
        )


def test_hash_detection_on_different_files():
    """Test hash detection on original vs compressed files."""

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Capture screenshot with compression
        CONFIG.SCREENSHOT_COMPRESS = True
        screenshot_data = capture_active_window_screenshot(
            output_dir=temp_path, compress=True
        )

        original_path = screenshot_data["path"]
        compressed_path = screenshot_data["compressed_path"]

        # Calculate hashes for both files
        original_hash = calculate_perceptual_hash(original_path)
        compressed_hash = calculate_perceptual_hash(compressed_path)

        # Hashes should be different (compression changes the image)
        assert original_hash != compressed_hash, (
            "Original and compressed hashes should be different"
        )

        print(
            f"✓ Hash detection test: original={original_hash[:8]}..., compressed={compressed_hash[:8]}..."
        )


def test_config_driven_file_selection():
    """Test that config determines which file is used for analysis."""

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Capture screenshot with compression
        CONFIG.SCREENSHOT_COMPRESS = True
        screenshot_data = capture_active_window_screenshot(
            output_dir=temp_path, compress=True
        )

        original_path = screenshot_data["path"]
        compressed_path = screenshot_data["compressed_path"]

        # Test config: use original for analysis
        CONFIG.LLM_ANALYZE_COMPRESSED = False
        analysis_file = original_path
        if CONFIG.LLM_ANALYZE_COMPRESSED and "compressed_path" in screenshot_data:
            analysis_file = screenshot_data["compressed_path"]

        assert analysis_file == original_path, (
            "Should use original file when LLM_ANALYZE_COMPRESSED=False"
        )

        # Test config: use compressed for analysis
        CONFIG.LLM_ANALYZE_COMPRESSED = True
        analysis_file = original_path
        if CONFIG.LLM_ANALYZE_COMPRESSED and "compressed_path" in screenshot_data:
            analysis_file = screenshot_data["compressed_path"]

        assert analysis_file == compressed_path, (
            "Should use compressed file when LLM_ANALYZE_COMPRESSED=True"
        )

        print("✓ Config-driven file selection test passed")


def test_compression_performance():
    """Test compression performance and file size reduction."""

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Test compression performance
        start_time = time.time()
        CONFIG.SCREENSHOT_COMPRESS = True
        screenshot_data = capture_active_window_screenshot(
            output_dir=temp_path, compress=True
        )
        compression_time = time.time() - start_time

        original_size = Path(screenshot_data["path"]).stat().st_size
        compressed_size = Path(screenshot_data["compressed_path"]).stat().st_size
        compression_ratio = (original_size - compressed_size) / original_size

        print(
            f"✓ Compression performance: {compression_time:.2f}s, {compression_ratio * 100:.1f}% reduction"
        )

        # Compression should be reasonably fast (< 1 second)
        assert compression_time < 1.0, f"Compression too slow: {compression_time:.2f}s"

        # Should achieve significant compression (> 50% reduction)
        assert compression_ratio > 0.5, (
            f"Compression ratio too low: {compression_ratio * 100:.1f}%"
        )


def test_no_compression_mode():
    """Test behavior when compression is disabled."""

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Test without compression
        CONFIG.SCREENSHOT_COMPRESS = False
        screenshot_data = capture_active_window_screenshot(
            output_dir=temp_path, compress=False
        )

        assert screenshot_data is not None
        assert "path" in screenshot_data
        assert "compressed_path" not in screenshot_data

        original_path = Path(screenshot_data["path"])
        assert original_path.exists(), (
            "Original file should exist even without compression"
        )

        print("✓ No compression mode test passed")


if __name__ == "__main__":
    print("Testing compression integration...")

    test_compression_file_handling()
    print("✓ Compression file handling test passed")

    test_hash_detection_on_different_files()
    print("✓ Hash detection on different files test passed")

    test_config_driven_file_selection()
    print("✓ Config-driven file selection test passed")

    test_compression_performance()
    print("✓ Compression performance test passed")

    test_no_compression_mode()
    print("✓ No compression mode test passed")

    print("\n🎉 All compression integration tests passed!")
