import sys
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from basic_checker_hardcoded.detector import analyze_distraction
from basic_checker_hardcoded.hash_change_detection import (
    calculate_perceptual_hash,
    get_change_percentage,
    has_significant_change,
)
from basic_checker_hardcoded.timer import IntervalTimer


class TestIntegration:
    """Integration tests using real screenshots from monitoring sessions."""

    @property
    def test_data_dir(self):
        """Get the test data directory path."""
        return Path(__file__).parent / "test_data"

    def test_distracted_screenshots_detection(self):
        """Test that distracted screenshots are correctly identified."""
        print("🔍 Testing distracted screenshot detection...")

        distracted_files = [
            "distracted_whatsapp_1.png",
            "distracted_whatsapp_2.png",
        ]

        for filename in distracted_files:
            screenshot_path = self.test_data_dir / filename
            assert screenshot_path.exists(), f"Test file {filename} not found"

            print(f"  Testing {filename}...")
            analysis = analyze_distraction(str(screenshot_path))

            assert analysis.get("distracted_decision") is True, (
                f"Expected distracted=True for {filename}, got {analysis.get('distracted_decision')}"
            )
            assert "reasoning" in analysis, f"Missing reasoning for {filename}"
            assert len(analysis["reasoning"]) > 10, (
                f"Reasoning too short for {filename}"
            )

            print(
                f"    ✓ Correctly identified as distracted: {analysis['reasoning'][:60]}..."
            )

        return True

    def test_focused_screenshots_detection(self):
        """Test that focused screenshots are correctly identified."""
        print("🔍 Testing focused screenshot detection...")

        focused_files = [
            "focused_ide_1.png",
            "focused_ide_2.png",
            "focused_task_complete.png",
            "focused_terminal.png",
        ]

        for filename in focused_files:
            screenshot_path = self.test_data_dir / filename
            assert screenshot_path.exists(), f"Test file {filename} not found"

            print(f"  Testing {filename}...")
            analysis = analyze_distraction(str(screenshot_path))

            assert analysis.get("distracted_decision") is False, (
                f"Expected distracted=False for {filename}, got {analysis.get('distracted_decision')}"
            )
            assert "reasoning" in analysis, f"Missing reasoning for {filename}"
            assert len(analysis["reasoning"]) > 10, (
                f"Reasoning too short for {filename}"
            )

            print(
                f"    ✓ Correctly identified as focused: {analysis['reasoning'][:60]}..."
            )

        return True

    def test_hash_change_detection_with_real_screenshots(self):
        """Test hash change detection using real screenshots."""
        print("🔍 Testing hash change detection with real screenshots...")

        # Test that different types of screenshots have different hashes
        focused_path = self.test_data_dir / "focused_ide_1.png"
        distracted_path = self.test_data_dir / "distracted_whatsapp_1.png"

        focused_hash = calculate_perceptual_hash(str(focused_path))
        distracted_hash = calculate_perceptual_hash(str(distracted_path))

        # Different screenshots should have different hashes
        assert focused_hash != distracted_hash, (
            "Different screenshots should have different hashes"
        )

        # Should detect significant change between different screenshots
        has_change = has_significant_change(focused_hash, distracted_hash)
        change_pct = get_change_percentage(focused_hash, distracted_hash)

        assert has_change is True, (
            "Should detect significant change between different screenshots"
        )
        assert change_pct > 10.0, f"Change percentage should be >10%, got {change_pct}%"

        print(f"    ✓ Hash change detection working: {change_pct:.1f}% change detected")
        return True

    def test_hash_change_detection_identical_screenshots(self):
        """Test that identical screenshots are not flagged as changed."""
        print("🔍 Testing hash change detection with identical screenshots...")

        # Use the same screenshot twice
        screenshot_path = self.test_data_dir / "focused_ide_1.png"
        hash1 = calculate_perceptual_hash(str(screenshot_path))
        hash2 = calculate_perceptual_hash(str(screenshot_path))

        # Identical screenshots should have identical hashes
        assert hash1 == hash2, "Identical screenshots should have identical hashes"

        # Should not detect change for identical screenshots
        has_change = has_significant_change(hash1, hash2)
        change_pct = get_change_percentage(hash1, hash2)

        assert has_change is False, "Should not detect change for identical screenshots"
        assert change_pct == 0.0, f"Change percentage should be 0%, got {change_pct}%"

        print(f"    ✓ Identical screenshots correctly identified: {change_pct}% change")
        return True

    def test_timer_precision_with_hash_detection(self):
        """Test timer precision in a simulated monitoring scenario."""
        print("🔍 Testing timer precision with hash detection simulation...")

        timer = IntervalTimer(1.0)  # 1 second for fast testing
        last_hash = None
        analysis_count = 0
        skip_count = 0

        # Simulate 3 monitoring cycles
        for cycle in range(3):
            timer.start_cycle()

            # Use different screenshots for each cycle to simulate changes
            if cycle == 0:
                screenshot_path = self.test_data_dir / "focused_ide_1.png"
            elif cycle == 1:
                screenshot_path = (
                    self.test_data_dir / "focused_ide_2.png"
                )  # Similar but different
            else:
                screenshot_path = (
                    self.test_data_dir / "distracted_whatsapp_1.png"
                )  # Very different

            # Calculate hash
            current_hash = calculate_perceptual_hash(str(screenshot_path))

            # Check for significant change
            if last_hash is None or has_significant_change(current_hash, last_hash):
                analysis_count += 1
                print(f"    Cycle {cycle + 1}: ANALYZING (change detected)")
            else:
                skip_count += 1
                print(f"    Cycle {cycle + 1}: SKIPPING (no significant change)")

            last_hash = current_hash

            # Wait for precise interval
            timer.wait_for_next()

        # Should have analyzed all cycles (all different screenshots)
        assert analysis_count == 3, f"Expected 3 analyses, got {analysis_count}"
        assert skip_count == 0, f"Expected 0 skips, got {skip_count}"

        print(
            f"    ✓ Timer precision test passed: {analysis_count} analyses, {skip_count} skips"
        )
        return True

    def test_end_to_end_monitoring_simulation(self):
        """Test complete end-to-end monitoring simulation."""
        print("🔍 Testing end-to-end monitoring simulation...")

        timer = IntervalTimer(0.5)  # 0.5 seconds for fast testing
        data_dir = Path(tempfile.mkdtemp())
        last_hash = None
        analysis_count = 0
        skip_count = 0

        # Simulate 4 monitoring cycles with different scenarios
        test_scenarios = [
            ("focused_ide_1.png", "First screenshot - should analyze"),
            ("focused_ide_1.png", "Identical screenshot - should skip"),
            ("focused_ide_2.png", "Similar screenshot - should analyze"),
            ("distracted_whatsapp_1.png", "Very different screenshot - should analyze"),
        ]

        for i, (filename, description) in enumerate(test_scenarios):
            timer.start_cycle()

            screenshot_path = self.test_data_dir / filename
            current_hash = calculate_perceptual_hash(str(screenshot_path))

            # Check for significant change
            if last_hash is None or has_significant_change(current_hash, last_hash):
                analysis_count += 1
                print(f"    Cycle {i + 1}: ANALYZING - {description}")

                # Run actual distraction analysis
                analysis = analyze_distraction(str(screenshot_path))
                print(f"      Result: distracted={analysis.get('distracted_decision')}")

            else:
                skip_count += 1
                print(f"    Cycle {i + 1}: SKIPPING - {description}")

            last_hash = current_hash
            timer.wait_for_next()

        # Should have analyzed 3 out of 4 cycles (skipped the identical one)
        assert analysis_count == 3, f"Expected 3 analyses, got {analysis_count}"
        assert skip_count == 1, f"Expected 1 skip, got {skip_count}"

        api_reduction = (skip_count / (analysis_count + skip_count)) * 100
        print(
            f"    ✓ End-to-end test passed: {analysis_count} analyses, {skip_count} skips"
        )
        print(f"    ✓ API call reduction: {api_reduction:.1f}%")

        # Cleanup
        import shutil

        shutil.rmtree(data_dir)
        return True


def run_integration_tests():
    """Run all integration tests."""
    print("🚀 Running coding feisty checker integration tests...")
    print("=" * 60)

    test_instance = TestIntegration()
    results = []

    try:
        results.append(test_instance.test_distracted_screenshots_detection())
        results.append(test_instance.test_focused_screenshots_detection())
        results.append(test_instance.test_hash_change_detection_with_real_screenshots())
        results.append(test_instance.test_hash_change_detection_identical_screenshots())
        results.append(test_instance.test_timer_precision_with_hash_detection())
        results.append(test_instance.test_end_to_end_monitoring_simulation())

        print("=" * 60)
        if all(results):
            print("🎉 ALL INTEGRATION TESTS PASSED!")
            print("✅ Distracted screenshots correctly identified")
            print("✅ Focused screenshots correctly identified")
            print("✅ Hash change detection working with real data")
            print("✅ Timer precision maintained")
            print("✅ End-to-end monitoring simulation successful")
            return True
        else:
            print("❌ Some integration tests failed")
            return False

    except Exception as e:
        print(f"❌ Integration test failed with error: {e}")
        return False


if __name__ == "__main__":
    success = run_integration_tests()
    exit(0 if success else 1)
