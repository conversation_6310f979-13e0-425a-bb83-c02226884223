import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from basic_checker_hardcoded.main import CONFIG, MonitorConfig


def test_config_defaults():
    """Test that config has expected default values."""
    config = MonitorConfig()

    # Screenshot settings
    assert config.SCREENSHOT_RESOLUTION == "full"
    assert config.SCREENSHOT_COMPRESS == True
    assert config.SCREENSHOT_COMPRESSION_METHOD == "binarize"

    # Analysis settings
    assert config.LLM_ANALYZE_COMPRESSED == False
    assert config.HASH_DETECTION_ON_COMPRESSED == False

    # Storage settings
    assert config.KEEP_ORIGINAL_AFTER_COMPRESSION == True
    assert config.CLEANUP_TEMP_FILES == True

    # Performance settings
    assert config.MONITORING_INTERVAL_SECONDS == 5.0
    assert config.HASH_CHANGE_THRESHOLD == 0.1

    # Voice settings
    assert config.VOICE_ALERTS_ENABLED == True
    assert config.VOICE_RATE == 173


def test_config_scale_factors():
    """Test screenshot scale factor calculation."""
    config = MonitorConfig()

    assert config.get_screenshot_scale() == 1.0

    config.SCREENSHOT_RESOLUTION = "half"
    assert config.get_screenshot_scale() == 0.5

    config.SCREENSHOT_RESOLUTION = "quarter"
    assert config.get_screenshot_scale() == 0.25

    config.SCREENSHOT_RESOLUTION = "invalid"
    assert config.get_screenshot_scale() == 1.0  # fallback


def test_config_compression_scenarios():
    """Test different compression configuration scenarios."""

    # Scenario 1: Keep both files (current behavior)
    config1 = MonitorConfig()
    config1.KEEP_ORIGINAL_AFTER_COMPRESSION = True
    config1.LLM_ANALYZE_COMPRESSED = False
    config1.HASH_DETECTION_ON_COMPRESSED = False
    assert config1.KEEP_ORIGINAL_AFTER_COMPRESSION == True
    assert config1.LLM_ANALYZE_COMPRESSED == False

    # Scenario 2: Use compressed for everything (space-saving)
    config2 = MonitorConfig()
    config2.KEEP_ORIGINAL_AFTER_COMPRESSION = False
    config2.LLM_ANALYZE_COMPRESSED = True
    config2.HASH_DETECTION_ON_COMPRESSED = True
    assert config2.KEEP_ORIGINAL_AFTER_COMPRESSION == False
    assert config2.LLM_ANALYZE_COMPRESSED == True

    # Scenario 3: Hybrid approach
    config3 = MonitorConfig()
    config3.KEEP_ORIGINAL_AFTER_COMPRESSION = False
    config3.LLM_ANALYZE_COMPRESSED = True
    config3.HASH_DETECTION_ON_COMPRESSED = False
    assert config3.LLM_ANALYZE_COMPRESSED == True
    assert config3.HASH_DETECTION_ON_COMPRESSED == False


def test_config_performance_tuning():
    """Test performance-related configuration options."""

    # Fast monitoring (more frequent, less sensitive)
    fast_config = MonitorConfig()
    fast_config.MONITORING_INTERVAL_SECONDS = 2.0
    fast_config.HASH_CHANGE_THRESHOLD = 0.2  # 20% change threshold
    fast_config.VOICE_ALERTS_ENABLED = False

    assert fast_config.MONITORING_INTERVAL_SECONDS == 2.0
    assert fast_config.HASH_CHANGE_THRESHOLD == 0.2
    assert fast_config.VOICE_ALERTS_ENABLED == False

    # Conservative monitoring (less frequent, more sensitive)
    conservative_config = MonitorConfig()
    conservative_config.MONITORING_INTERVAL_SECONDS = 10.0
    conservative_config.HASH_CHANGE_THRESHOLD = 0.05  # 5% change threshold
    conservative_config.VOICE_ALERTS_ENABLED = True

    assert conservative_config.MONITORING_INTERVAL_SECONDS == 10.0
    assert conservative_config.HASH_CHANGE_THRESHOLD == 0.05
    assert conservative_config.VOICE_ALERTS_ENABLED == True


def test_global_config_instance():
    """Test that global CONFIG instance works correctly."""
    # Test that we can access the global config
    assert CONFIG.SCREENSHOT_COMPRESS == True
    assert CONFIG.MONITORING_INTERVAL_SECONDS == 5.0

    # Test that we can modify it
    original_compress = CONFIG.SCREENSHOT_COMPRESS
    CONFIG.SCREENSHOT_COMPRESS = False
    assert CONFIG.SCREENSHOT_COMPRESS == False

    # Restore original value
    CONFIG.SCREENSHOT_COMPRESS = original_compress
    assert CONFIG.SCREENSHOT_COMPRESS == original_compress


def test_config_validation():
    """Test config validation and edge cases."""
    config = MonitorConfig()

    # Test valid compression methods
    config.SCREENSHOT_COMPRESSION_METHOD = "binarize"
    assert config.SCREENSHOT_COMPRESSION_METHOD == "binarize"

    config.SCREENSHOT_COMPRESSION_METHOD = "bilateral"
    assert config.SCREENSHOT_COMPRESSION_METHOD == "bilateral"

    # Test valid resolutions
    for resolution in ["full", "half", "quarter"]:
        config.SCREENSHOT_RESOLUTION = resolution
        assert config.SCREENSHOT_RESOLUTION == resolution

    # Test threshold bounds
    config.HASH_CHANGE_THRESHOLD = 0.0  # No change threshold
    assert config.HASH_CHANGE_THRESHOLD == 0.0

    config.HASH_CHANGE_THRESHOLD = 1.0  # 100% change threshold
    assert config.HASH_CHANGE_THRESHOLD == 1.0


if __name__ == "__main__":
    print("Testing MonitorConfig...")

    test_config_defaults()
    print("✓ Config defaults test passed")

    test_config_scale_factors()
    print("✓ Config scale factors test passed")

    test_config_compression_scenarios()
    print("✓ Config compression scenarios test passed")

    test_config_performance_tuning()
    print("✓ Config performance tuning test passed")

    test_global_config_instance()
    print("✓ Global config instance test passed")

    test_config_validation()
    print("✓ Config validation test passed")

    print("\n🎉 All config tests passed!")
