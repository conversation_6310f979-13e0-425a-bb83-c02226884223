import sys
import time
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from basic_checker_hardcoded.timer import IntervalTimer


def test_interval_timer_initialization():
    """Test IntervalTimer initializes with correct interval."""
    timer = IntervalTimer(5.0)
    assert timer.interval_seconds == 5.0
    print("✓ Timer initialization test passed")


def test_interval_timer_precise_timing():
    """Test IntervalTimer maintains precise intervals."""
    timer = IntervalTimer(1.0)  # 1 second for fast testing

    start = time.time()
    timer.start_cycle()

    # Simulate 0.3 seconds of processing
    time.sleep(0.3)

    # Should sleep 0.7 seconds to reach 1.0 total
    timer.wait_for_next()

    elapsed = time.time() - start

    # Should be close to 1.0 second (allow 50ms tolerance)
    assert 0.95 <= elapsed <= 1.05, f"Expected ~1.0s, got {elapsed}s"
    print(f"✓ Precise timing test passed - elapsed: {elapsed:.3f}s")


def test_interval_timer_overrun_handling():
    """Test IntervalTimer handles processing overruns gracefully."""
    timer = IntervalTimer(0.5)  # 0.5 second interval

    timer.start_cycle()

    # Simulate 0.8 seconds of processing (overrun)
    time.sleep(0.8)

    # Should not sleep (negative remaining time)
    start_wait = time.time()
    timer.wait_for_next()
    wait_time = time.time() - start_wait

    # Should sleep minimal time (not negative)
    assert wait_time < 0.1, f"Expected minimal wait, got {wait_time}s"
    print(f"✓ Overrun handling test passed - wait time: {wait_time:.3f}s")


def test_get_actual_interval():
    """Test get_actual_interval returns correct elapsed time."""
    timer = IntervalTimer(2.0)

    timer.start_cycle()
    time.sleep(0.5)

    actual = timer.get_actual_interval()
    assert 0.45 <= actual <= 0.55, f"Expected ~0.5s, got {actual}s"
    print(f"✓ Actual interval test passed - actual: {actual:.3f}s")


if __name__ == "__main__":
    test_interval_timer_initialization()
    test_interval_timer_precise_timing()
    test_interval_timer_overrun_handling()
    test_get_actual_interval()
    print("\nAll timer tests passed!")
