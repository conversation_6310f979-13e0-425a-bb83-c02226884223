import sys
from pathlib import Path
from unittest.mock import patch

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from basic_checker_hardcoded.main import (
    format_summary_stats,
    get_duration_from_user,
    run_feisty_monitor,
)


def test_get_duration_from_user():
    """Test duration input parsing."""
    with patch("builtins.input", return_value="5"):
        duration = get_duration_from_user()
        assert duration == 5.0
        print("✓ Duration input test passed")


def test_get_duration_from_user_invalid():
    """Test duration input with invalid input."""
    with patch("builtins.input", side_effect=["invalid", "3"]):
        duration = get_duration_from_user()
        assert duration == 3.0
        print("✓ Invalid duration input handling test passed")


def test_format_summary_stats():
    """Test summary statistics formatting."""
    stats = {
        "total_cycles": 12,
        "llm_calls": 4,
        "api_calls_saved": 8,
        "elapsed_minutes": 2.5,
    }

    summary = format_summary_stats(stats)

    assert "Total cycles: 12" in summary
    assert "LLM calls: 4" in summary
    assert "API calls saved: 8" in summary
    assert "2.5 minutes" in summary
    assert "67%" in summary  # 8/(4+8) = 67%
    print("✓ Summary stats formatting test passed")


def test_run_feisty_monitor_structure():
    """Test monitor runs with proper structure and tracking."""
    # Mock all the heavy dependencies
    with (
        patch(
            "basic_checker_hardcoded.main.capture_active_window_screenshot"
        ) as mock_screenshot,
        patch("basic_checker_hardcoded.main.calculate_perceptual_hash") as mock_hash,
        patch("basic_checker_hardcoded.main.has_significant_change") as mock_change,
        patch("basic_checker_hardcoded.main.analyze_distraction") as mock_analyze,
        patch("basic_checker_hardcoded.main.generate_kind_voice_message") as mock_voice,
        patch("basic_checker_hardcoded.main.speak") as mock_speak,
        patch("time.sleep") as mock_sleep,
    ):
        # Setup mocks
        mock_screenshot.return_value = {"compressed_path": "/fake/path.png"}
        mock_hash.return_value = "hash123"
        mock_change.return_value = True  # Always significant change for testing
        mock_analyze.return_value = {
            "distracted_decision": True,
            "reasoning": "Using social media",
        }
        mock_voice.return_value = "Get back to feisty coding!"

        # Run for 0.1 minutes (6 seconds, should get 1-2 cycles)
        stats = run_feisty_monitor(duration_minutes=0.1)

        # Verify structure
        assert "total_cycles" in stats
        assert "llm_calls" in stats
        assert "api_calls_saved" in stats
        assert "elapsed_minutes" in stats

        # Should have made some calls
        assert stats["total_cycles"] >= 1
        assert stats["llm_calls"] >= 1

        print("✓ Monitor structure and tracking test passed")


def test_run_feisty_monitor_change_detection():
    """Test that change detection properly saves API calls."""
    with (
        patch(
            "basic_checker_hardcoded.main.capture_active_window_screenshot"
        ) as mock_screenshot,
        patch("basic_checker_hardcoded.main.calculate_perceptual_hash") as mock_hash,
        patch("basic_checker_hardcoded.main.has_significant_change") as mock_change,
        patch("basic_checker_hardcoded.main.analyze_distraction") as mock_analyze,
        patch("time.sleep") as mock_sleep,
    ):
        # Setup mocks - no significant change to test skipping
        mock_screenshot.return_value = {"compressed_path": "/fake/path.png"}
        mock_hash.return_value = "same_hash"
        mock_change.return_value = False  # No significant change

        # Run for very short duration
        stats = run_feisty_monitor(duration_minutes=0.05)

        # Should have skipped LLM calls due to no change
        assert stats["api_calls_saved"] >= 0

        # analyze_distraction should not be called when no change detected
        mock_analyze.assert_not_called()

        print("✓ Change detection API saving test passed")


if __name__ == "__main__":
    test_get_duration_from_user()
    test_get_duration_from_user_invalid()
    test_format_summary_stats()
    test_run_feisty_monitor_structure()
    test_run_feisty_monitor_change_detection()
    print("\nAll main integration tests passed!")
