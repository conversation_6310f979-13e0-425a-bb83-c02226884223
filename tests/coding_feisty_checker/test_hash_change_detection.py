import sys
import tempfile
from pathlib import Path

from PIL import Image, ImageDraw

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "src"))

from basic_checker_hardcoded.hash_change_detection import (
    calculate_perceptual_hash,
    get_change_percentage,
    has_significant_change,
)


def create_test_image(width=100, height=100, color=(255, 255, 255), text=""):
    """Create a test image with optional text."""
    image = Image.new("RGB", (width, height), color)
    if text:
        draw = ImageDraw.Draw(image)
        draw.text((10, 10), text, fill=(0, 0, 0))
    return image


def test_calculate_perceptual_hash():
    """Test perceptual hash calculation."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test image
        image = create_test_image(color=(255, 0, 0))
        image_path = Path(temp_dir) / "test.png"
        image.save(image_path)

        # Calculate hash
        hash_value = calculate_perceptual_hash(str(image_path))

        assert isinstance(hash_value, str), "Hash should be string"
        assert len(hash_value) > 0, "Hash should not be empty"
        print(f"✓ Hash calculation test passed - hash: {hash_value}")


def test_identical_images_no_change():
    """Test identical images return no significant change."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create identical images
        image1 = create_test_image(color=(0, 255, 0))
        image2 = create_test_image(color=(0, 255, 0))

        path1 = Path(temp_dir) / "image1.png"
        path2 = Path(temp_dir) / "image2.png"

        image1.save(path1)
        image2.save(path2)

        hash1 = calculate_perceptual_hash(str(path1))
        hash2 = calculate_perceptual_hash(str(path2))

        has_change = has_significant_change(hash1, hash2)
        change_pct = get_change_percentage(hash1, hash2)

        assert not has_change, "Identical images should not show significant change"
        assert change_pct == 0.0, "Identical images should show 0% change"
        print(f"✓ Identical images test passed - change: {change_pct}%")


def test_different_images_show_change():
    """Test different images return significant change."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create very different images
        image1 = create_test_image(width=200, height=200, color=(255, 255, 255))
        image2 = Image.new("RGB", (200, 200), (0, 0, 0))

        # Add different patterns to ensure significant difference
        draw1 = ImageDraw.Draw(image1)
        draw2 = ImageDraw.Draw(image2)

        draw1.rectangle([50, 50, 150, 150], fill=(0, 0, 0))
        draw2.rectangle([25, 25, 175, 175], fill=(255, 255, 255))

        path1 = Path(temp_dir) / "image1.png"
        path2 = Path(temp_dir) / "image2.png"

        image1.save(path1)
        image2.save(path2)

        hash1 = calculate_perceptual_hash(str(path1))
        hash2 = calculate_perceptual_hash(str(path2))

        has_change = has_significant_change(hash1, hash2)
        change_pct = get_change_percentage(hash1, hash2)

        assert has_change, "Different images should show significant change"
        assert change_pct > 10.0, "Different images should show >10% change"
        print(f"✓ Different images test passed - change: {change_pct}%")


def test_minor_change_threshold():
    """Test minor changes below threshold are ignored."""
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create nearly identical images (1 pixel difference)
        image1 = create_test_image(color=(255, 255, 255))
        image2 = create_test_image(color=(255, 255, 255))

        # Add tiny difference
        pixels2 = image2.load()
        pixels2[50, 50] = (254, 255, 255)  # Very minor change

        path1 = Path(temp_dir) / "image1.png"
        path2 = Path(temp_dir) / "image2.png"

        image1.save(path1)
        image2.save(path2)

        hash1 = calculate_perceptual_hash(str(path1))
        hash2 = calculate_perceptual_hash(str(path2))

        has_change = has_significant_change(hash1, hash2, threshold=4)
        change_pct = get_change_percentage(hash1, hash2)

        # Minor changes should not trigger threshold
        assert not has_change, (
            f"Minor change should not exceed threshold - change: {change_pct}%"
        )
        print(f"✓ Minor change threshold test passed - change: {change_pct}%")


if __name__ == "__main__":
    test_calculate_perceptual_hash()
    test_identical_images_no_change()
    test_different_images_show_change()
    test_minor_change_threshold()
    print("\nAll hash change threshold tests passed!")
