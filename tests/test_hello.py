"""Tests for hello world utility function."""

import pytest
from src.utils.hello import hello_world


def test_hello_world():
    """Test that hello_world returns the correct greeting."""
    result = hello_world()
    assert result == 'Hello, World!'


def test_hello_world_return_type():
    """Test that hello_world returns a string."""
    result = hello_world()
    assert isinstance(result, str)


def test_hello_world_not_empty():
    """Test that hello_world returns a non-empty string."""
    result = hello_world()
    assert len(result) > 0