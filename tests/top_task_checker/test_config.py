#!/usr/bin/env python3
"""
Tests for configuration system
"""

import unittest
import os
from unittest.mock import patch
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'src' / 'top_task_checker'))

from config import TaskFocusConfig, CONFIG, load_config_from_env

class TestTaskFocusConfig(unittest.TestCase):
    """Test configuration functionality"""
    
    def test_default_config_values(self):
        """if default config values don't match expected then broken"""
        config = TaskFocusConfig()
        
        # Test key defaults
        self.assertEqual(config.TASK_REFRESH_INTERVAL, 30)
        self.assertEqual(config.SCREENSHOT_INTERVAL, 2.0)
        self.assertTrue(config.VOICE_ALERTS_ENABLED)
        self.assertEqual(config.LLM_MODEL, "gpt-4o")
        self.assertEqual(config.ALERT_COOLDOWN_SECONDS, 300)
        
        print("✓ Default configuration values correct")
    
    def test_config_validation(self):
        """if config validation doesn't catch invalid values then broken"""
        
        # Test invalid refresh interval
        with self.assertRaises(ValueError):
            TaskFocusConfig(TASK_REFRESH_INTERVAL=2)  # Too low
        
        # Test invalid screenshot interval  
        with self.assertRaises(ValueError):
            TaskFocusConfig(SCREENSHOT_INTERVAL=0.1)  # Too low
        
        # Test invalid thresholds
        with self.assertRaises(ValueError):
            TaskFocusConfig(HASH_CHANGE_THRESHOLD=1.5)  # Too high
            
        with self.assertRaises(ValueError):
            TaskFocusConfig(FOCUS_CONFIDENCE_THRESHOLD=-0.1)  # Too low
        
        print("✓ Configuration validation works")
    
    def test_performance_mode(self):
        """if performance mode config doesn't optimize correctly then broken"""
        config = TaskFocusConfig.create_performance_mode()
        
        # Performance optimizations
        self.assertEqual(config.SCREENSHOT_INTERVAL, 3.0)  # Less frequent
        self.assertFalse(config.VOICE_ALERTS_ENABLED)      # No voice alerts
        self.assertTrue(config.SCREENSHOT_COMPRESS)         # Compression enabled
        self.assertFalse(config.KEEP_ORIGINAL_AFTER_COMPRESSION)  # Space saving
        self.assertEqual(config.TASK_REFRESH_INTERVAL, 60) # Less frequent task checks
        
        print("✓ Performance mode configuration correct")
    
    def test_focus_mode(self):
        """if focus mode config doesn't enhance sensitivity then broken"""
        config = TaskFocusConfig.create_focus_mode()
        
        # Focus enhancements
        self.assertEqual(config.SCREENSHOT_INTERVAL, 1.0)   # More sensitive
        self.assertEqual(config.HASH_CHANGE_THRESHOLD, 0.1) # Smaller changes
        self.assertTrue(config.VOICE_ALERTS_ENABLED)        # Voice alerts on
        self.assertTrue(config.ESCALATING_ALERTS)           # Escalating alerts
        self.assertEqual(config.ALERT_COOLDOWN_SECONDS, 180) # Shorter cooldown
        self.assertEqual(config.TASK_REFRESH_INTERVAL, 15)  # More frequent updates
        
        print("✓ Focus mode configuration correct")
    
    def test_space_saving_mode(self):
        """if space saving mode doesn't optimize storage then broken"""
        config = TaskFocusConfig.create_space_saving_mode()
        
        # Storage optimizations
        self.assertTrue(config.SCREENSHOT_COMPRESS)
        self.assertFalse(config.KEEP_ORIGINAL_AFTER_COMPRESSION)
        self.assertTrue(config.LLM_ANALYZE_COMPRESSED)
        self.assertTrue(config.HASH_DETECTION_ON_COMPRESSED)
        self.assertTrue(config.CLEANUP_TEMP_FILES)
        self.assertEqual(config.MAX_SCREENSHOT_AGE_HOURS, 4)
        self.assertEqual(config.SCREENSHOT_RESOLUTION, "quarter")
        
        print("✓ Space saving mode configuration correct")
    
    def test_environment_variable_loading(self):
        """if environment variable loading doesn't work then broken"""
        # Test with mock environment variables
        env_vars = {
            'FOCUS_TASK_REFRESH_INTERVAL': '45',
            'FOCUS_SCREENSHOT_INTERVAL': '1.5',
            'FOCUS_VOICE_ALERTS': 'false',
            'FOCUS_DEBUG_MODE': 'true'
        }
        
        with patch.dict(os.environ, env_vars):
            config = load_config_from_env()
            
            self.assertEqual(config.TASK_REFRESH_INTERVAL, 45)
            self.assertEqual(config.SCREENSHOT_INTERVAL, 1.5)
            self.assertFalse(config.VOICE_ALERTS_ENABLED)
            self.assertTrue(config.DEBUG_MODE)
        
        print("✓ Environment variable loading works")
    
    def test_global_config_instance(self):
        """if global CONFIG instance doesn't exist then broken"""
        # Test that CONFIG is available and valid
        self.assertIsInstance(CONFIG, TaskFocusConfig)
        self.assertEqual(CONFIG.TASK_REFRESH_INTERVAL, 30)
        
        print("✓ Global CONFIG instance available")
    
    def test_config_serialization(self):
        """if config can't be converted to dict then broken"""
        config = TaskFocusConfig()
        
        # Should be able to access as dict-like
        self.assertEqual(config.TASK_REFRESH_INTERVAL, 30)
        self.assertTrue(hasattr(config, 'VOICE_ALERTS_ENABLED'))
        
        # Test string representation works
        config_str = str(config)
        self.assertIn('TaskFocusConfig', config_str)
        
        print("✓ Configuration serialization works")

class TestConfigModes(unittest.TestCase):
    """Test different configuration modes"""
    
    def test_all_modes_valid(self):
        """if any configuration mode is invalid then broken"""
        modes = [
            TaskFocusConfig(),  # default
            TaskFocusConfig.create_performance_mode(),
            TaskFocusConfig.create_focus_mode(),
            TaskFocusConfig.create_space_saving_mode()
        ]
        
        for i, config in enumerate(modes):
            # Each config should pass validation
            self.assertIsInstance(config, TaskFocusConfig)
            
            # Basic sanity checks
            self.assertGreaterEqual(config.TASK_REFRESH_INTERVAL, 5)
            self.assertGreaterEqual(config.SCREENSHOT_INTERVAL, 0.5)
            self.assertGreaterEqual(config.HASH_CHANGE_THRESHOLD, 0.0)
            self.assertLessEqual(config.HASH_CHANGE_THRESHOLD, 1.0)
        
        print("✓ All configuration modes are valid")
    
    def test_mode_differences(self):
        """if configuration modes aren't actually different then broken"""
        default = TaskFocusConfig()
        performance = TaskFocusConfig.create_performance_mode()
        focus = TaskFocusConfig.create_focus_mode()
        
        # Performance mode should be different from default
        self.assertNotEqual(default.SCREENSHOT_INTERVAL, performance.SCREENSHOT_INTERVAL)
        self.assertNotEqual(default.VOICE_ALERTS_ENABLED, performance.VOICE_ALERTS_ENABLED)
        
        # Focus mode should be different from default
        self.assertNotEqual(default.SCREENSHOT_INTERVAL, focus.SCREENSHOT_INTERVAL)
        self.assertNotEqual(default.ALERT_COOLDOWN_SECONDS, focus.ALERT_COOLDOWN_SECONDS)
        
        print("✓ Configuration modes have meaningful differences")

if __name__ == '__main__':
    print("🧪 Testing Task Focus Configuration")
    print("=" * 50)
    
    # Run tests with detailed output
    unittest.main(verbosity=2, exit=False)
    
    print("\n📊 Configuration Test Summary:")
    print("All configuration tests completed successfully!")