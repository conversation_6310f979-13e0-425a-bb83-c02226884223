#!/usr/bin/env python3
"""
Integration tests for top task checker system
"""

import unittest
import time
from unittest.mock import patch, MagicMock, Mock
import sys
from pathlib import Path
import tempfile

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'src' / 'top_task_checker'))

from config import TaskFocusConfig

class TestSystemIntegration(unittest.TestCase):
    """Test integration between system components"""
    
    def setUp(self):
        # Create test config with fast intervals for testing
        self.config = TaskFocusConfig(
            TASK_REFRESH_INTERVAL=1,     # 1 second for testing
            SCREENSHOT_INTERVAL=0.5,     # 0.5 seconds for testing  
            VOICE_ALERTS_ENABLED=False,  # Disable for testing
            DISABLE_SCREENSHOTS=True,    # Disable for testing
            DEBUG_MODE=True
        )
    
    @patch('task_fetcher.subprocess.run')
    def test_task_fetcher_integration(self, mock_run):
        """if task fetcher integration doesn't work then broken"""
        # Mock Things3 script response
        mock_result = MagicMock()
        mock_result.returncode = 0
        mock_result.stdout = "integration test task\n"
        mock_run.return_value = mock_result
        
        from task_fetcher import TaskFetcher
        
        fetcher = TaskFetcher(refresh_interval=1)
        task = fetcher.get_current_task()
        
        self.assertEqual(task, "integration test task")
        
        # Verify script was called with correct arguments
        mock_run.assert_called_with([
            'python', 'src/tasklist/things3_import.py', '--top-task-only'
        ], capture_output=True, text=True, timeout=10, cwd='.')
        
        print("✓ Task fetcher integration works")
    
    def test_config_integration(self):
        """if config integration doesn't work across components then broken"""
        from task_fetcher import TaskFetcher
        
        # Test that components accept config properly
        fetcher = TaskFetcher(refresh_interval=self.config.TASK_REFRESH_INTERVAL)
        
        self.assertEqual(fetcher.refresh_interval, 1)
        
        print("✓ Configuration integration works")
    
    @patch('focus_analyzer.openai.OpenAI')
    def test_focus_analyzer_integration(self, mock_openai):
        """if focus analyzer integration doesn't work then broken"""
        # Mock OpenAI client
        mock_client = MagicMock()
        mock_response = MagicMock()
        mock_choice = MagicMock()
        mock_choice.message.content = '''
        {
            "status": "focused",
            "confidence": 0.9,
            "current_activity": "coding",
            "reasoning": "user is focused on development work",
            "task_alignment": "directly related to coding task"
        }
        '''
        mock_response.choices = [mock_choice]
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        from focus_analyzer import FocusAnalyzer
        
        analyzer = FocusAnalyzer(self.config)
        
        # Create a temporary test image
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            # Create minimal PNG file (just enough to test)
            png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x06\x00\x00\x00\x1f\x15\xc4\x89\x00\x00\x00\nIDATx\x9cc\x00\x01\x00\x00\x05\x00\x01\r\n-\xdb\x00\x00\x00\x00IEND\xaeB`\x82'
            tmp_file.write(png_data)
            tmp_file.flush()
            
            result = analyzer.analyze_focus(Path(tmp_file.name), "test task")
            
            self.assertEqual(result['status'], 'focused')
            self.assertEqual(result['confidence'], 0.9)
            self.assertIn('focused on development work', result['reasoning'])
            
            # Cleanup
            Path(tmp_file.name).unlink()
        
        print("✓ Focus analyzer integration works")
    
    def test_hash_detector_integration(self):
        """if hash detector integration doesn't work then broken"""
        from hash_detector import HashDetector
        
        detector = HashDetector(self.config)
        
        # Test with temporary test images
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp1:
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp2:
                # Create two different minimal PNG files
                png1 = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x02\x00\x00\x00\x01\x08\x06\x00\x00\x00\xe2\xdd\xa0\x9c\x00\x00\x00\x0cIDATx\x9cc\xff\x00\x00\x00\x02\x00\x01\xe5\'\xde\xfc\x00\x00\x00\x00IEND\xaeB`\x82'
                png2 = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x02\x00\x00\x00\x01\x08\x06\x00\x00\x00\xe2\xdd\xa0\x9c\x00\x00\x00\x0cIDATx\x9cc\x00\xff\x00\x00\x02\x00\x01\x84r\x05\x27\x00\x00\x00\x00IEND\xaeB`\x82'
                
                tmp1.write(png1)
                tmp1.flush()
                tmp2.write(png2)
                tmp2.flush()
                
                path1, path2 = Path(tmp1.name), Path(tmp2.name)
                
                # First check should detect change
                change1 = detector.has_screen_changed(path1)
                self.assertTrue(change1)  # First image always detects change
                
                # Same image should not detect change
                change2 = detector.has_screen_changed(path1)
                self.assertFalse(change2)  # Same image, no change
                
                # Different image should detect change
                change3 = detector.has_screen_changed(path2)
                self.assertTrue(change3)  # Different image, change detected
                
                # Cleanup
                path1.unlink()
                path2.unlink()
        
        print("✓ Hash detector integration works")
    
    def test_alerter_integration(self):
        """if alerter integration doesn't work then broken"""
        from alerter import Alerter
        
        # Use config with alerts disabled for testing
        test_config = self.config
        test_config.VOICE_ALERTS_ENABLED = False
        test_config.VISUAL_ALERTS_ENABLED = False
        
        alerter = Alerter(test_config)
        
        # Test that methods don't crash when called
        alerter.announce_monitoring_start("test task")
        alerter.announce_task_change("old task", "new task")
        alerter.send_distraction_alert("focus task", "distraction reason", 1)
        
        # Test alert statistics
        stats = alerter.get_alert_stats()
        self.assertIsInstance(stats, dict)
        self.assertIn('config', stats)
        
        print("✓ Alerter integration works")
    
    def test_component_error_handling(self):
        """if component error handling doesn't work then broken"""
        from task_fetcher import TaskFetcher
        from hash_detector import HashDetector
        
        # Test task fetcher with subprocess error
        with patch('task_fetcher.subprocess.run') as mock_run:
            mock_run.side_effect = Exception("Test error")
            
            fetcher = TaskFetcher(refresh_interval=1)
            task = fetcher.get_current_task()
            
            # Should handle error gracefully
            self.assertIsNone(task)
        
        # Test hash detector with file error
        detector = HashDetector(self.config)
        nonexistent_path = Path("/nonexistent/file.png")
        
        # Should handle missing file gracefully
        change = detector.has_screen_changed(nonexistent_path)
        self.assertTrue(change)  # Should assume change on error
        
        print("✓ Component error handling works")
    
    def test_workflow_integration(self):
        """if complete workflow integration doesn't work then broken"""
        # This tests the basic workflow without actually running the full system
        
        # 1. Config creation
        config = TaskFocusConfig(
            TASK_REFRESH_INTERVAL=30,
            SCREENSHOT_INTERVAL=2.0,
            VOICE_ALERTS_ENABLED=True
        )
        self.assertIsNotNone(config)
        
        # 2. Component initialization (mocked)
        with patch('task_fetcher.subprocess.run') as mock_task:
            with patch('focus_analyzer.openai.OpenAI') as mock_openai:
                
                # Mock task fetcher
                mock_result = MagicMock()
                mock_result.returncode = 0
                mock_result.stdout = "workflow test task\n"
                mock_task.return_value = mock_result
                
                # Mock OpenAI
                mock_client = MagicMock()
                mock_openai.return_value = mock_client
                
                # Import and test component creation
                from task_fetcher import TaskFetcher
                from focus_analyzer import FocusAnalyzer
                from hash_detector import HashDetector
                from alerter import Alerter
                
                # All components should initialize without error
                fetcher = TaskFetcher(config.TASK_REFRESH_INTERVAL)
                analyzer = FocusAnalyzer(config)
                detector = HashDetector(config)
                alerter = Alerter(config)
                
                # Test basic workflow step
                task = fetcher.get_current_task()
                self.assertEqual(task, "workflow test task")
                
                print("✓ Complete workflow integration works")

class TestRealWorldScenarios(unittest.TestCase):
    """Test real-world usage scenarios"""
    
    def setUp(self):
        self.config = TaskFocusConfig(
            TASK_REFRESH_INTERVAL=30,
            SCREENSHOT_INTERVAL=2.0,
            VOICE_ALERTS_ENABLED=False,  # Disabled for testing
            DEBUG_MODE=True
        )
    
    @patch('task_fetcher.subprocess.run')
    def test_task_change_scenario(self, mock_run):
        """if task change scenario doesn't work then broken"""
        from task_fetcher import TaskFetcher
        
        # Simulate task changing over time
        responses = [
            ("first task", 0, "first task\n"),
            ("first task", 0, "first task\n"),  # Same task
            ("second task", 0, "second task\n"),  # Task changed
        ]
        
        def side_effect(*args, **kwargs):
            response_data = responses.pop(0) if responses else ("no task", 1, "")
            result = MagicMock()
            result.returncode = response_data[1]
            result.stdout = response_data[2]
            return result
        
        mock_run.side_effect = side_effect
        
        fetcher = TaskFetcher(refresh_interval=0.1)  # Very short for testing
        
        # Track task changes
        changes = []
        def track_changes(old, new):
            changes.append((old, new))
        
        fetcher.set_task_change_callback(track_changes)
        
        # Simulate time progression
        task1 = fetcher.get_current_task()          # "first task"
        fetcher.last_fetch_time = 0                 # Force refresh
        task2 = fetcher.get_current_task()          # "first task" (no change)
        fetcher.last_fetch_time = 0                 # Force refresh
        task3 = fetcher.get_current_task()          # "second task" (changed)
        
        # Verify progression
        self.assertEqual(task1, "first task")
        self.assertEqual(task2, "first task") 
        self.assertEqual(task3, "second task")
        
        # Verify change callback was called
        self.assertEqual(len(changes), 1)
        self.assertEqual(changes[0], ("first task", "second task"))
        
        print("✓ Task change scenario works")

if __name__ == '__main__':
    print("🧪 Testing Top Task Checker Integration")
    print("=" * 50)
    
    # Run integration tests
    unittest.main(verbosity=2, exit=False)
    
    print("\n📊 Integration Test Summary:")
    print("All integration tests completed successfully!")