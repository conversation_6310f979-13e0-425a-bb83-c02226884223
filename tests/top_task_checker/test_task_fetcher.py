#!/usr/bin/env python3
"""
Tests for task fetcher functionality
"""

import unittest
import time
from unittest.mock import patch, MagicMock
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / 'src' / 'top_task_checker'))

from task_fetcher import TaskFetcher, get_top_task

class TestTaskFetcher(unittest.TestCase):
    """Test task fetching functionality"""
    
    def setUp(self):
        self.fetcher = TaskFetcher(refresh_interval=1)  # 1 second for testing
    
    def test_initial_task_fetch(self):
        """if initial task fetch doesn't work then broken"""
        with patch('subprocess.run') as mock_run:
            # Mock successful Things3 script call
            mock_result = MagicMock()
            mock_result.returncode = 0
            mock_result.stdout = "test task from things3\n"
            mock_run.return_value = mock_result
            
            task = self.fetcher.get_current_task()
            
            self.assertEqual(task, "test task from things3")
            self.assertIsNotNone(self.fetcher.current_task)
            mock_run.assert_called_once()
    
    def test_task_caching_within_interval(self):
        """if task caching doesn't prevent unnecessary calls then broken"""
        with patch('subprocess.run') as mock_run:
            mock_result = MagicMock()
            mock_result.returncode = 0
            mock_result.stdout = "cached task\n"
            mock_run.return_value = mock_result
            
            # First call should fetch
            task1 = self.fetcher.get_current_task()
            
            # Second call within interval should use cache
            task2 = self.fetcher.get_current_task()
            
            self.assertEqual(task1, task2)
            self.assertEqual(mock_run.call_count, 1)  # Only one API call
    
    def test_refresh_after_interval(self):
        """if refresh doesn't happen after interval then broken"""
        with patch('subprocess.run') as mock_run:
            # First call
            mock_result1 = MagicMock()
            mock_result1.returncode = 0
            mock_result1.stdout = "first task\n"
            
            # Second call (after interval)
            mock_result2 = MagicMock()
            mock_result2.returncode = 0
            mock_result2.stdout = "second task\n"
            
            mock_run.side_effect = [mock_result1, mock_result2]
            
            # First call
            task1 = self.fetcher.get_current_task()
            
            # Simulate time passing
            self.fetcher.last_fetch_time = time.time() - 2  # Force refresh
            
            # Second call should refresh
            task2 = self.fetcher.get_current_task()
            
            self.assertEqual(task1, "first task")
            self.assertEqual(task2, "second task")
            self.assertEqual(mock_run.call_count, 2)
    
    def test_task_change_callback(self):
        """if task change callback doesn't fire then broken"""
        callback_called = []
        
        def test_callback(old_task, new_task):
            callback_called.append((old_task, new_task))
        
        self.fetcher.set_task_change_callback(test_callback)
        
        with patch('subprocess.run') as mock_run:
            # First call
            mock_result1 = MagicMock()
            mock_result1.returncode = 0
            mock_result1.stdout = "task one\n"
            
            # Second call with different task
            mock_result2 = MagicMock()
            mock_result2.returncode = 0
            mock_result2.stdout = "task two\n"
            
            mock_run.side_effect = [mock_result1, mock_result2]
            
            # First fetch
            self.fetcher.get_current_task()
            
            # Force refresh with new task
            self.fetcher.last_fetch_time = 0
            self.fetcher.get_current_task()
            
            self.assertEqual(len(callback_called), 1)
            self.assertEqual(callback_called[0], ("task one", "task two"))
    
    def test_error_handling(self):
        """if error handling doesn't preserve previous task then broken"""
        with patch('subprocess.run') as mock_run:
            # First successful call
            mock_result1 = MagicMock()
            mock_result1.returncode = 0
            mock_result1.stdout = "good task\n"
            mock_run.return_value = mock_result1
            
            task1 = self.fetcher.get_current_task()
            self.assertEqual(task1, "good task")
            
            # Second call fails
            mock_result2 = MagicMock()
            mock_result2.returncode = 1
            mock_result2.stderr = "Things3 error"
            mock_run.return_value = mock_result2
            
            # Force refresh
            self.fetcher.last_fetch_time = 0
            task2 = self.fetcher.get_current_task()
            
            # Should keep previous task on error
            self.assertEqual(task2, "good task")
    
    def test_timeout_handling(self):
        """if timeout handling doesn't work then broken"""
        with patch('subprocess.run') as mock_run:
            # First successful call
            mock_result1 = MagicMock()
            mock_result1.returncode = 0
            mock_result1.stdout = "timeout test task\n"
            mock_run.return_value = mock_result1
            
            self.fetcher.get_current_task()
            
            # Second call times out
            from subprocess import TimeoutExpired
            mock_run.side_effect = TimeoutExpired('cmd', 10)
            
            # Force refresh
            self.fetcher.last_fetch_time = 0
            task = self.fetcher.get_current_task()
            
            # Should keep previous task on timeout
            self.assertEqual(task, "timeout test task")
    
    def test_force_refresh(self):
        """if force refresh doesn't bypass interval then broken"""
        with patch('subprocess.run') as mock_run:
            mock_result = MagicMock()
            mock_result.returncode = 0
            mock_result.stdout = "forced refresh task\n"
            mock_run.return_value = mock_result
            
            # Get initial task
            self.fetcher.get_current_task()
            
            # Force refresh should bypass interval
            task = self.fetcher.force_refresh()
            
            self.assertEqual(task, "forced refresh task")
            self.assertEqual(mock_run.call_count, 2)
    
    def test_task_age_calculation(self):
        """if task age calculation doesn't work then broken"""
        # Before any fetch
        self.assertEqual(self.fetcher.get_task_age(), float('inf'))
        
        with patch('subprocess.run') as mock_run:
            mock_result = MagicMock()
            mock_result.returncode = 0
            mock_result.stdout = "age test task\n"
            mock_run.return_value = mock_result
            
            # Fetch task
            self.fetcher.get_current_task()
            
            # Age should be very small (just fetched)
            age = self.fetcher.get_task_age()
            self.assertLess(age, 1.0)
            self.assertGreaterEqual(age, 0.0)
    
    def test_stale_task_detection(self):
        """if stale task detection doesn't work then broken"""
        # Initially not stale (no data)
        self.assertFalse(self.fetcher.is_task_stale())
        
        with patch('subprocess.run') as mock_run:
            mock_result = MagicMock()
            mock_result.returncode = 0
            mock_result.stdout = "stale test task\n"
            mock_run.return_value = mock_result
            
            # Fetch task
            self.fetcher.get_current_task()
            
            # Fresh task should not be stale
            self.assertFalse(self.fetcher.is_task_stale())
            
            # Simulate old data
            self.fetcher.last_fetch_time = time.time() - (self.fetcher.refresh_interval * 3)
            
            # Should now be stale
            self.assertTrue(self.fetcher.is_task_stale())

class TestConvenienceFunction(unittest.TestCase):
    """Test convenience functions"""
    
    def test_get_top_task_function(self):
        """if get_top_task convenience function doesn't work then broken"""
        with patch('subprocess.run') as mock_run:
            mock_result = MagicMock()
            mock_result.returncode = 0
            mock_result.stdout = "convenience function task\n"
            mock_run.return_value = mock_result
            
            task = get_top_task()
            
            self.assertEqual(task, "convenience function task")
            mock_run.assert_called_once()

if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)