import unittest
import os
import numpy as np
from PIL import Image
from img_compression.sharpening import sharpen_image


class TestSharpening(unittest.TestCase):
    
    def setUp(self):
        self.test_image_path = "src/img_compression/data/test_input.png"
        self.output_dir = "src/img_compression/data/output_ss"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def test_sharpen_image_creates_output_file(self):
        """if sharpen_image doesn't create output file then broken"""
        output_path = os.path.join(self.output_dir, "sharpened_test.png")
        if os.path.exists(output_path):
            os.remove(output_path)
            
        result_path = sharpen_image(self.test_image_path, output_path)
        
        self.assertTrue(os.path.exists(result_path))
        self.assertEqual(result_path, output_path)
        
    def test_sharpening_enhances_edges(self):
        """if sharpening doesn't enhance edges then broken"""
        output_path = os.path.join(self.output_dir, "sharpened_edges_test.png")
        
        result_path = sharpen_image(self.test_image_path, output_path)
        
        original = np.array(Image.open(self.test_image_path))
        sharpened = np.array(Image.open(result_path))
        
        # Calculate edge strength using Laplacian
        from scipy.ndimage import laplace
        
        if len(original.shape) == 3:  # RGB image
            original_gray = np.mean(original, axis=2)
            sharpened_gray = np.mean(sharpened, axis=2)
        else:
            original_gray = original
            sharpened_gray = sharpened
            
        original_edges = np.abs(laplace(original_gray.astype(float)))
        sharpened_edges = np.abs(laplace(sharpened_gray.astype(float)))
        
        # Sharpened image should have stronger edges on average
        original_edge_strength = np.mean(original_edges)
        sharpened_edge_strength = np.mean(sharpened_edges)
        
        self.assertGreaterEqual(sharpened_edge_strength, original_edge_strength)
        
    def test_sharpening_preserves_image_dimensions(self):
        """if sharpening doesn't preserve image dimensions then broken"""
        output_path = os.path.join(self.output_dir, "sharpened_dims_test.png")
        
        result_path = sharpen_image(self.test_image_path, output_path)
        
        original = Image.open(self.test_image_path)
        sharpened = Image.open(result_path)
        
        self.assertEqual(original.size, sharpened.size)
        
    def test_sharpening_improves_text_clarity(self):
        """if sharpening doesn't improve text clarity then broken"""
        output_path = os.path.join(self.output_dir, "sharpened_text_test.png")
        
        result_path = sharpen_image(self.test_image_path, output_path)
        
        original = np.array(Image.open(self.test_image_path))
        sharpened = np.array(Image.open(result_path))
        
        # Sharpened image should be different from original
        self.assertFalse(np.array_equal(original, sharpened))
        
        # Images should have same shape
        self.assertEqual(original.shape, sharpened.shape)


if __name__ == '__main__':
    unittest.main()