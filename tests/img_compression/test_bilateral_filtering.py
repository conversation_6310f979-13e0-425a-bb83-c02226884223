import unittest
import os
import numpy as np
from PIL import Image
from img_compression.bilateral_filtering import bilateral_filter_image


class TestBilateralFiltering(unittest.TestCase):
    
    def setUp(self):
        self.test_image_path = "src/img_compression/data/test_input.png"
        self.output_dir = "src/img_compression/data/output_ss"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def test_bilateral_filter_creates_output_file(self):
        """if bilateral_filter_image doesn't create output file then broken"""
        output_path = os.path.join(self.output_dir, "bilateral_test.png")
        if os.path.exists(output_path):
            os.remove(output_path)
            
        result_path = bilateral_filter_image(self.test_image_path, output_path)
        
        self.assertTrue(os.path.exists(result_path))
        self.assertEqual(result_path, output_path)
        
    def test_bilateral_filter_preserves_edges(self):
        """if bilateral filter doesn't preserve edges better than gaussian then broken"""
        output_path = os.path.join(self.output_dir, "bilateral_edges_test.png")
        
        result_path = bilateral_filter_image(self.test_image_path, output_path)
        
        # Load original and filtered images
        original = np.array(Image.open(self.test_image_path))
        filtered = np.array(Image.open(result_path))
        
        # Images should have same dimensions
        self.assertEqual(original.shape, filtered.shape)
        
        # Filtered image should be different from original (noise removed)
        self.assertFalse(np.array_equal(original, filtered))
        
    def test_bilateral_filter_reduces_noise(self):
        """if bilateral filter doesn't reduce noise then broken"""
        output_path = os.path.join(self.output_dir, "bilateral_noise_test.png")
        
        result_path = bilateral_filter_image(self.test_image_path, output_path)
        
        original = np.array(Image.open(self.test_image_path))
        filtered = np.array(Image.open(result_path))
        
        # Calculate variance as a measure of noise
        original_variance = np.var(original)
        filtered_variance = np.var(filtered)
        
        # Filtered image should have lower variance (less noise)
        self.assertLessEqual(filtered_variance, original_variance)


if __name__ == '__main__':
    unittest.main()