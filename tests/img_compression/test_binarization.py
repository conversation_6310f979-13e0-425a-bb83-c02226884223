import unittest
import os
import numpy as np
from PIL import Image
from img_compression.binarization import binarize_image


class TestBinarization(unittest.TestCase):
    
    def setUp(self):
        self.test_image_path = "src/img_compression/data/test_input.png"
        self.output_dir = "src/img_compression/data/output_ss"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def test_binarize_image_creates_output_file(self):
        """if binarize_image doesn't create output file then broken"""
        output_path = os.path.join(self.output_dir, "binarized_test.png")
        if os.path.exists(output_path):
            os.remove(output_path)
            
        result_path = binarize_image(self.test_image_path, output_path)
        
        self.assertTrue(os.path.exists(result_path))
        self.assertEqual(result_path, output_path)
        
    def test_binarized_image_is_black_and_white(self):
        """if binarized image isn't purely black/white then broken"""
        output_path = os.path.join(self.output_dir, "binarized_bw_test.png")
        
        result_path = binarize_image(self.test_image_path, output_path)
        
        img = Image.open(result_path)
        img_array = np.array(img)
        
        # Check that image only contains values 0 and 255 (black and white)
        unique_values = np.unique(img_array)
        self.assertTrue(len(unique_values) <= 2)
        self.assertTrue(all(val in [0, 255] for val in unique_values))
        
    def test_binarized_image_smaller_file_size(self):
        """if binarized image isn't smaller file size then broken"""
        output_path = os.path.join(self.output_dir, "binarized_size_test.png")
        
        original_size = os.path.getsize(self.test_image_path)
        result_path = binarize_image(self.test_image_path, output_path)
        compressed_size = os.path.getsize(result_path)
        
        self.assertLess(compressed_size, original_size)


if __name__ == '__main__':
    unittest.main()