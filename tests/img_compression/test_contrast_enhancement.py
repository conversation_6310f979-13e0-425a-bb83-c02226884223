import unittest
import os
import numpy as np
from PIL import Image
from img_compression.contrast_enhancement import enhance_contrast


class TestContrastEnhancement(unittest.TestCase):
    
    def setUp(self):
        self.test_image_path = "src/img_compression/data/test_input.png"
        self.output_dir = "src/img_compression/data/output_ss"
        os.makedirs(self.output_dir, exist_ok=True)
        
    def test_enhance_contrast_creates_output_file(self):
        """if enhance_contrast doesn't create output file then broken"""
        output_path = os.path.join(self.output_dir, "contrast_test.png")
        if os.path.exists(output_path):
            os.remove(output_path)
            
        result_path = enhance_contrast(self.test_image_path, output_path)
        
        self.assertTrue(os.path.exists(result_path))
        self.assertEqual(result_path, output_path)
        
    def test_contrast_enhancement_increases_dynamic_range(self):
        """if contrast enhancement doesn't increase dynamic range then broken"""
        output_path = os.path.join(self.output_dir, "contrast_range_test.png")
        
        result_path = enhance_contrast(self.test_image_path, output_path)
        
        original = np.array(Image.open(self.test_image_path))
        enhanced = np.array(Image.open(result_path))
        
        # Calculate dynamic range (max - min values)
        original_range = np.max(original) - np.min(original)
        enhanced_range = np.max(enhanced) - np.min(enhanced)
        
        # Enhanced image should have equal or greater dynamic range
        self.assertGreaterEqual(enhanced_range, original_range)
        
    def test_contrast_enhancement_improves_text_visibility(self):
        """if contrast enhancement doesn't improve text visibility then broken"""
        output_path = os.path.join(self.output_dir, "contrast_text_test.png")
        
        result_path = enhance_contrast(self.test_image_path, output_path)
        
        original = np.array(Image.open(self.test_image_path))
        enhanced = np.array(Image.open(result_path))
        
        # Images should have same dimensions
        self.assertEqual(original.shape, enhanced.shape)
        
        # Enhanced image should be different from original
        self.assertFalse(np.array_equal(original, enhanced))
        
    def test_contrast_enhancement_preserves_image_structure(self):
        """if contrast enhancement doesn't preserve image structure then broken"""
        output_path = os.path.join(self.output_dir, "contrast_structure_test.png")
        
        result_path = enhance_contrast(self.test_image_path, output_path)
        
        original = np.array(Image.open(self.test_image_path))
        enhanced = np.array(Image.open(result_path))
        
        # Check that the correlation between original and enhanced is high
        correlation = np.corrcoef(original.flatten(), enhanced.flatten())[0, 1]
        self.assertGreater(correlation, 0.8)  # Strong positive correlation


if __name__ == '__main__':
    unittest.main()