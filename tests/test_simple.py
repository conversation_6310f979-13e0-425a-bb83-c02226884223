#!/usr/bin/env python3
"""Simple test to verify things3_import works"""

import subprocess
import sys
import json

def test_script_execution():
    """if script doesn't execute then broken"""
    try:
        # Test help flag
        result = subprocess.run([sys.executable, 'src/tasklist/things3_import.py', '--help'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("❌ Help command failed")
            return False
        print("✅ Help command works")
        
        # Test that we can import the module
        result = subprocess.run([sys.executable, '-c', 
                               'import sys; sys.path.insert(0, "src/tasklist"); from things3_import import main; print("Import works")'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print(f"❌ Import failed: {result.stderr}")
            return False
        print("✅ Module import works")
        
        return True
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

def test_mock_behavior():
    """if mocked behavior doesn't work then broken"""
    try:
        # Simple mock test
        code = '''
import sys
sys.path.insert(0, "src/tasklist")
from unittest.mock import patch
from things3_import import main

with patch("sys.argv", ["test"]):
    with patch("subprocess.check_output", return_value=b"id1\\n---\\ntask1"):
        with patch("builtins.print") as mock_print:
            main()
            if mock_print.called:
                print("✅ Mock test passed")
            else:
                print("❌ Mock test failed - print not called")
'''
        result = subprocess.run([sys.executable, '-c', code], 
                              capture_output=True, text=True, timeout=10)
        
        if "✅ Mock test passed" in result.stdout:
            print("✅ Mock behavior works")
            return True
        else:
            print(f"❌ Mock behavior failed: {result.stdout} {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Mock test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Simple Things3 Import Tests")
    print("="*40)
    
    success = True
    success &= test_script_execution()
    success &= test_mock_behavior()
    
    if success:
        print("\n🎉 All simple tests passed!")
    else:
        print("\n❌ Some tests failed")
        sys.exit(1)