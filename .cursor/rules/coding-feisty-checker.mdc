---
description: Coding feisty checker system architecture and development patterns
---

# Coding Feisty Checker System

## Core Architecture

The coding feisty checker is a distraction detection system that monitors user activity through screenshots and LLM analysis.

### Main Entry Point
- [src/basic_checker_hardcoded/main.py](mdc:src/basic_checker_hardcoded/main.py) - Main monitoring system with config-driven behavior

### Key Components
- **Config System**: [MonitorConfig](mdc:src/basic_checker_hardcoded/main.py) class with toggles for all system behavior
- **Screenshot Capture**: [src/will_detector/screenshot.py](mdc:src/will_detector/screenshot.py) - Handles screenshot capture with compression
- **Distraction Analysis**: [src/basic_checker_hardcoded/detector.py](mdc:src/basic_checker_hardcoded/detector.py) - LLM-based distraction detection
- **Hash Change Detection**: [src/basic_checker_hardcoded/hash_change_detection.py](mdc:src/basic_checker_hardcoded/hash_change_detection.py) - Optimizes API calls
- **Voice Alerts**: [src/basic_checker_hardcoded/speaker.py](mdc:src/basic_checker_hardcoded/speaker.py) - macOS voice notifications

## Configuration System

The system uses a comprehensive config object with these key settings:

### Screenshot Settings
- `SCREENSHOT_RESOLUTION`: "full", "half", "quarter"
- `SCREENSHOT_COMPRESS`: Enable/disable compression
- `SCREENSHOT_COMPRESSION_METHOD`: "binarize", "bilateral"

### Analysis Settings
- `LLM_ANALYZE_COMPRESSED`: Use compressed images for LLM analysis
- `HASH_DETECTION_ON_COMPRESSED`: Use compressed images for hash detection

### Storage Settings
- `KEEP_ORIGINAL_AFTER_COMPRESSION`: Keep both files vs delete original
- `CLEANUP_TEMP_FILES`: Clean up temporary files

### Performance Settings
- `MONITORING_INTERVAL_SECONDS`: Monitoring frequency
- `HASH_CHANGE_THRESHOLD`: Change detection sensitivity

## Development Patterns

### Config-Driven Development
- All system behavior controlled by [CONFIG](mdc:src/basic_checker_hardcoded/main.py) object
- Easy toggles for different optimization scenarios
- No hardcoded behavior - everything configurable

### File Management
- Original and compressed files handled separately
- Config determines which file to use for analysis
- Automatic cleanup based on config settings

### Testing
- [tests/basic_checker_hardcoded/test_config.py](mdc:tests/basic_checker_hardcoded/test_config.py) - Config system tests
- [tests/basic_checker_hardcoded/test_compression_integration.py](mdc:tests/basic_checker_hardcoded/test_compression_integration.py) - Compression behavior tests

## Cleanup Commands

Use the justfile command for cleanup:
```bash
just clean-feisty-screenshots
```

This removes monitoring screenshots while preserving:
- Test data in `tests/basic_checker_hardcoded/test_data/`
- Log files in `src/basic_checker_hardcoded/data/screenshots/logs/`
- Any files in `dont-delete/` directories

## Optimization Scenarios

### Space-Saving Mode (96% storage reduction)
```python
CONFIG.KEEP_ORIGINAL_AFTER_COMPRESSION = False
CONFIG.LLM_ANALYZE_COMPRESSED = True
CONFIG.HASH_DETECTION_ON_COMPRESSED = True
```

### Performance Mode
```python
CONFIG.MONITORING_INTERVAL_SECONDS = 2.0
CONFIG.HASH_CHANGE_THRESHOLD = 0.2
CONFIG.VOICE_ALERTS_ENABLED = False
```

### Current Behavior (Default)
- Keeps both original and compressed files
- Uses original images for analysis
- Maintains current functionality