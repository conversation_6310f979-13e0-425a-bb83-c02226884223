---
globs: **/hash_change_detection.py,**/test_hash_change_detection.py
description: Hash change detection optimization patterns
---

# Hash Change Detection System

## Purpose

The hash change detection system optimizes the coding feisty checker by reducing unnecessary LLM API calls when screenshots haven't changed significantly.

## Core Functions

### calculate_perceptual_hash(image_path: str) -> str
- Uses dhash algorithm for perceptual hashing
- Returns hex string representation
- Fast operation (~0.1s for typical screenshots)

### has_significant_change(current_hash: str, previous_hash: str, threshold: int = 4) -> bool
- Compares hashes using Hamming distance
- Default threshold: 4 bits (~10% change)
- Returns True if change exceeds threshold

### get_change_percentage(current_hash: str, previous_hash: str) -> float
- Returns approximate percentage change (0-100)
- Useful for debugging and optimization tuning

## Integration Pattern

```python
# In monitoring loop
current_hash = calculate_perceptual_hash(compressed_path)

if last_hash is None or has_significant_change(current_hash, last_hash):
    # Run expensive LLM analysis
    analysis = analyze_distraction(compressed_path)
else:
    # Skip analysis - no significant change
    print("⚡ Skipping - no significant change")
```

## Performance Benefits

- **25-80% API call reduction** in typical usage
- **Faster response times** for unchanged screenshots
- **Cost savings** on LLM API usage
- **Maintains accuracy** - only skips when truly unchanged

## Testing

Use [tests/basic_checker_hardcoded/test_hash_change_detection.py](mdc:tests/basic_checker_hardcoded/test_hash_change_detection.py) for:
- Hash calculation accuracy
- Change detection thresholds
- Identical vs different image handling
- Performance benchmarks