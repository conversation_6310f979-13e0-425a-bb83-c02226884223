---
description: Beeper MCP filtering and message handling
---

# Beeper MCP Filtering Rules

## Critical Issues with Beeper MCP Parameters

**BROKEN PARAMETERS:**
- `excludeLowPriority=True` - Does NOT actually filter out low-priority chats
- `includeMuted=False` - Causes search to FAIL completely
- `unreadOnly=True` - Causes search to FAIL when combined with other filters

## Working Filtering Strategy

**Manual filtering is required:**

1. **Filter out archived chats** - Look for "This chat is archived" in chat data
2. **Filter out low-priority group chats** - Use name patterns:
   - burning man, camp, burner, dmt, spinning, pega, mantracorns
   - fox&badge, bad sex, global creative, ataraxia, tribe, llama
   - dancing, housing, sublets, outdoor movie, afrikaburn

3. **Keep important chats:**
   - Single chats (personal conversations)
   - Event planning groups (birthdays, meetings)
   - Work-related conversations

## Expected Results

- **Before filtering:** 2,000+ unread messages across 17+ chats
- **After filtering:** ~4-6 unread messages across 7 important chats
- **Successfully filtered out:** 10+ archived chats and low-priority group chats

## Implementation Notes

When working with <PERSON><PERSON> MCP:
- Always use manual filtering logic
- Test with actual chat data to verify filtering works
- Focus on single chats and event planning groups
- Ignore archived chats and Burning Man/festival group chats