---
alwaysApply: true
---

# Feisty5 Project Structure

## Main Entry Points
- [run.py](mdc:run.py) - Main project entry point
- [justfile](mdc:justfile) - Task runner commands

## Core Modules
- [src/will_detector/](mdc:src/will_detector/) - Will detection and classification system
- [src/notifications/](mdc:src/notifications/) - Mac notification system
- [src/img_compression/](mdc:src/img_compression/) - Image processing utilities
- [src/composio_gmail/](mdc:src/composio_gmail/) - Email automation
- [src/peekaboo_quick/](mdc:src/peekaboo_quick/) - Quick utilities

## Configuration
- [requirements.txt](mdc:requirements.txt) - Python dependencies
- [settings.json](mdc:settings.json) - Project settings
- [.env](mdc:.env) - Environment variables (if exists)

## Development Rules
- Use `uv` instead of `pip` for package management
- Use `zsh` as terminal shell
- All centralized config should live in `.env`
- Use type hints and dataclasses where appropriate
- Follow purposeful brittleness during LLM development (no defaults, no exception handling, no mocking)