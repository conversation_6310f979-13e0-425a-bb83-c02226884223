---
description: MCP (Model Context Protocol) integration patterns
---

# MCP Integration Patterns

## Beeper MCP Usage

### Working Parameters
- `limit`: Controls number of results (1-200, default 50)
- `dateAfter`/`dateBefore`: ISO datetime filtering
- `chatIDs`: Array of specific chat IDs to search
- `sender`: "me", "others", or specific user ID

### Broken Parameters (DO NOT USE)
- `excludeLowPriority=True` - Broken, doesn't filter properly
- `includeMuted=False` - Causes search failures
- `unreadOnly=True` - Causes failures when combined with other filters

### Reliable Search Patterns
```python
# Get all chats (then filter manually)
mcp_beeper_search_chats(limit=50)

# Search messages in specific chats
mcp_beeper_search_messages(
    chatIDs=["41933", "42152"], 
    limit=20,
    dateAfter="2025-09-08T00:00:00Z"
)

# Search recent messages
mcp_beeper_search_messages(
    limit=100,
    dateAfter="2025-09-08T18:00:00Z"
)
```

## MCP Tool Calling Best Practices

1. **Always test parameters** - Many MCP parameters are broken
2. **Use manual filtering** - Don't rely on built-in filters
3. **Batch tool calls** - Use multiple tools in parallel when possible
4. **Handle failures gracefully** - MCP tools can fail unexpectedly
5. **Verify results** - Check that filtering actually worked

## Common MCP Issues

- **Parameter combinations fail** - Test each parameter individually
- **Search results incomplete** - May need multiple searches with different parameters
- **Chat access limitations** - Some high-volume chats may be inaccessible
- **Output formatting varies** - Parse results carefully, don't assume format