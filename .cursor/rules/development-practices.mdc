---
globs: *.py,*.md,*.sh
---

# Development Practices

## Core Development Loop
1. **WRITE CODE** - Implement functionality
2. **REFLECT** - Analyze the implementation
3. **FIX IF REQUIRED** - Debug and improve
4. **RUN SCRIPT** - Test and verify output meets expectations
5. **DEBUG/FIX LOOP** - Iterate until requirements are met
6. **RESTART** - Begin again if task requirements not met

## Code Quality Standards
- **PURPOSEFUL BRITTLENESS** - No defaults, no exception handling, no mocking APIs, no fallbacks
- **LEAST CODE** - Most elegant, DRY, clearly named functions
- **HELPER FUNCTIONS** - Use `_helper` functions with clear `#-----` divisions
- **STATIC CLASSES** - Use with <6 char names for in-script grouping
- **ALWAYS KEEP GOING** - Full-auto pursuit of task completion until working as expected

## Package Management
- Use `uv add`, `uv sync`, `uv run` instead of `pip`
- Use `zsh` as terminal shell
- All centralized config/params in `.env` using `os.getenv` or `_get_*` functions

## File Organization
- No temporary 'check files' in root - use `./.tmp` with `.tmp_*` prefix
- Use absolute paths over relative paths when possible
- Use type hints and in-script dataclasses where appropriate

## Logging Pattern
```python
# Add to src/name/config.py
from loguru import logger as log

@dataclass
class Config:
    LOG_LEVEL: str = "INFO"  # or "DEBUG" for quick toggle
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    
    def log_init(self):
        log.remove()  # Remove default handler
        log.add(sys.stderr, format=self.LOG_FORMAT, level=self.LOG_LEVEL)

# Add to main.py at top:
CONFIG.log_init()
```