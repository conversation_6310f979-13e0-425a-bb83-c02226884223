---
globs: **/detector.py,**/test_integration.py
description: LLM-based distraction detection patterns and best practices
---

# LLM Distraction Detection

## Model Configuration

- **Model**: `gpt-5-nano` (set via `SS_MAIN_MODEL` environment variable)
- **Token Limit**: 2000 completion tokens (prevents timeout issues)
- **Response Format**: Structured JSON with schema enforcement

## Detection Logic

The system analyzes screenshots to determine if the user is:
- **Focused**: Working on coding/development tasks related to "feisty"
- **Distracted**: Using social media, entertainment, or unrelated applications

## Prompt Strategy

The detection prompt specifically looks for:
1. **Evidence of coding work** - IDEs, terminals, code files
2. **Feisty project references** - "feisty" in filenames, project names
3. **Distraction indicators** - Social media, entertainment apps
4. **Context awareness** - Task completion dialogs, development tools

## Response Format

```json
{
  "distracted_decision": true/false,
  "reasoning": "Detailed explanation of the decision"
}
```

## Common Scenarios

### Focused (distracted_decision: false)
- IDE/editor with code files
- Terminal/command line
- Task completion notifications
- Development documentation
- Project management tools

### Distracted (distracted_decision: true)
- Social media (WhatsApp, Facebook, etc.)
- Entertainment (YouTube, games)
- News/reading unrelated to work
- Personal communication apps

## Performance Considerations

- **Token Management**: Use 2000 completion tokens to prevent timeouts
- **Image Compression**: Screenshots are binarized for faster processing
- **Hash Optimization**: Only analyze when significant changes detected
- **Response Time**: Typical analysis takes 10-20 seconds

## Testing

Use [tests/basic_checker_hardcoded/test_integration.py](mdc:tests/basic_checker_hardcoded/test_integration.py) with real screenshots:
- `distracted_whatsapp_1.png` - Should return `distracted_decision: true`
- `focused_ide_1.png` - Should return `distracted_decision: false`
- `focused_terminal.png` - Should return `distracted_decision: false`