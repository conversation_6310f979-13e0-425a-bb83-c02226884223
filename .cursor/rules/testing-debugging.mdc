---
globs: *.py,test_*.py,*_test.py
---

# Testing and Debugging

## Testing Philosophy
- **NO MOCKING** - Diagnose issues without mocking outputs
- **NO FALLBACKS** - System should work as expected or fail clearly
- **ALWAYS RUN SCRIPTS** - Test immediately after changes to ensure fixes work
- **KEEP GOING** - Continue until requirements are met unless blocked by impossible constraints

## Debugging Workflow
1. **Run the script** - Check logs and output meet expectations
2. **Debug/fix loop** - Iterate until working
3. **Restart at (1)** - If task requirements not met
4. **Check for API keys** - Usually in `.env` file

## Common Issues and Solutions

### Import Issues
- **Fix via pyproject.toml** - Don't use bash workarounds or sys.path fixes
- **Check directory structure** - Ensure proper module organization
- **Use proper import methods** - Follow Python packaging standards

### Terminal Output Issues
- **Check shell configuration** - May need different terminal approach
- **Use explicit output** - Add print statements for debugging
- **Test with simple commands** - Verify basic functionality first

### MCP Tool Issues
- **Test parameters individually** - Many combinations fail
- **Use manual filtering** - Built-in filters often broken
- **Handle failures gracefully** - Tools can fail unexpectedly

## Refactoring Guidelines
- **After completion** - Think through how to refactor the script
- **Suggest improvements** - Ask for feedback on refactoring approach
- **Minimize code** - Focus on elegant, DRY solutions
- **Clear naming** - Use descriptive function and variable names