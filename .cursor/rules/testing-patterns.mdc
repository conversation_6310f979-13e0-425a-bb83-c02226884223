---
globs: tests/**/*.py,**/test_*.py
description: Testing patterns and best practices for the coding feisty checker
---

# Testing Patterns

## Test Structure

### Unit Tests
- **[tests/basic_checker_hardcoded/test_hash_change_detection.py](mdc:tests/basic_checker_hardcoded/test_hash_change_detection.py)** - Hash detection algorithms
- **[tests/basic_checker_hardcoded/test_timer.py](mdc:tests/basic_checker_hardcoded/test_timer.py)** - Timer precision and intervals

### Integration Tests
- **[tests/basic_checker_hardcoded/test_integration.py](mdc:tests/basic_checker_hardcoded/test_integration.py)** - End-to-end functionality with real screenshots

## Test Data

Real screenshots in [tests/basic_checker_hardcoded/test_data/](mdc:tests/basic_checker_hardcoded/test_data/):
- `distracted_whatsapp_1.png` - WhatsApp interface (should be detected as distracted)
- `distracted_whatsapp_2.png` - WhatsApp interface (should be detected as distracted)
- `focused_ide_1.png` - IDE workspace (should be detected as focused)
- `focused_ide_2.png` - IDE workspace (should be detected as focused)
- `focused_task_complete.png` - Task completion dialog (should be detected as focused)
- `focused_terminal.png` - Terminal workspace (should be detected as focused)

## Testing Best Practices

### Performance Testing
- Time individual LLM calls (typically 10-20 seconds)
- Measure hash calculation speed (~0.1 seconds)
- Test timer precision (should maintain exact intervals)

### Mock vs Real Data
- Use real screenshots for integration tests
- Use generated images for unit tests
- Test edge cases (empty responses, API failures)

### Assertion Patterns
```python
# Test distraction detection
assert analysis.get("distracted_decision") is True
assert "reasoning" in analysis
assert len(analysis["reasoning"]) > 10

# Test hash change detection
assert has_change is True
assert change_pct > 10.0

# Test timer precision
assert 0.95 <= elapsed <= 1.05
```

## Running Tests

```bash
# Run all tests
uv run pytest tests/basic_checker_hardcoded/ -v

# Run specific test file
uv run pytest tests/basic_checker_hardcoded/test_integration.py -v

# Run with timing
time uv run pytest tests/basic_checker_hardcoded/ -v
```

## Test Coverage

Current coverage includes:
- ✅ Hash change detection (4 tests)
- ✅ Timer precision (4 tests)  
- ✅ Integration with real screenshots (6 tests)
- ✅ End-to-end monitoring simulation
- ✅ API call optimization verification