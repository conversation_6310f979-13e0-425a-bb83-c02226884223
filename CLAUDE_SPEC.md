# CLAUDE SPEC

NOTICE: **READ-ONLY LLM'S MAY UNDER NO CIRCUMSTANCE EDIT THIS FILE THEMSELVES.**

## GOAL1: 
Simple script that takes regular screen shots and asks gpt-5 or claude code whether I have just recieved a message from <PERSON>. It logs detectios and avoids duplication.

- For 1 use src/1_will_detector/ as app folder.


## Scripts:
/1/ SCREENSHOT Simplest command for detecting which of my mac screens is the active one (user is using this screen/window) and which application is focused.
- I want to take a screenshot of ONLY the application the user (me) is using.
- Save to /src/1_will_detector/data/screenshots/YYYYMMDD_HHMMSS.png
/2/ CLASSIFICATION PROMPT -> input = screenshot path. output = json w reasoning, 'will Sighting' boolean true/false, verbatim quote of what was spotted (verified)
/3/ Save 'sightings' to /src/1_will_detector/data/sightings.json


## GOAL2:

Keyboard shortcut that Captures Screenshot of active screen + a prompt. LLM system /claude code updates Will on Email based on the screenshot (ss).
- <EMAIL>, cc <EMAIL>

# NIELS MILESTONES!

# Focus Guardian - Build Checkpoints

### Checkpoint 1: Basic Screen Capture
**Requirement:** Python script that captures a screenshot and saves it locally

### Checkpoint 2: Basic Notification System
**Requirement:** Script can trigger a native Mac notification with a test message

### Checkpoint 3: Simple LLM Test
**Requirement:** LLM can distinguish between two screenshots (Will Hayes conversation vs not) and trigger a Mac notification with the result

### Checkpoint 4: Todo File Integration  
**Requirement:** Script reads a `todos.txt` file, identifies the top task (first non-empty line), and displays it in a Mac notification

### Checkpoint 5: Todo Assessment
**Requirement:** LLM assesses whether a screenshot shows work related to the current todo item and triggers a Mac notification with the result

### Checkpoint 6: Escalating Notifications
**Requirement:** Progressive notifications based on off-task time (1 min gentle, 2 min stern, 3 min urgent)

### Checkpoint 7: The Nuclear Option
**Requirement:** After 4 minutes off-task, show 10-second countdown then close the active window

### Checkpoint 8: Configuration & UI
**Requirement:** Simple GUI with start/stop button, current task display, and snooze option

### Checkpoint 9: Prompt Optimization
**Requirement:** Use the tool for an hour and iterate on the LLM prompt to improve accuracy

### Checkpoint 10: Add Metadata to Screen Capture
**Requirement** screen capture also gets core info like window name, tab name, tab browser url, browser tab domain