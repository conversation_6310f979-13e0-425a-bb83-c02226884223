#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, "src")


def test_basic_checker():
    try:
        print("🧪 Testing Basic Checker Tasklist System")
        print("=" * 50)

        # Test imports
        print("1. Testing imports...")
        from basic_checker_tasklist.detector import analyze_distraction, get_top_task
        from basic_checker_tasklist.speaker import speak
        from will_detector.screenshot import capture_active_window_screenshot

        print("   ✅ All imports successful")

        # Test environment
        print("2. Testing environment...")
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key:
            print(f"   ✅ OpenAI API key found (ends with: ...{api_key[-10:]})")
        else:
            print("   ❌ OpenAI API key not found")
            return False

        # Test top task detection
        print("3. Testing top task detection...")
        top_task = get_top_task()
        print(f"   ✅ Top task: '{top_task}'")

        # Test screenshot capture
        print("4. Testing screenshot capture...")
        data_dir = Path("src/basic_checker_tasklist/data/screenshots")
        data_dir.mkdir(parents=True, exist_ok=True)

        screenshot_data = capture_active_window_screenshot(
            output_dir=data_dir, compress=True
        )

        if screenshot_data and "path" in screenshot_data:
            print(f"   ✅ Screenshot captured: {screenshot_data['path']}")
            if "compressed_path" in screenshot_data:
                print(f"   ✅ Compressed version: {screenshot_data['compressed_path']}")

                # Test LLM analysis
                print("5. Testing LLM analysis...")
                analysis = analyze_distraction(screenshot_data["compressed_path"])
                print("   ✅ Analysis complete:")
                print(
                    f"      Distracted: {analysis.get('distracted_decision', 'unknown')}"
                )
                print(
                    f"      Reasoning: {analysis.get('reasoning', 'No reasoning')[:100]}..."
                )

                # Test voice (if distracted)
                if analysis.get("distracted_decision", False):
                    print("6. Testing voice alert...")
                    speak("Test voice alert - you seem distracted!", rate=173)
                    print("   ✅ Voice alert played")
                else:
                    print("6. Skipping voice test (not distracted)")

                return True
            else:
                print("   ❌ No compressed screenshot found")
                return False
        else:
            print("   ❌ Screenshot capture failed")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_basic_checker()
    print("=" * 50)
    print(f"🎯 Test {'PASSED' if success else 'FAILED'}")
