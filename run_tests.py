#!/usr/bin/env python3
"""Test runner for things3_import tests"""

import sys
import traceback

def run_test_suite():
    print("🧪 Running Things3 Import Tests")
    print("="*50)
    
    try:
        print("\n📋 Basic functionality tests:")
        sys.path.insert(0, 'tests')
        from test_things3_import import test_returns_top_3_tasks, test_pretty_print_format, test_correct_json_structure
        
        test_returns_top_3_tasks()
        test_pretty_print_format() 
        test_correct_json_structure()
        print("✅ Basic tests passed!")
        
    except Exception as e:
        print(f"❌ Basic tests failed: {e}")
        traceback.print_exc()
        
    try:
        print("\n⚙️  Parameter functionality tests:")
        from test_things3_import_params import test_default_n_parameter, test_custom_n_parameter, test_top_task_only_flag, test_top_task_only_overrides_n
        
        test_default_n_parameter()
        test_custom_n_parameter()
        test_top_task_only_flag()
        test_top_task_only_overrides_n()
        print("✅ Parameter tests passed!")
        
    except Exception as e:
        print(f"❌ Parameter tests failed: {e}")
        traceback.print_exc()
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    run_test_suite()