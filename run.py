#!/usr/bin/env python3
import sys
import time
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

from src.will_detector.screenshot import capture_active_window_screenshot
from src.will_detector.classifier import MessageDetector
from src.will_detector.storage import SightingStorage
from src.will_detector.run_config import CFG


def main():
    print(f"Will Detector Started")
    print(f"Using LLM Provider: {CFG.LLM_PROVIDER}")
    print(f"Screenshot Directory: {CFG.SCREENSHOT_DIR}")
    print(f"Checking every {CFG.SCREENSHOT_INTERVAL} seconds")
    print("-" * 50)
    
    detector = MessageDetector(provider=CFG.LLM_PROVIDER)
    storage = SightingStorage(CFG.SIGHTINGS_FILE)
    
    while True:
        print(f"\n[{time.strftime('%Y-%m-%d %H:%M:%S')}] Taking screenshot...")
        
        screenshot_info = capture_active_window_screenshot(CFG.SCREENSHOT_DIR)
        print(f"  App: {screenshot_info['app_name']}")
        print(f"  Window: {screenshot_info['window_title']}")
        
        print("  Analyzing...")
        detection_result = detector.analyze_screenshot(screenshot_info['path'])
        
        # Can we detect Will in this screenshot? 
        if detection_result.get('willSighting'):
            print(f"  ✓ WILL DETECTED!")
            print(f"    Reasoning: {detection_result['reasoning']}")
            print(f"    Message: {detection_result['verbatimQuote']}")
            
            if storage.add_sighting(screenshot_info, detection_result):
                print("    → Sighting saved (new)")
            else:
                print("    → Duplicate (already logged)")
        else:
            print(f"  No Will message detected")
            print(f"    Reasoning: {detection_result['reasoning']}")
        
        print(f"  Waiting {CFG.SCREENSHOT_INTERVAL} seconds...")
        time.sleep(CFG.SCREENSHOT_INTERVAL)


if __name__ == "__main__":
    main()