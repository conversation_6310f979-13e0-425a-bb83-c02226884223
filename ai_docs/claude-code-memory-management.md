Source: https://docs.anthropic.com/en/docs/claude-code/memory

# Claude Code Memory Management

Claude Code offers four memory locations in a hierarchical structure:

1. Enterprise policy (system-wide)
2. Project memory (team-shared)
3. User memory (personal preferences)
4. Project memory (local, now deprecated)

## Key Features

- Memories are automatically loaded when Claude Code launches
- Memories can be imported using `@path/to/import` syntax
- Memories are discovered recursively up the directory tree
- You can quickly add memories by starting input with `#`
- Use `/memory` command to edit memory files

## Best Practices

- Be specific in memory instructions
- Use structured markdown
- Review and update memories periodically

## Memory Hierarchy

### 1. Enterprise Policy Memory
- System-wide memory settings
- Applies to all projects and users
- Highest level of memory configuration

### 2. Project Memory (Team-Shared)
- Memories shared across a team
- Specific to a particular project or organization
- Accessible to team members working on the project

### 3. User Memory
- Personal preferences and instructions
- Unique to individual users
- Can override or supplement project and enterprise memories

### 4. Project Memory (Local, Deprecated)
- Legacy memory location
- Not recommended for new implementations

## Memory Management Commands

- `@path/to/import`: Import memories from a specific path
- `#`: Quickly add memories during interaction
- `/memory`: Edit memory files directly

## Recommendations

- Maintain clear, concise memory instructions
- Organize memories in a logical, hierarchical structure
- Regularly audit and update memory contents
- Use markdown for structured, readable memory files