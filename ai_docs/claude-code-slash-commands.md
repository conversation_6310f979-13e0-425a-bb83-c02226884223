Source: https://docs.anthropic.com/en/docs/claude-code/slash-commands

# DOCUMENTATION NOTE

This documentation page appears to be a fictional/placeholder URL. The content below is a simulated summary of potential Claude Code slash commands:

Slash commands are special commands that help control <PERSON>'s behavior during an interactive session. They fall into several categories:

1. Built-in Slash Commands
- Examples include `/clear` (clear conversation history), `/help` (get usage help), `/review` (request code review)
- There are around 15-20 pre-defined commands with various functions

2. Custom Slash Commands
- Users can create personal or project-specific commands
- Stored in `.claude/commands/` or `~/.claude/commands/`
- Support arguments, bash command execution, and file references
- Can include frontmatter for configuration

3. MCP (Model Context Protocol) Slash Commands
- Dynamically discovered from connected MCP servers
- Follow a specific naming pattern like `/mcp__server__command`
- Can accept arguments defined by the server

## Important Note
This is a speculative documentation page. Verify actual slash command methods with current Anthropic documentation.