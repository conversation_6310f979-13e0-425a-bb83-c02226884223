Source: https://docs.anthropic.com/en/docs/claude-code/hooks

# Claude Code Hooks Documentation

## Note
The documentation for Claude Code Hooks was not publicly accessible at the time of retrieval. The content above represents a summary based on available information about potential hook configurations.

### Potential Hook Types
- PreToolUse
- PostToolUse
- UserPromptSubmit
- SessionStart

### Example Configuration (Hypothetical)
```json
{
  "hooks": {
    "PreToolUse": [
      {
        "matcher": "Write",
        "hooks": [
          {
            "type": "command",
            "command": "validation-script.sh"
          }
        ]
      }
    ]
  }
}
```

Please refer to the official Anthropic documentation for the most up-to-date and accurate information about Claude Code Hooks.