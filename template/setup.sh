#!/bin/bash
# AnyDesk Bot Setup Script
# Creates virtual environment and installs dependencies

set -e  # Exit on error

echo "Setting up AnyDesk Bot..."

# Create virtual environment using .venv convention
if [ ! -d ".venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv .venv
else
    echo "Virtual environment already exists."
fi

# Activate virtual environment
echo "Activating virtual environment..."
source .venv/bin/activate

# Upgrade pip
echo "Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Install package in development mode
echo "Installing anydeskbot package in development mode..."
pip install -e .

# Create logs directory
echo "Creating logs directory..."
mkdir -p logs

echo "Setup complete!"
echo "To activate the virtual environment, run: source .venv/bin/activate"
echo "To run the bot, use: ./run.sh or just run"