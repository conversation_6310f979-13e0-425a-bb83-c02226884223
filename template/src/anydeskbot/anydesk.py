import subprocess
import json
import time
from pathlib import Path
from typing import Optional, List, Dict
from .config import CFG

class AnyDeskCLI:
    def __init__(self):
        self.anydesk_path = CFG.ANYDESK_PATH
        CFG.validate()
    
    def _run_command(self, args: List[str], input_text: Optional[str] = None) -> tuple[int, str, str]:
        cmd = [self.anydesk_path] + args
        process = subprocess.Popen(
            cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        stdout, stderr = process.communicate(input=input_text)
        return process.returncode, stdout, stderr
    
    def get_id(self) -> Optional[str]:
        returncode, stdout, stderr = self._run_command(['--get-id'])
        if returncode == 0:
            return stdout.strip()
        return None
    
    def get_alias(self) -> Optional[str]:
        returncode, stdout, stderr = self._run_command(['--get-alias'])
        if returncode == 0:
            return stdout.strip()
        return None
    
    def get_status(self) -> Dict[str, any]:
        returncode, stdout, stderr = self._run_command(['--get-status'])
        return {
            'online': returncode == 0,
            'output': stdout.strip(),
            'error': stderr.strip() if stderr else None
        }
    
    def connect(self, remote_id: str, password: Optional[str] = None, 
                fullscreen: bool = False, plain_password: bool = False) -> bool:
        args = [remote_id]
        
        if fullscreen:
            args.append('--fullscreen')
        
        if password:
            if plain_password:
                args.extend(['--plain', f'--password={password}'])
            else:
                args.append(f'--with-password')
        
        returncode, stdout, stderr = self._run_command(args, input_text=password if not plain_password else None)
        return returncode == 0
    
    def disconnect(self, connection_id: Optional[str] = None) -> bool:
        args = ['--disconnect']
        if connection_id:
            args.append(connection_id)
        
        returncode, stdout, stderr = self._run_command(args)
        return returncode == 0
    
    def set_password(self, password: str) -> bool:
        returncode, stdout, stderr = self._run_command(['--set-password'], input_text=password)
        return returncode == 0
    
    def remove_password(self) -> bool:
        returncode, stdout, stderr = self._run_command(['--remove-password'])
        return returncode == 0
    
    def restart_service(self) -> bool:
        returncode, stdout, stderr = self._run_command(['--restart-service'])
        return returncode == 0
    
    def get_version(self) -> Optional[str]:
        returncode, stdout, stderr = self._run_command(['--version'])
        if returncode == 0:
            return stdout.strip()
        return None