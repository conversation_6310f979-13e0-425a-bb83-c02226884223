import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

class Config:
    ANYDESK_PATH = os.getenv('ANYDESK_PATH', '/Applications/AnyDesk.app/Contents/MacOS/AnyDesk')
    DEFAULT_PASSWORD = os.getenv('ANYDESK_PASSWORD', '')
    DEFAULT_TIMEOUT = int(os.getenv('CONNECTION_TIMEOUT', '30'))
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    CONNECTIONS_FILE = Path(os.getenv('CONNECTIONS_FILE', './.claude/connections.json'))
    
    @classmethod
    def validate(cls):
        if not Path(cls.ANYDESK_PATH).exists():
            raise FileNotFoundError(f"AnyDesk not found at {cls.ANYDESK_PATH}")
        
        cls.CONNECTIONS_FILE.parent.mkdir(parents=True, exist_ok=True)
        if not cls.CONNECTIONS_FILE.exists():
            cls.CONNECTIONS_FILE.write_text('[]')

CFG = Config()