import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from .anydesk import AnyDeskCL<PERSON>
from .connections import ConnectionManager
from .config import CFG

console = Console()
anydesk = AnyDeskCLI()
conn_manager = ConnectionManager()

@click.group()
@click.version_option(version='1.0.0', prog_name='anydeskbot')
def cli():
    pass

@cli.command()
def info():
    my_id = anydesk.get_id()
    my_alias = anydesk.get_alias()
    version = anydesk.get_version()
    status = anydesk.get_status()
    
    table = Table(title="AnyDesk Information")
    table.add_column("Property", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("AnyDesk ID", my_id or "Not available")
    table.add_row("Alias", my_alias or "Not set")
    table.add_row("Version", version or "Unknown")
    table.add_row("Status", "Online" if status['online'] else "Offline")
    table.add_row("AnyDesk Path", CFG.ANYDESK_PATH)
    
    console.print(table)

@cli.command()
@click.argument('remote_id')
@click.option('--password', '-p', help='Password for the connection')
@click.option('--fullscreen', '-f', is_flag=True, help='Connect in fullscreen mode')
@click.option('--save', '-s', help='Save connection with a name')
def connect(remote_id, password, fullscreen, save):
    if save:
        conn_manager.add_connection(save, remote_id, password)
        console.print(f"[green]Connection saved as '{save}'[/green]")
    
    console.print(f"[yellow]Connecting to {remote_id}...[/yellow]")
    
    if anydesk.connect(remote_id, password or CFG.DEFAULT_PASSWORD, fullscreen):
        console.print(f"[green]Successfully connected to {remote_id}[/green]")
        if save:
            conn_manager.update_last_connected(save)
    else:
        console.print(f"[red]Failed to connect to {remote_id}[/red]")

@cli.command()
@click.argument('name')
def quick(name):
    conn = conn_manager.get_connection(name)
    if not conn:
        console.print(f"[red]Connection '{name}' not found[/red]")
        return
    
    console.print(f"[yellow]Connecting to {conn['name']} ({conn['remote_id']})...[/yellow]")
    
    if anydesk.connect(conn['remote_id'], conn.get('password') or CFG.DEFAULT_PASSWORD):
        console.print(f"[green]Successfully connected to {conn['name']}[/green]")
        conn_manager.update_last_connected(name)
    else:
        console.print(f"[red]Failed to connect to {conn['name']}[/red]")

@cli.command()
def disconnect():
    if anydesk.disconnect():
        console.print("[green]Disconnected successfully[/green]")
    else:
        console.print("[red]Failed to disconnect[/red]")

@cli.group()
def saved():
    pass

@saved.command('list')
def list_saved():
    connections = conn_manager.list_connections()
    
    if not connections:
        console.print("[yellow]No saved connections[/yellow]")
        return
    
    table = Table(title="Saved Connections")
    table.add_column("Name", style="cyan")
    table.add_column("Remote ID", style="green")
    table.add_column("Description", style="white")
    table.add_column("Last Connected", style="yellow")
    
    for conn in connections:
        table.add_row(
            conn['name'],
            conn['remote_id'],
            conn.get('description', '-'),
            conn.get('last_connected', 'Never') if conn.get('last_connected') else 'Never'
        )
    
    console.print(table)

@saved.command('add')
@click.argument('name')
@click.argument('remote_id')
@click.option('--password', '-p', help='Password for the connection')
@click.option('--description', '-d', help='Description of the connection')
def add_saved(name, remote_id, password, description):
    conn_manager.add_connection(name, remote_id, password, description)
    console.print(f"[green]Connection '{name}' added successfully[/green]")

@saved.command('remove')
@click.argument('name')
def remove_saved(name):
    if conn_manager.remove_connection(name):
        console.print(f"[green]Connection '{name}' removed successfully[/green]")
    else:
        console.print(f"[red]Connection '{name}' not found[/red]")

@cli.group()
def config():
    pass

@config.command('set-password')
@click.argument('password')
def set_password(password):
    if anydesk.set_password(password):
        console.print("[green]Password set successfully[/green]")
    else:
        console.print("[red]Failed to set password[/red]")

@config.command('remove-password')
def remove_password():
    if anydesk.remove_password():
        console.print("[green]Password removed successfully[/green]")
    else:
        console.print("[red]Failed to remove password[/red]")

@config.command('restart')
def restart():
    if anydesk.restart_service():
        console.print("[green]AnyDesk service restarted successfully[/green]")
    else:
        console.print("[red]Failed to restart AnyDesk service[/red]")

if __name__ == '__main__':
    cli()