import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
from .config import CFG

class ConnectionManager:
    def __init__(self):
        self.connections_file = CFG.CONNECTIONS_FILE
        self.connections = self._load_connections()
    
    def _load_connections(self) -> List[Dict]:
        if self.connections_file.exists():
            with open(self.connections_file, 'r') as f:
                return json.load(f)
        return []
    
    def _save_connections(self):
        with open(self.connections_file, 'w') as f:
            json.dump(self.connections, f, indent=2, default=str)
    
    def add_connection(self, name: str, remote_id: str, password: Optional[str] = None, 
                      description: Optional[str] = None) -> Dict:
        connection = {
            'name': name,
            'remote_id': remote_id,
            'password': password,
            'description': description,
            'created_at': datetime.now().isoformat(),
            'last_connected': None
        }
        self.connections.append(connection)
        self._save_connections()
        return connection
    
    def remove_connection(self, name: str) -> bool:
        original_len = len(self.connections)
        self.connections = [c for c in self.connections if c['name'] != name]
        if len(self.connections) < original_len:
            self._save_connections()
            return True
        return False
    
    def get_connection(self, name: str) -> Optional[Dict]:
        for conn in self.connections:
            if conn['name'] == name:
                return conn
        return None
    
    def list_connections(self) -> List[Dict]:
        return self.connections
    
    def update_last_connected(self, name: str):
        for conn in self.connections:
            if conn['name'] == name:
                conn['last_connected'] = datetime.now().isoformat()
                self._save_connections()
                break