# AnyDesk Bot - Minimalistic cheatsheet for running scripts, main app, tests, setup etc.

# Set up virtual environment and install dependencies
setup:
    ./setup.sh

# Run the main application
run *args:
    ./run.sh {{args}}

# Run tests
test:
    source .venv/bin/activate && python -m pytest tests/ -v

# Run tests with coverage
test-cov:
    source .venv/bin/activate && python -m pytest tests/ --cov=anydeskbot --cov-report=html

# Format code with black
format:
    source .venv/bin/activate && black src/ tests/

# Check code style
lint:
    source .venv/bin/activate && flake8 src/ tests/

# Type checking
typecheck:
    source .venv/bin/activate && mypy src/

# Clean up build artifacts
clean:
    rm -rf build/ dist/ *.egg-info/ .pytest_cache/ htmlcov/ .coverage
    find . -type d -name __pycache__ -exec rm -rf {} +
    find . -type f -name "*.pyc" -delete

# Install package in development mode
install:
    source .venv/bin/activate && pip install -e .

# Show help
help:
    @just --list