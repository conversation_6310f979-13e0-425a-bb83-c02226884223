import pytest
import json
from pathlib import Path
from unittest.mock import Mock, patch
from src.anydeskbot.connections import ConnectionManager

@patch('src.anydeskbot.connections.CFG')
def test_connection_manager_initialization(mock_cfg, tmp_path):
    connections_file = tmp_path / 'connections.json'
    connections_file.write_text('[]')
    mock_cfg.CONNECTIONS_FILE = connections_file
    
    manager = ConnectionManager()
    assert manager.connections == []
    print("✓ if ConnectionManager initializes with empty list then working")

@patch('src.anydeskbot.connections.CFG')
def test_add_connection(mock_cfg, tmp_path):
    connections_file = tmp_path / 'connections.json'
    connections_file.write_text('[]')
    mock_cfg.CONNECTIONS_FILE = connections_file
    
    manager = ConnectionManager()
    conn = manager.add_connection('test', '123456', 'pass123', 'Test PC')
    
    assert conn['name'] == 'test'
    assert conn['remote_id'] == '123456'
    assert conn['password'] == 'pass123'
    assert conn['description'] == 'Test PC'
    assert len(manager.connections) == 1
    print("✓ if add_connection adds new connection then working")

@patch('src.anydeskbot.connections.CFG')
def test_remove_connection(mock_cfg, tmp_path):
    connections_file = tmp_path / 'connections.json'
    connections_file.write_text('[]')
    mock_cfg.CONNECTIONS_FILE = connections_file
    
    manager = ConnectionManager()
    manager.add_connection('test', '123456')
    result = manager.remove_connection('test')
    
    assert result is True
    assert len(manager.connections) == 0
    print("✓ if remove_connection removes existing connection then working")

@patch('src.anydeskbot.connections.CFG')
def test_get_connection(mock_cfg, tmp_path):
    connections_file = tmp_path / 'connections.json'
    connections_file.write_text('[]')
    mock_cfg.CONNECTIONS_FILE = connections_file
    
    manager = ConnectionManager()
    manager.add_connection('test', '123456', 'pass123')
    conn = manager.get_connection('test')
    
    assert conn is not None
    assert conn['name'] == 'test'
    assert conn['remote_id'] == '123456'
    print("✓ if get_connection retrieves existing connection then working")