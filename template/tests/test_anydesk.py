import pytest
from unittest.mock import Mock, patch
from src.anydeskbot.anydesk import AnyDeskCL<PERSON>

@patch('src.anydeskbot.anydesk.CFG')
def test_anydesk_cli_initialization(mock_cfg):
    mock_cfg.ANYDESK_PATH = '/fake/path'
    mock_cfg.validate = Mock()
    
    cli = AnyDeskCLI()
    assert cli.anydesk_path == '/fake/path'
    mock_cfg.validate.assert_called_once()
    print("✓ if AnyDeskCLI initializes with config then working")

@patch('src.anydeskbot.anydesk.subprocess.Popen')
@patch('src.anydeskbot.anydesk.CFG')
def test_get_id(mock_cfg, mock_popen):
    mock_cfg.ANYDESK_PATH = '/fake/path'
    mock_cfg.validate = Mock()
    
    mock_process = Mock()
    mock_process.communicate.return_value = ('1234567890\n', '')
    mock_process.returncode = 0
    mock_popen.return_value = mock_process
    
    cli = AnyDeskCLI()
    result = cli.get_id()
    
    assert result == '1234567890'
    print("✓ if get_id returns correct ID then working")

@patch('src.anydeskbot.anydesk.subprocess.Popen')
@patch('src.anydeskbot.anydesk.CFG')
def test_get_version(mock_cfg, mock_popen):
    mock_cfg.ANYDESK_PATH = '/fake/path'
    mock_cfg.validate = Mock()
    
    mock_process = Mock()
    mock_process.communicate.return_value = ('9.1.1\n', '')
    mock_process.returncode = 0
    mock_popen.return_value = mock_process
    
    cli = AnyDeskCLI()
    result = cli.get_version()
    
    assert result == '9.1.1'
    print("✓ if get_version returns version string then working")

@patch('src.anydeskbot.anydesk.subprocess.Popen')
@patch('src.anydeskbot.anydesk.CFG')
def test_connect(mock_cfg, mock_popen):
    mock_cfg.ANYDESK_PATH = '/fake/path'
    mock_cfg.validate = Mock()
    
    mock_process = Mock()
    mock_process.communicate.return_value = ('', '')
    mock_process.returncode = 0
    mock_popen.return_value = mock_process
    
    cli = AnyDeskCLI()
    result = cli.connect('123456789', password='test123')
    
    assert result is True
    print("✓ if connect returns True on success then working")