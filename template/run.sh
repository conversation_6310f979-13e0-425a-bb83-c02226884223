#!/bin/bash
# AnyDesk Bot Run Script
# Standard template with shebang, error handling, and executable permissions

set -e  # Exit on error

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Virtual environment not found. Please run ./setup.sh first."
    exit 1
fi

# Activate virtual environment
source .venv/bin/activate

# Check if package is installed
if ! python -c "import anydeskbot" 2>/dev/null; then
    echo "AnyDesk Bot package not installed. Please run ./setup.sh first."
    exit 1
fi

# Run the application
echo "Starting AnyDesk Bot..."
python -m anydeskbot.cli "$@"