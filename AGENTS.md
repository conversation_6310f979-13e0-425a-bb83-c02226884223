# AGENTS.md

This file provides guidance to agents when working with code in this repository.

## Non-Obvious Requirements

- Virtual environment MUST be `.venv` (not `venv`)
- Models are hardcoded: `gpt-5` (OpenAI), `sonnet-4-latest` (Anthropic), `gpt-5-nano` (distraction detection)
- Override distraction model with env var `SS_MAIN_MODEL` 
- Never use `max_tokens` parameter in LLM API calls (deliberately set to None)
- CFG singleton pattern in `run_config.py` for global config
- `@inv` is shorthand for `@invisible.email` domain

## Critical Patterns

- Screenshot compression is mandatory - always use `compress=True`
- Hash-based change detection in `basic_checker_hardcoded` to skip redundant API calls
- Manual .env parsing in some modules to avoid dependencies (see detector.py:12-18)
- Voice alerts use rate=173 (120% of default 144 WPM)
- Voice backend toggleable: macOS say (default) or OpenAI TTS (nova voice)
- Toggle with: `just voice-toggle` or set `VOICE_BACKEND` env var

## Testing

- Run single component: `python -m src.module_name`
- justfile commands test individual modules, not full suites